stages:
  - clean
  - deploy

clean:
  stage: clean
  tags:
    - frontend
  script: env JAVA_HOME=/usr/java/jdk-21 mvn clean
  rules:
    - if: $CI_COMMIT_BRANCH == "promox-5"

deploy:
  stage: deploy
  tags:
    - frontend
  script:
    - env JAVA_HOME=/usr/java/jdk-21 mvn clean
    - env JAVA_HOME=/usr/java/jdk-21 mvn -U package  -DskipTests
    - pwd
    - rsync -av --progress --delete --rsh='sshpass -p 4n8c8f5t ssh -o StrictHostKeyChecking=no -p 7108' --exclude=classes/config.json --include=classes --include=lib --include=classes/** --include=lib/** --exclude=* target/ root@************:/opt/ma-transaction-backend/
    - sshpass -p 4n8c8f5t ssh -o StrictHostKeyChecking=no root@************ -p 7108 -X '/opt/setper && systemctl restart ma-transaction-backend'
  rules:
    - if: $CI_COMMIT_BRANCH == "promox-5"
