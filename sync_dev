env JAVA_HOME=/usr/java/jdk-21 mvn clean
env JAVA_HOME=/usr/java/jdk-21 mvn -U package -DskipTests

rsync -av --progress --delete --delete-excluded --rsh='ssh -p7109' \
  --include=classes \
  --include=lib \
  --include=classes/** \
  --include=lib/* \
  --exclude=* \
  target/ root@************:/root/tmp_deploy/ma-transaction-backend/

ssh root@************ -p 7109 -X 'meld /root/tmp_deploy/ma-transaction-backend/ /opt/ma-transaction-backend/ && /opt/ma-transaction-backend/setper'