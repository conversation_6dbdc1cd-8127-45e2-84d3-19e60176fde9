2025-07-04 09:00:00.000 [transaction-report-job-1] INFO  v.o.t.job.TransactionReportJob - ⚙️ Running TransactionReportJob...
2025-07-04 09:00:00.023 [transaction-report-job-1] INFO  v.o.t.s.impl.TransactionReportImpl - Starting insertDailyTransactionReport from 2025-07-02T00:00 to 2025-07-02T23:59:59.999999999
2025-07-04 09:00:00.055 [boundedElastic-1] INFO  v.o.t.s.impl.TransactionReportImpl - Sending OpenSearch request
2025-07-04 09:00:00.073 [boundedElastic-1] INFO  v.o.t.client.OpenSearchClient - OPENSEARCH EXECUTE QUERY:{
    "size": 0,
    "query": {
        "bool": {
            "filter": [
                {
                    "range": {
                        "enrich_txn.d_create": {
                            "gte": "2025-07-02T00:00:00",
                            "lt": "2025-07-02T23:59:59.999999999",
                            "time_zone": "+07:00"
                        }
                    }
                },
                {
                    "wildcard": {
                        "enrich_txn.s_state.keyword": "Successful"
                    }
                }
            ],
            "must_not": [
                {
                    "term": {
                        "b_has_payment": true
                    }
                },
                {
                    "term": {
                        "enrich_txn.s_base_txn_type.keyword": "invoice"
                    }
                },
                {
                    "terms": {
                        "enrich_txn.s_txn_type.keyword": [
                            "Refund Request",
                            "Request Refund",
                            ""
                        ]
                    }
                }
            ]
        }
    },
    "aggs": {
        "byComposite": {
            "composite": {
                "size": 10000,
                "sources": [
                    {
                        "transactionDay": {
                            "date_histogram": {
                                "field": "enrich_txn.d_create",
                                "calendar_interval": "day",
                                "time_zone": "+07:00"
                            }
                        }
                    },
                    {
                        "merchantId": {
                            "terms": {
                                "field": "msp_merchant.s_id.keyword"
                            }
                        }
                    },
                    {
                        "currency": {
                            "terms": {
                                "field": "msp_payment.s_currency.keyword"
                            }
                        }
                    },
                    {
                        "txnType": {
                            "terms": {
                                "field": "enrich_txn.s_txn_type.keyword"
                            }
                        }
                    },
                    {
                        "paymentMethod": {
                            "terms": {
                                "field": "msp_payment.s_e_pay_method.keyword"
                            }
                        }
                    },
                    {
                        "orderSource": {
                            "terms": {
                                "field": "msp_invoice.s_e_order_source.keyword"
                            }
                        }
                    },
                    {
                        "isPromotion": {
                            "terms": {
                                "script": {
                                    "source": "def channel = doc['msp_payment.s_e_channel.keyword'].size() == 0 ? null : doc['msp_payment.s_e_channel.keyword'].value;\nreturn channel == 'Promotion';",
                                    "lang": "painless"
                                }
                            }
                        }
                    }
                ]
            },
            "aggs": {
                "totalVolume": {
                    "sum": {
                        "field": "enrich_txn.n_amount"
                    }
                }
            }
        }
    }
}
2025-07-04 09:00:00.192 [boundedElastic-1] INFO  v.o.t.s.impl.TransactionReportImpl - Received response from OpenSearch
2025-07-04 09:00:00.211 [boundedElastic-1] INFO  v.o.t.s.impl.TransactionReportImpl - Converted OpenSearch response into 0 report entities
2025-07-04 09:00:00.212 [boundedElastic-1] INFO  v.o.t.s.impl.TransactionReportImpl - Fetched 0 reports from the first OpenSearch request
2025-07-04 09:00:00.212 [boundedElastic-1] INFO  v.o.t.s.impl.TransactionReportImpl - No reports to insert into database
2025-07-04 09:00:00.212 [boundedElastic-1] INFO  v.o.t.s.impl.TransactionReportImpl - Less than 10000 reports fetched (0). Assuming last page, stopping recursion.
2025-07-04 09:05:00.000 [Promotion-report-job-1] INFO  v.o.t.job.PromotionReportJob - ⚙️ Running PromotionReportJob...
2025-07-04 09:05:00.001 [Promotion-report-job-1] INFO  v.o.t.s.impl.PromotionServiceImpl - Starting insertDailyPromotionReport from 2025-07-02T00:00 to 2025-07-02T23:59:59.999999999
2025-07-04 09:05:00.004 [boundedElastic-2] INFO  v.o.t.s.impl.PromotionServiceImpl - Sending OpenSearch request
2025-07-04 09:05:00.005 [boundedElastic-2] INFO  v.o.t.client.OpenSearchClient - OPENSEARCH EXECUTE QUERY:{
    "size": 0,
    "query": {
        "bool": {
            "filter": [
                {
                    "range": {
                        "enrich_txn.d_create": {
                            "gte": "2025-07-02T00:00:00",
                            "lt": "2025-07-02T23:59:59.999999999",
                            "time_zone": "+07:00"
                        }
                    }
                },
                {
                    "terms": {
                        "msp_payment.s_e_channel.keyword": [
                            "Promotion"
                        ]
                    }
                },
                {
                    "wildcard": {
                        "enrich_txn.s_state.keyword": "Successful"
                    }
                }
            ],
            "must_not": [
                {
                    "term": {
                        "b_has_payment": true
                    }
                },
                {
                    "term": {
                        "enrich_txn.s_base_txn_type.keyword": "invoice"
                    }
                },
                {
                    "terms": {
                        "enrich_txn.s_txn_type.keyword": [
                            "Refund Request",
                            "Request Refund",
                            ""
                        ]
                    }
                }
            ]
        }
    },
    "aggs": {
        "byComposite": {
            "composite": {
                "size": 10000,
                "sources": [
                    {
                        "transactionDay": {
                            "date_histogram": {
                                "field": "enrich_txn.d_create",
                                "calendar_interval": "day",
                                "time_zone": "+07:00"
                            }
                        }
                    },
                    {
                        "merchantId": {
                            "terms": {
                                "field": "msp_merchant.s_id.keyword"
                            }
                        }
                    },
                    {
                        "currency": {
                            "terms": {
                                "field": "msp_payment.s_currency.keyword"
                            }
                        }
                    },
                    {
                        "txnType": {
                            "terms": {
                                "field": "enrich_txn.s_txn_type.keyword"
                            }
                        }
                    },
                    {
                        "pr_id": {
                            "terms": {
                                "field": "onepr_pr.s_id.keyword"
                            }
                        }
                    },
                    {
                        "paymentMethod": {
                            "terms": {
                                "field": "msp_payment.s_e_pay_method.keyword"
                            }
                        }
                    },
                    {
                        "payGate": {
                            "terms": {
                                "field": "msp_payment.s_e_gate_label.keyword"
                            }
                        }
                    },
                    {
                        "cardType": {
                            "terms": {
                                "field": "msp_payment.s_e_card.keyword"
                            }
                        }
                    }
                ]
            },
            "aggs": {
                "totalVolume": {
                    "sum": {
                        "field": "msp_invoice.n_amount"
                    }
                },
                "totalPayment": {
                    "sum": {
                        "field": "msp_payment.n_amount"
                    }
                }
            }
        }
    }
}
2025-07-04 09:05:00.016 [boundedElastic-2] INFO  v.o.t.s.impl.PromotionServiceImpl - Received response from OpenSearch
2025-07-04 09:05:00.017 [boundedElastic-2] INFO  v.o.t.s.impl.PromotionServiceImpl - Converted OpenSearch response into 0 report entities
2025-07-04 09:05:00.017 [boundedElastic-2] INFO  v.o.t.s.impl.PromotionServiceImpl - Fetched 0 reports from the first OpenSearch request
2025-07-04 09:05:00.017 [boundedElastic-2] INFO  v.o.t.s.impl.PromotionServiceImpl - No reports to insert into database
2025-07-04 09:05:00.017 [boundedElastic-2] INFO  v.o.t.s.impl.PromotionServiceImpl - No afterKey found. All pages fetched.
2025-07-04 09:10:00.000 [Upos-report-job-1] INFO  v.o.transaction.job.UposReportJob - ⚙️ Running UposReportJob...
2025-07-04 09:10:00.000 [Upos-report-job-1] INFO  v.o.t.service.impl.UposServiceImpl - Starting insertDailyUposReport from 2025-07-02T00:00 to 2025-07-02T23:59:59.999999999
2025-07-04 09:10:00.003 [boundedElastic-3] INFO  v.o.t.service.impl.UposServiceImpl - Sending OpenSearch request
2025-07-04 09:10:00.003 [boundedElastic-3] INFO  v.o.t.client.OpenSearchClient - OPENSEARCH EXECUTE QUERY:{
    "size": 0,
    "query": {
        "bool": {
            "filter": [
                {
                    "range": {
                        "enrich_txn.d_create": {
                            "gte": "2025-07-02T00:00:00",
                            "lt": "2025-07-02T23:59:59.999999999",
                            "time_zone": "+07:00"
                        }
                    }
                },
                {
                    "terms": {
                        "msp_payment.s_e_merchant_channel.keyword": [
                            "UPOS"
                        ]
                    }
                },
                {
                    "wildcard": {
                        "enrich_txn.s_state.keyword": "Successful"
                    }
                }
            ],
            "must_not": [
                {
                    "term": {
                        "b_has_payment": true
                    }
                },
                {
                    "term": {
                        "enrich_txn.s_base_txn_type.keyword": "invoice"
                    }
                },
                {
                    "terms": {
                        "enrich_txn.s_txn_type.keyword": [
                            "Refund Request",
                            "Request Refund",
                            ""
                        ]
                    }
                }
            ]
        }
    },
    "aggs": {
        "byComposite": {
            "composite": {
                "size": 10000,
                "sources": [
                    {
                        "transactionDay": {
                            "date_histogram": {
                                "field": "enrich_txn.d_create",
                                "calendar_interval": "day",
                                "time_zone": "+07:00"
                            }
                        }
                    },
                    {
                        "merchantId": {
                            "terms": {
                                "field": "msp_merchant.s_id.keyword"
                            }
                        }
                    },
                    {
                        "currency": {
                            "terms": {
                                "field": "msp_payment.s_currency.keyword"
                            }
                        }
                    },
                    {
                        "txnType": {
                            "terms": {
                                "field": "enrich_txn.s_txn_type.keyword"
                            }
                        }
                    },
                    {
                        "paymentChannel": {
                            "terms": {
                                "script": {
                                    "source": "def method = doc['msp_payment.s_e_pay_method.keyword'].size() > 0 ? doc['msp_payment.s_e_pay_method.keyword'].value : null; if (method != 'CARD') return method; if (!doc.containsKey('msp_payment.s_ita_id.keyword') || doc['msp_payment.s_ita_id.keyword'].size() == 0) return 'CARD'; def ita = doc['msp_payment.s_ita_id.keyword'].value; return (ita == '') ? 'CARD' : 'Installment';",
                                    "lang": "painless"
                                }
                            }
                        }
                    },
                    {
                        "cardType": {
                            "terms": {
                                "field": "msp_payment.s_e_card.keyword"
                            }
                        }
                    },
                    {
                        "tid": {
                            "terms": {
                                "script": {
                                    "source": "def v = doc['msp_payment.s_data.tid.keyword'].size() == 0 ? '' : doc['msp_payment.s_data.tid.keyword'].value;\nreturn v == '' ? '' : v;",
                                    "lang": "painless"
                                }
                            }
                        }
                    }
                ]
            },
            "aggs": {
                "amount": {
                    "sum": {
                        "field": "msp_payment.n_amount"
                    }
                }
            }
        }
    }
}
2025-07-04 09:10:00.021 [boundedElastic-3] INFO  v.o.t.service.impl.UposServiceImpl - Received response from OpenSearch
2025-07-04 09:10:00.022 [boundedElastic-3] INFO  v.o.t.service.impl.UposServiceImpl - Converted OpenSearch response into 0 report entities
2025-07-04 09:10:00.022 [boundedElastic-3] INFO  v.o.t.service.impl.UposServiceImpl - Fetched 0 reports from the first OpenSearch request
2025-07-04 09:10:00.022 [boundedElastic-3] INFO  v.o.t.service.impl.UposServiceImpl - No reports to insert into database
2025-07-04 09:10:00.022 [boundedElastic-3] INFO  v.o.t.service.impl.UposServiceImpl - No afterKey found. All pages fetched.
