2025-07-18 04:07:26.080 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.k.l.Ka<PERSON>kaMessageListenerContainer - download-task-group: Consumer stopped
2025-07-18 04:07:26.109 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.k.l.KafkaMessageListenerContainer - download-task-group: Consumer stopped
2025-07-18 04:07:26.113 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.netty.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-18 04:07:26.121 [netty-shutdown] INFO  o.s.b.w.e.netty.GracefulShutdown - Graceful shutdown complete
2025-07-18 04:07:28.151 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Shutdown initiated...
2025-07-18 04:07:28.164 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Shutdown completed.
2025-07-18 04:07:28.165 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Shutdown initiated...
2025-07-18 04:07:28.185 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Shutdown completed.
