2025-08-04 09:18:26.933 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 3211194 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-08-04 09:18:26.935 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-08-04 09:18:27.697 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-08-04 09:18:27.880 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 175 ms. Found 2 R2DBC repository interfaces.
2025-08-04 09:18:29.404 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-08-04 09:18:29.701 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@156cfa20
2025-08-04 09:18:29.703 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-08-04 09:18:29.756 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-08-04 09:18:29.867 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@56f9de3b
2025-08-04 09:18:29.868 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-08-04 09:18:29.972 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-08-04 09:18:29.980 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-08-04 09:18:29.983 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-08-04 09:18:29.984 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-08-04 09:18:29.987 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-08-04 09:18:29.988 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-08-04 09:18:30.888 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8596 (http)
2025-08-04 09:18:31.085 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 4.819 seconds (process running for 6.386)
2025-08-04 09:20:24.807 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 3212434 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-08-04 09:20:24.810 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-08-04 09:20:25.599 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-08-04 09:20:25.787 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 181 ms. Found 2 R2DBC repository interfaces.
2025-08-04 09:20:27.344 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-08-04 09:20:27.625 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@58aa5c94
2025-08-04 09:20:27.627 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-08-04 09:20:27.682 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-08-04 09:20:27.806 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@27db45f
2025-08-04 09:20:27.807 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-08-04 09:20:27.903 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-08-04 09:20:27.911 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-08-04 09:20:27.913 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-08-04 09:20:27.914 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-08-04 09:20:27.916 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-08-04 09:20:27.917 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-08-04 09:20:28.885 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8596 (http)
2025-08-04 09:20:29.107 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 4.993 seconds (process running for 6.56)
2025-08-04 09:36:09.637 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 3221409 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-08-04 09:36:09.639 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-08-04 09:36:10.430 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-08-04 09:36:10.601 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 165 ms. Found 2 R2DBC repository interfaces.
2025-08-04 09:36:12.287 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-08-04 09:36:12.677 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@58aa5c94
2025-08-04 09:36:12.679 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-08-04 09:36:12.760 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-08-04 09:36:12.924 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@27db45f
2025-08-04 09:36:12.925 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-08-04 09:36:13.041 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-08-04 09:36:13.050 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-08-04 09:36:13.053 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-08-04 09:36:13.054 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-08-04 09:36:13.056 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-08-04 09:36:13.057 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-08-04 09:36:13.932 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8596 (http)
2025-08-04 09:36:14.130 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 5.16 seconds (process running for 6.727)
2025-08-04 09:53:14.321 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 3231159 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-08-04 09:53:14.324 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-08-04 09:53:15.148 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-08-04 09:53:15.416 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 261 ms. Found 2 R2DBC repository interfaces.
2025-08-04 09:53:17.132 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-08-04 09:53:17.447 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@156cfa20
2025-08-04 09:53:17.449 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-08-04 09:53:17.519 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-08-04 09:53:17.656 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@56f9de3b
2025-08-04 09:53:17.656 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-08-04 09:53:17.779 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-08-04 09:53:17.788 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-08-04 09:53:17.791 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-08-04 09:53:17.792 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-08-04 09:53:17.795 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-08-04 09:53:17.795 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-08-04 09:53:18.898 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8596 (http)
2025-08-04 09:53:19.110 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 5.481 seconds (process running for 6.952)
2025-08-04 09:53:59.834 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 3231744 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-08-04 09:53:59.838 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-08-04 09:54:00.634 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-08-04 09:54:00.839 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 197 ms. Found 2 R2DBC repository interfaces.
2025-08-04 09:54:02.539 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-08-04 09:54:02.923 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@156cfa20
2025-08-04 09:54:02.925 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-08-04 09:54:03.009 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-08-04 09:54:03.285 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@56f9de3b
2025-08-04 09:54:03.286 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-08-04 09:54:03.421 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-08-04 09:54:03.433 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-08-04 09:54:03.437 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-08-04 09:54:03.438 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-08-04 09:54:03.441 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-08-04 09:54:03.443 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-08-04 09:54:04.578 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8597 (http)
2025-08-04 09:54:04.839 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 5.695 seconds (process running for 7.297)
2025-08-04 09:54:23.489 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8597/ma-service/api/v1/transactions/transaction/multiple/approve-reject
2025-08-04 09:54:23.490 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-08-04 09:54:23.490 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-08-04 09:54:23.490 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-08-04 09:54:23.490 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-08-04 09:54:23.490 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-08-04 09:54:23.490 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev18-ma.onepay.vn
2025-08-04 09:54:23.490 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-08-04 09:54:23.490 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev18-ma.onepay.vn/mav2-main/
2025-08-04 09:54:23.491 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-08-04 09:54:23.491 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-08-04 09:54:23.491 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-08-04 09:54:23.491 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-08-04 09:54:23.491 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-08-04 09:54:23.491 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-08-04 09:54:23.491 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-08-04 09:54:23.491 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-08-04 09:54:23.491 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1*********; _ga_D5QNXXJGL1=GS2.2.s1*********$o1$g0$t1*********$j60$l0$h0; JSESSIONID=dummy; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************.01Lw3_bvHko48ou8T1vOIJxdZURj5hSCUtH7808I8i8
2025-08-04 09:54:23.492 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-04 09:54:23.492 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = d1df6fff-bcfe-42d4-9a30-b09ab35729a5
2025-08-04 09:54:23.492 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8597
2025-08-04 09:54:23.492 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-08-04 09:54:23.492 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 445
2025-08-04 09:54:23.497 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-08-04 09:54:24.073 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api approveOrRejectMultipleTransaction MA-----------
2025-08-04 09:54:24.086 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=null ,paymentMethod=BNPL ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-04 09:54:24.086 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-04 09:54:24.143 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8235/ma-bnpl/api/v1/bnpl/transaction/null ,xUserId=null
2025-08-04 09:54:24.340 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"rowIndex":0,"reqRefundId":0,"refundable":false,"voidable":false,"captureAble":false,"discount":0.0,"cutoffDay":0}
2025-08-04 09:54:24.340 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-04 09:54:24.345 [reactor-tcp-epoll-1] ERROR v.o.t.s.i.TransactionOldServiceImpl - Get transaction detail not exists: 
vn.onepay.transaction.exception.NoItemExistsException: Transaction refund with transactionId null not found
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.getTranDetail(TransactionOldServiceImpl.java:791)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.getTransactionDetail(TransactionOldServiceImpl.java:632)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.approveOrRejectMultipleTransaction(TransactionOldServiceImpl.java:1924)
	at vn.onepay.transaction.controller.TransactionController.lambda$16(TransactionController.java:379)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.Operators$MonoInnerProducerBase.complete(Operators.java:2864)
	at reactor.core.publisher.MonoSingle$SingleSubscriber.onComplete(MonoSingle.java:180)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onComplete(Operators.java:2231)
	at reactor.core.publisher.FluxUsingWhen$UsingWhenSubscriber.deferredComplete(FluxUsingWhen.java:397)
	at reactor.core.publisher.FluxUsingWhen$CommitInner.onComplete(FluxUsingWhen.java:532)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onComplete(Operators.java:2231)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:210)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:239)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:210)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.netty.FutureMono.doSubscribe(FutureMono.java:122)
	at reactor.netty.FutureMono$ImmediateFutureMono.subscribe(FutureMono.java:83)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onComplete(MonoPeekTerminal.java:299)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onComplete(MonoIgnoreElements.java:89)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onComplete(FluxConcatMapNoPrefetch.java:241)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onComplete(FluxPeekFuseable.java:277)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2573)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.request(FluxPeekFuseable.java:144)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onSubscribe(MonoIgnoreElements.java:72)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onSubscribe(FluxPeekFuseable.java:178)
	at reactor.core.publisher.FluxJust.subscribe(FluxJust.java:68)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onError(MonoIgnoreElements.java:84)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.FluxFilter$FilterSubscriber.onError(FluxFilter.java:157)
	at reactor.core.publisher.FluxFilter$FilterConditionalSubscriber.onError(FluxFilter.java:291)
	at reactor.core.publisher.FluxMap$MapConditionalSubscriber.onError(FluxMap.java:265)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
	at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxUsingWhen$UsingWhenSubscriber.onComplete(FluxUsingWhen.java:389)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.checkTerminated(FluxFlatMap.java:850)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.drainLoop(FluxFlatMap.java:612)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.innerComplete(FluxFlatMap.java:898)
	at reactor.core.publisher.FluxFlatMap$FlatMapInner.onComplete(FluxFlatMap.java:1001)
	at reactor.core.publisher.FluxHandle$HandleSubscriber.onComplete(FluxHandle.java:223)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyInner.onComplete(MonoFlatMapMany.java:261)
	at reactor.core.publisher.FluxHandleFuseable$HandleFuseableSubscriber.onComplete(FluxHandleFuseable.java:239)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onComplete(FluxFilterFuseable.java:391)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onComplete(FluxContextWrite.java:126)
	at reactor.core.publisher.FluxPeekFuseable$PeekConditionalSubscriber.onComplete(FluxPeekFuseable.java:940)
	at reactor.core.publisher.FluxPeekFuseable$PeekConditionalSubscriber.onComplete(FluxPeekFuseable.java:940)
	at io.r2dbc.postgresql.util.FluxDiscardOnCancel$FluxDiscardOnCancelSubscriber.onComplete(FluxDiscardOnCancel.java:104)
	at reactor.core.publisher.FluxDoFinally$DoFinallySubscriber.onComplete(FluxDoFinally.java:128)
	at reactor.core.publisher.FluxHandle$HandleSubscriber.onComplete(FluxHandle.java:223)
	at reactor.core.publisher.FluxCreate$BaseSink.complete(FluxCreate.java:465)
	at reactor.core.publisher.FluxCreate$BufferAsyncSink.drain(FluxCreate.java:871)
	at reactor.core.publisher.FluxCreate$BufferAsyncSink.complete(FluxCreate.java:819)
	at reactor.core.publisher.FluxCreate$SerializedFluxSink.drainLoop(FluxCreate.java:249)
	at reactor.core.publisher.FluxCreate$SerializedFluxSink.drain(FluxCreate.java:215)
	at reactor.core.publisher.FluxCreate$SerializedFluxSink.complete(FluxCreate.java:206)
	at io.r2dbc.postgresql.client.ReactorNettyClient$Conversation.complete(ReactorNettyClient.java:680)
	at io.r2dbc.postgresql.client.ReactorNettyClient$BackendMessageSubscriber.emit(ReactorNettyClient.java:946)
	at io.r2dbc.postgresql.client.ReactorNettyClient$BackendMessageSubscriber.onNext(ReactorNettyClient.java:822)
	at io.r2dbc.postgresql.client.ReactorNettyClient$BackendMessageSubscriber.onNext(ReactorNettyClient.java:728)
	at reactor.core.publisher.FluxHandle$HandleSubscriber.onNext(FluxHandle.java:129)
	at reactor.core.publisher.FluxPeekFuseable$PeekConditionalSubscriber.onNext(FluxPeekFuseable.java:854)
	at reactor.core.publisher.FluxMap$MapConditionalSubscriber.onNext(FluxMap.java:224)
	at reactor.core.publisher.FluxMap$MapConditionalSubscriber.onNext(FluxMap.java:224)
	at reactor.netty.channel.FluxReceive.drainReceiver(FluxReceive.java:292)
	at reactor.netty.channel.FluxReceive.onInboundNext(FluxReceive.java:401)
	at reactor.netty.channel.ChannelOperations.onInboundNext(ChannelOperations.java:435)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-04 09:54:24.347 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-04 09:54:24.347 [reactor-tcp-epoll-1] ERROR v.o.t.s.i.TransactionOldServiceImpl - Error call api approveOrRejectMultipleTransaction: 
vn.onepay.transaction.exception.NoItemExistsException: Transaction refund with transactionId null not found
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.getTranDetail(TransactionOldServiceImpl.java:791)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.getTransactionDetail(TransactionOldServiceImpl.java:632)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.approveOrRejectMultipleTransaction(TransactionOldServiceImpl.java:1924)
	at vn.onepay.transaction.controller.TransactionController.lambda$16(TransactionController.java:379)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.Operators$MonoInnerProducerBase.complete(Operators.java:2864)
	at reactor.core.publisher.MonoSingle$SingleSubscriber.onComplete(MonoSingle.java:180)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onComplete(Operators.java:2231)
	at reactor.core.publisher.FluxUsingWhen$UsingWhenSubscriber.deferredComplete(FluxUsingWhen.java:397)
	at reactor.core.publisher.FluxUsingWhen$CommitInner.onComplete(FluxUsingWhen.java:532)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onComplete(Operators.java:2231)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:210)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:239)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:210)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.netty.FutureMono.doSubscribe(FutureMono.java:122)
	at reactor.netty.FutureMono$ImmediateFutureMono.subscribe(FutureMono.java:83)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onComplete(MonoPeekTerminal.java:299)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onComplete(MonoIgnoreElements.java:89)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onComplete(FluxConcatMapNoPrefetch.java:241)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onComplete(FluxPeekFuseable.java:277)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2573)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.request(FluxPeekFuseable.java:144)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onSubscribe(MonoIgnoreElements.java:72)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onSubscribe(FluxPeekFuseable.java:178)
	at reactor.core.publisher.FluxJust.subscribe(FluxJust.java:68)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onError(MonoIgnoreElements.java:84)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.FluxFilter$FilterSubscriber.onError(FluxFilter.java:157)
	at reactor.core.publisher.FluxFilter$FilterConditionalSubscriber.onError(FluxFilter.java:291)
	at reactor.core.publisher.FluxMap$MapConditionalSubscriber.onError(FluxMap.java:265)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
	at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxUsingWhen$UsingWhenSubscriber.onComplete(FluxUsingWhen.java:389)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.checkTerminated(FluxFlatMap.java:850)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.drainLoop(FluxFlatMap.java:612)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.innerComplete(FluxFlatMap.java:898)
	at reactor.core.publisher.FluxFlatMap$FlatMapInner.onComplete(FluxFlatMap.java:1001)
	at reactor.core.publisher.FluxHandle$HandleSubscriber.onComplete(FluxHandle.java:223)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyInner.onComplete(MonoFlatMapMany.java:261)
	at reactor.core.publisher.FluxHandleFuseable$HandleFuseableSubscriber.onComplete(FluxHandleFuseable.java:239)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onComplete(FluxFilterFuseable.java:391)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onComplete(FluxContextWrite.java:126)
	at reactor.core.publisher.FluxPeekFuseable$PeekConditionalSubscriber.onComplete(FluxPeekFuseable.java:940)
	at reactor.core.publisher.FluxPeekFuseable$PeekConditionalSubscriber.onComplete(FluxPeekFuseable.java:940)
	at io.r2dbc.postgresql.util.FluxDiscardOnCancel$FluxDiscardOnCancelSubscriber.onComplete(FluxDiscardOnCancel.java:104)
	at reactor.core.publisher.FluxDoFinally$DoFinallySubscriber.onComplete(FluxDoFinally.java:128)
	at reactor.core.publisher.FluxHandle$HandleSubscriber.onComplete(FluxHandle.java:223)
	at reactor.core.publisher.FluxCreate$BaseSink.complete(FluxCreate.java:465)
	at reactor.core.publisher.FluxCreate$BufferAsyncSink.drain(FluxCreate.java:871)
	at reactor.core.publisher.FluxCreate$BufferAsyncSink.complete(FluxCreate.java:819)
	at reactor.core.publisher.FluxCreate$SerializedFluxSink.drainLoop(FluxCreate.java:249)
	at reactor.core.publisher.FluxCreate$SerializedFluxSink.drain(FluxCreate.java:215)
	at reactor.core.publisher.FluxCreate$SerializedFluxSink.complete(FluxCreate.java:206)
	at io.r2dbc.postgresql.client.ReactorNettyClient$Conversation.complete(ReactorNettyClient.java:680)
	at io.r2dbc.postgresql.client.ReactorNettyClient$BackendMessageSubscriber.emit(ReactorNettyClient.java:946)
	at io.r2dbc.postgresql.client.ReactorNettyClient$BackendMessageSubscriber.onNext(ReactorNettyClient.java:822)
	at io.r2dbc.postgresql.client.ReactorNettyClient$BackendMessageSubscriber.onNext(ReactorNettyClient.java:728)
	at reactor.core.publisher.FluxHandle$HandleSubscriber.onNext(FluxHandle.java:129)
	at reactor.core.publisher.FluxPeekFuseable$PeekConditionalSubscriber.onNext(FluxPeekFuseable.java:854)
	at reactor.core.publisher.FluxMap$MapConditionalSubscriber.onNext(FluxMap.java:224)
	at reactor.core.publisher.FluxMap$MapConditionalSubscriber.onNext(FluxMap.java:224)
	at reactor.netty.channel.FluxReceive.drainReceiver(FluxReceive.java:292)
	at reactor.netty.channel.FluxReceive.onInboundNext(FluxReceive.java:401)
	at reactor.netty.channel.ChannelOperations.onInboundNext(ChannelOperations.java:435)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-04 09:54:24.348 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api approveOrRejectMultipleTransaction MA------------
2025-08-04 09:54:24.366 [reactor-tcp-epoll-1] INFO  v.o.t.exception.RequestLoggingFilter - Response: PATCH http://localhost:8597/ma-service/api/v1/transactions/transaction/multiple/approve-reject - Status: 200 OK
2025-08-04 09:54:57.505 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8597/ma-service/api/v1/transactions/transaction/multiple/approve-reject
2025-08-04 09:54:57.506 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-08-04 09:54:57.506 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-08-04 09:54:57.506 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-08-04 09:54:57.506 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-08-04 09:54:57.506 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-08-04 09:54:57.506 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev18-ma.onepay.vn
2025-08-04 09:54:57.506 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-08-04 09:54:57.506 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev18-ma.onepay.vn/mav2-main/
2025-08-04 09:54:57.506 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-08-04 09:54:57.506 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-08-04 09:54:57.506 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-08-04 09:54:57.506 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-08-04 09:54:57.506 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-08-04 09:54:57.506 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-08-04 09:54:57.506 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-08-04 09:54:57.507 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-08-04 09:54:57.507 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1*********; _ga_D5QNXXJGL1=GS2.2.s1*********$o1$g0$t1*********$j60$l0$h0; JSESSIONID=dummy; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************.01Lw3_bvHko48ou8T1vOIJxdZURj5hSCUtH7808I8i8
2025-08-04 09:54:57.507 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-04 09:54:57.507 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 352f0ab3-90fe-4006-9748-b992334dd718
2025-08-04 09:54:57.507 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8597
2025-08-04 09:54:57.507 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-08-04 09:54:57.507 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 445
2025-08-04 09:54:57.507 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-08-04 09:54:57.547 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api approveOrRejectMultipleTransaction MA-----------
2025-08-04 09:55:47.820 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=null ,paymentMethod=BNPL ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-04 09:55:47.821 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-04 09:55:47.823 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8235/ma-bnpl/api/v1/bnpl/transaction/null ,xUserId=null
2025-08-04 09:55:47.833 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"rowIndex":0,"reqRefundId":0,"refundable":false,"voidable":false,"captureAble":false,"discount":0.0,"cutoffDay":0}
2025-08-04 09:55:47.834 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-04 09:55:47.835 [reactor-tcp-epoll-2] ERROR v.o.t.s.i.TransactionOldServiceImpl - Get transaction detail not exists: 
vn.onepay.transaction.exception.NoItemExistsException: Transaction refund with transactionId null not found
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.getTranDetail(TransactionOldServiceImpl.java:791)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.getTransactionDetail(TransactionOldServiceImpl.java:632)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.approveOrRejectMultipleTransaction(TransactionOldServiceImpl.java:1924)
	at vn.onepay.transaction.controller.TransactionController.lambda$16(TransactionController.java:379)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.Operators$MonoInnerProducerBase.complete(Operators.java:2864)
	at reactor.core.publisher.MonoSingle$SingleSubscriber.onComplete(MonoSingle.java:180)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onComplete(Operators.java:2231)
	at reactor.core.publisher.FluxUsingWhen$UsingWhenSubscriber.deferredComplete(FluxUsingWhen.java:397)
	at reactor.core.publisher.FluxUsingWhen$CommitInner.onComplete(FluxUsingWhen.java:532)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onComplete(Operators.java:2231)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:210)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:239)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:210)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.netty.FutureMono.doSubscribe(FutureMono.java:122)
	at reactor.netty.FutureMono$ImmediateFutureMono.subscribe(FutureMono.java:83)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onComplete(MonoPeekTerminal.java:299)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onComplete(MonoIgnoreElements.java:89)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onComplete(FluxConcatMapNoPrefetch.java:241)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onComplete(FluxPeekFuseable.java:277)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2573)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.request(FluxPeekFuseable.java:144)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onSubscribe(MonoIgnoreElements.java:72)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onSubscribe(FluxPeekFuseable.java:178)
	at reactor.core.publisher.FluxJust.subscribe(FluxJust.java:68)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onError(MonoIgnoreElements.java:84)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.FluxFilter$FilterSubscriber.onError(FluxFilter.java:157)
	at reactor.core.publisher.FluxFilter$FilterConditionalSubscriber.onError(FluxFilter.java:291)
	at reactor.core.publisher.FluxMap$MapConditionalSubscriber.onError(FluxMap.java:265)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
	at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxUsingWhen$UsingWhenSubscriber.onComplete(FluxUsingWhen.java:389)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.checkTerminated(FluxFlatMap.java:850)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.drainLoop(FluxFlatMap.java:612)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.innerComplete(FluxFlatMap.java:898)
	at reactor.core.publisher.FluxFlatMap$FlatMapInner.onComplete(FluxFlatMap.java:1001)
	at reactor.core.publisher.FluxHandle$HandleSubscriber.onComplete(FluxHandle.java:223)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyInner.onComplete(MonoFlatMapMany.java:261)
	at reactor.core.publisher.FluxHandleFuseable$HandleFuseableSubscriber.onComplete(FluxHandleFuseable.java:239)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onComplete(FluxFilterFuseable.java:391)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onComplete(FluxContextWrite.java:126)
	at reactor.core.publisher.FluxPeekFuseable$PeekConditionalSubscriber.onComplete(FluxPeekFuseable.java:940)
	at reactor.core.publisher.FluxPeekFuseable$PeekConditionalSubscriber.onComplete(FluxPeekFuseable.java:940)
	at io.r2dbc.postgresql.util.FluxDiscardOnCancel$FluxDiscardOnCancelSubscriber.onComplete(FluxDiscardOnCancel.java:104)
	at reactor.core.publisher.FluxDoFinally$DoFinallySubscriber.onComplete(FluxDoFinally.java:128)
	at reactor.core.publisher.FluxHandle$HandleSubscriber.onComplete(FluxHandle.java:223)
	at reactor.core.publisher.FluxCreate$BaseSink.complete(FluxCreate.java:465)
	at reactor.core.publisher.FluxCreate$BufferAsyncSink.drain(FluxCreate.java:871)
	at reactor.core.publisher.FluxCreate$BufferAsyncSink.complete(FluxCreate.java:819)
	at reactor.core.publisher.FluxCreate$SerializedFluxSink.drainLoop(FluxCreate.java:249)
	at reactor.core.publisher.FluxCreate$SerializedFluxSink.drain(FluxCreate.java:215)
	at reactor.core.publisher.FluxCreate$SerializedFluxSink.complete(FluxCreate.java:206)
	at io.r2dbc.postgresql.client.ReactorNettyClient$Conversation.complete(ReactorNettyClient.java:680)
	at io.r2dbc.postgresql.client.ReactorNettyClient$BackendMessageSubscriber.emit(ReactorNettyClient.java:946)
	at io.r2dbc.postgresql.client.ReactorNettyClient$BackendMessageSubscriber.onNext(ReactorNettyClient.java:822)
	at io.r2dbc.postgresql.client.ReactorNettyClient$BackendMessageSubscriber.onNext(ReactorNettyClient.java:728)
	at reactor.core.publisher.FluxHandle$HandleSubscriber.onNext(FluxHandle.java:129)
	at reactor.core.publisher.FluxPeekFuseable$PeekConditionalSubscriber.onNext(FluxPeekFuseable.java:854)
	at reactor.core.publisher.FluxMap$MapConditionalSubscriber.onNext(FluxMap.java:224)
	at reactor.core.publisher.FluxMap$MapConditionalSubscriber.onNext(FluxMap.java:224)
	at reactor.netty.channel.FluxReceive.drainReceiver(FluxReceive.java:292)
	at reactor.netty.channel.FluxReceive.onInboundNext(FluxReceive.java:401)
	at reactor.netty.channel.ChannelOperations.onInboundNext(ChannelOperations.java:435)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-04 09:55:47.842 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-04 09:55:47.843 [reactor-tcp-epoll-2] ERROR v.o.t.s.i.TransactionOldServiceImpl - Error call api approveOrRejectMultipleTransaction: 
vn.onepay.transaction.exception.NoItemExistsException: Transaction refund with transactionId null not found
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.getTranDetail(TransactionOldServiceImpl.java:791)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.getTransactionDetail(TransactionOldServiceImpl.java:632)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.approveOrRejectMultipleTransaction(TransactionOldServiceImpl.java:1924)
	at vn.onepay.transaction.controller.TransactionController.lambda$16(TransactionController.java:379)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.Operators$MonoInnerProducerBase.complete(Operators.java:2864)
	at reactor.core.publisher.MonoSingle$SingleSubscriber.onComplete(MonoSingle.java:180)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onComplete(Operators.java:2231)
	at reactor.core.publisher.FluxUsingWhen$UsingWhenSubscriber.deferredComplete(FluxUsingWhen.java:397)
	at reactor.core.publisher.FluxUsingWhen$CommitInner.onComplete(FluxUsingWhen.java:532)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onComplete(Operators.java:2231)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:210)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:239)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:210)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.netty.FutureMono.doSubscribe(FutureMono.java:122)
	at reactor.netty.FutureMono$ImmediateFutureMono.subscribe(FutureMono.java:83)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onComplete(MonoPeekTerminal.java:299)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onComplete(MonoIgnoreElements.java:89)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onComplete(FluxConcatMapNoPrefetch.java:241)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onComplete(FluxPeekFuseable.java:277)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2573)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.request(FluxPeekFuseable.java:144)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onSubscribe(MonoIgnoreElements.java:72)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onSubscribe(FluxPeekFuseable.java:178)
	at reactor.core.publisher.FluxJust.subscribe(FluxJust.java:68)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onError(MonoIgnoreElements.java:84)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.FluxFilter$FilterSubscriber.onError(FluxFilter.java:157)
	at reactor.core.publisher.FluxFilter$FilterConditionalSubscriber.onError(FluxFilter.java:291)
	at reactor.core.publisher.FluxMap$MapConditionalSubscriber.onError(FluxMap.java:265)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
	at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxUsingWhen$UsingWhenSubscriber.onComplete(FluxUsingWhen.java:389)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.checkTerminated(FluxFlatMap.java:850)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.drainLoop(FluxFlatMap.java:612)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.innerComplete(FluxFlatMap.java:898)
	at reactor.core.publisher.FluxFlatMap$FlatMapInner.onComplete(FluxFlatMap.java:1001)
	at reactor.core.publisher.FluxHandle$HandleSubscriber.onComplete(FluxHandle.java:223)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyInner.onComplete(MonoFlatMapMany.java:261)
	at reactor.core.publisher.FluxHandleFuseable$HandleFuseableSubscriber.onComplete(FluxHandleFuseable.java:239)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onComplete(FluxFilterFuseable.java:391)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onComplete(FluxContextWrite.java:126)
	at reactor.core.publisher.FluxPeekFuseable$PeekConditionalSubscriber.onComplete(FluxPeekFuseable.java:940)
	at reactor.core.publisher.FluxPeekFuseable$PeekConditionalSubscriber.onComplete(FluxPeekFuseable.java:940)
	at io.r2dbc.postgresql.util.FluxDiscardOnCancel$FluxDiscardOnCancelSubscriber.onComplete(FluxDiscardOnCancel.java:104)
	at reactor.core.publisher.FluxDoFinally$DoFinallySubscriber.onComplete(FluxDoFinally.java:128)
	at reactor.core.publisher.FluxHandle$HandleSubscriber.onComplete(FluxHandle.java:223)
	at reactor.core.publisher.FluxCreate$BaseSink.complete(FluxCreate.java:465)
	at reactor.core.publisher.FluxCreate$BufferAsyncSink.drain(FluxCreate.java:871)
	at reactor.core.publisher.FluxCreate$BufferAsyncSink.complete(FluxCreate.java:819)
	at reactor.core.publisher.FluxCreate$SerializedFluxSink.drainLoop(FluxCreate.java:249)
	at reactor.core.publisher.FluxCreate$SerializedFluxSink.drain(FluxCreate.java:215)
	at reactor.core.publisher.FluxCreate$SerializedFluxSink.complete(FluxCreate.java:206)
	at io.r2dbc.postgresql.client.ReactorNettyClient$Conversation.complete(ReactorNettyClient.java:680)
	at io.r2dbc.postgresql.client.ReactorNettyClient$BackendMessageSubscriber.emit(ReactorNettyClient.java:946)
	at io.r2dbc.postgresql.client.ReactorNettyClient$BackendMessageSubscriber.onNext(ReactorNettyClient.java:822)
	at io.r2dbc.postgresql.client.ReactorNettyClient$BackendMessageSubscriber.onNext(ReactorNettyClient.java:728)
	at reactor.core.publisher.FluxHandle$HandleSubscriber.onNext(FluxHandle.java:129)
	at reactor.core.publisher.FluxPeekFuseable$PeekConditionalSubscriber.onNext(FluxPeekFuseable.java:854)
	at reactor.core.publisher.FluxMap$MapConditionalSubscriber.onNext(FluxMap.java:224)
	at reactor.core.publisher.FluxMap$MapConditionalSubscriber.onNext(FluxMap.java:224)
	at reactor.netty.channel.FluxReceive.drainReceiver(FluxReceive.java:292)
	at reactor.netty.channel.FluxReceive.onInboundNext(FluxReceive.java:401)
	at reactor.netty.channel.ChannelOperations.onInboundNext(ChannelOperations.java:435)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-04 09:55:47.849 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api approveOrRejectMultipleTransaction MA------------
2025-08-04 09:55:47.851 [reactor-tcp-epoll-2] INFO  v.o.t.exception.RequestLoggingFilter - Response: PATCH http://localhost:8597/ma-service/api/v1/transactions/transaction/multiple/approve-reject - Status: 200 OK
2025-08-04 09:55:50.086 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8597/ma-service/api/v1/transactions/transaction/multiple/approve-reject
2025-08-04 09:55:50.087 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-08-04 09:55:50.087 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-08-04 09:55:50.087 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-08-04 09:55:50.087 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-08-04 09:55:50.087 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-08-04 09:55:50.087 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev18-ma.onepay.vn
2025-08-04 09:55:50.087 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-08-04 09:55:50.087 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev18-ma.onepay.vn/mav2-main/
2025-08-04 09:55:50.087 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-08-04 09:55:50.087 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-08-04 09:55:50.087 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-08-04 09:55:50.087 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-08-04 09:55:50.088 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-08-04 09:55:50.088 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-08-04 09:55:50.088 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-08-04 09:55:50.088 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-08-04 09:55:50.088 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1*********; _ga_D5QNXXJGL1=GS2.2.s1*********$o1$g0$t1*********$j60$l0$h0; JSESSIONID=dummy; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************.01Lw3_bvHko48ou8T1vOIJxdZURj5hSCUtH7808I8i8
2025-08-04 09:55:50.088 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-04 09:55:50.088 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 05549e09-6268-4c76-8073-71d2719c7a15
2025-08-04 09:55:50.088 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8597
2025-08-04 09:55:50.088 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-08-04 09:55:50.088 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 722
2025-08-04 09:55:50.088 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-08-04 09:55:50.126 [reactor-tcp-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api approveOrRejectMultipleTransaction MA-----------
2025-08-04 09:55:57.851 [reactor-tcp-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=864143 ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-04 09:55:57.860 [reactor-tcp-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-04 09:55:57.878 [reactor-tcp-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/864143 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-04 09:55:57.898 [reactor-tcp-epoll-3] ERROR v.o.t.s.i.TransactionOldServiceImpl - Error call api get transaction detail: 
java.net.ConnectException: null
	at java.net.http/jdk.internal.net.http.HttpClientImpl.send(HttpClientImpl.java:951)
	at java.net.http/jdk.internal.net.http.HttpClientFacade.send(HttpClientFacade.java:133)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.callApiGetTransDetail(TransactionOldServiceImpl.java:1805)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.getTranDetail(TransactionOldServiceImpl.java:666)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.getTransactionDetail(TransactionOldServiceImpl.java:632)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.approveOrRejectMultipleTransaction(TransactionOldServiceImpl.java:1924)
	at vn.onepay.transaction.controller.TransactionController.lambda$16(TransactionController.java:379)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.Operators$MonoInnerProducerBase.complete(Operators.java:2864)
	at reactor.core.publisher.MonoSingle$SingleSubscriber.onComplete(MonoSingle.java:180)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onComplete(Operators.java:2231)
	at reactor.core.publisher.FluxUsingWhen$UsingWhenSubscriber.deferredComplete(FluxUsingWhen.java:397)
	at reactor.core.publisher.FluxUsingWhen$CommitInner.onComplete(FluxUsingWhen.java:532)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onComplete(Operators.java:2231)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:210)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:239)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:210)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.netty.FutureMono.doSubscribe(FutureMono.java:122)
	at reactor.netty.FutureMono$ImmediateFutureMono.subscribe(FutureMono.java:83)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onComplete(MonoPeekTerminal.java:299)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onComplete(MonoIgnoreElements.java:89)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onComplete(FluxConcatMapNoPrefetch.java:241)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onComplete(FluxPeekFuseable.java:277)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2573)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.request(FluxPeekFuseable.java:144)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onSubscribe(MonoIgnoreElements.java:72)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onSubscribe(FluxPeekFuseable.java:178)
	at reactor.core.publisher.FluxJust.subscribe(FluxJust.java:68)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onError(MonoIgnoreElements.java:84)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.FluxFilter$FilterSubscriber.onError(FluxFilter.java:157)
	at reactor.core.publisher.FluxFilter$FilterConditionalSubscriber.onError(FluxFilter.java:291)
	at reactor.core.publisher.FluxMap$MapConditionalSubscriber.onError(FluxMap.java:265)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
	at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxUsingWhen$UsingWhenSubscriber.onComplete(FluxUsingWhen.java:389)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.checkTerminated(FluxFlatMap.java:850)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.drainLoop(FluxFlatMap.java:612)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.innerComplete(FluxFlatMap.java:898)
	at reactor.core.publisher.FluxFlatMap$FlatMapInner.onComplete(FluxFlatMap.java:1001)
	at reactor.core.publisher.FluxHandle$HandleSubscriber.onComplete(FluxHandle.java:223)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyInner.onComplete(MonoFlatMapMany.java:261)
	at reactor.core.publisher.FluxHandleFuseable$HandleFuseableSubscriber.onComplete(FluxHandleFuseable.java:239)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onComplete(FluxFilterFuseable.java:391)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onComplete(FluxContextWrite.java:126)
	at reactor.core.publisher.FluxPeekFuseable$PeekConditionalSubscriber.onComplete(FluxPeekFuseable.java:940)
	at reactor.core.publisher.FluxPeekFuseable$PeekConditionalSubscriber.onComplete(FluxPeekFuseable.java:940)
	at io.r2dbc.postgresql.util.FluxDiscardOnCancel$FluxDiscardOnCancelSubscriber.onComplete(FluxDiscardOnCancel.java:104)
	at reactor.core.publisher.FluxDoFinally$DoFinallySubscriber.onComplete(FluxDoFinally.java:128)
	at reactor.core.publisher.FluxHandle$HandleSubscriber.onComplete(FluxHandle.java:223)
	at reactor.core.publisher.FluxCreate$BaseSink.complete(FluxCreate.java:465)
	at reactor.core.publisher.FluxCreate$BufferAsyncSink.drain(FluxCreate.java:871)
	at reactor.core.publisher.FluxCreate$BufferAsyncSink.complete(FluxCreate.java:819)
	at reactor.core.publisher.FluxCreate$SerializedFluxSink.drainLoop(FluxCreate.java:249)
	at reactor.core.publisher.FluxCreate$SerializedFluxSink.drain(FluxCreate.java:215)
	at reactor.core.publisher.FluxCreate$SerializedFluxSink.complete(FluxCreate.java:206)
	at io.r2dbc.postgresql.client.ReactorNettyClient$Conversation.complete(ReactorNettyClient.java:680)
	at io.r2dbc.postgresql.client.ReactorNettyClient$BackendMessageSubscriber.emit(ReactorNettyClient.java:946)
	at io.r2dbc.postgresql.client.ReactorNettyClient$BackendMessageSubscriber.onNext(ReactorNettyClient.java:822)
	at io.r2dbc.postgresql.client.ReactorNettyClient$BackendMessageSubscriber.onNext(ReactorNettyClient.java:728)
	at reactor.core.publisher.FluxHandle$HandleSubscriber.onNext(FluxHandle.java:129)
	at reactor.core.publisher.FluxPeekFuseable$PeekConditionalSubscriber.onNext(FluxPeekFuseable.java:854)
	at reactor.core.publisher.FluxMap$MapConditionalSubscriber.onNext(FluxMap.java:224)
	at reactor.core.publisher.FluxMap$MapConditionalSubscriber.onNext(FluxMap.java:224)
	at reactor.netty.channel.FluxReceive.drainReceiver(FluxReceive.java:292)
	at reactor.netty.channel.FluxReceive.onInboundNext(FluxReceive.java:401)
	at reactor.netty.channel.ChannelOperations.onInboundNext(ChannelOperations.java:435)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.net.ConnectException: null
	at java.net.http/jdk.internal.net.http.common.Utils.toConnectException(Utils.java:1041)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.connectAsync(PlainHttpConnection.java:227)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.checkRetryConnect(PlainHttpConnection.java:280)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.lambda$connectAsync$2(PlainHttpConnection.java:238)
	at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
	at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1773)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	... 1 common frames omitted
Caused by: java.nio.channels.ClosedChannelException: null
	at java.base/sun.nio.ch.SocketChannelImpl.ensureOpen(SocketChannelImpl.java:202)
	at java.base/sun.nio.ch.SocketChannelImpl.beginConnect(SocketChannelImpl.java:786)
	at java.base/sun.nio.ch.SocketChannelImpl.connect(SocketChannelImpl.java:874)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.lambda$connectAsync$1(PlainHttpConnection.java:210)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:571)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.connectAsync(PlainHttpConnection.java:212)
	... 9 common frames omitted
2025-08-04 09:55:58.022 [reactor-tcp-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-04 09:55:58.029 [reactor-tcp-epoll-3] ERROR v.o.t.s.i.TransactionOldServiceImpl - Error getTransactionDetail: 
java.lang.IllegalArgumentException: argument "content" is null
	at com.fasterxml.jackson.databind.ObjectMapper._assertNotNull(ObjectMapper.java:5086)
	at com.fasterxml.jackson.databind.ObjectMapper.readTree(ObjectMapper.java:3279)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.getTranDetail(TransactionOldServiceImpl.java:669)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.getTransactionDetail(TransactionOldServiceImpl.java:632)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.approveOrRejectMultipleTransaction(TransactionOldServiceImpl.java:1924)
	at vn.onepay.transaction.controller.TransactionController.lambda$16(TransactionController.java:379)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.Operators$MonoInnerProducerBase.complete(Operators.java:2864)
	at reactor.core.publisher.MonoSingle$SingleSubscriber.onComplete(MonoSingle.java:180)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onComplete(Operators.java:2231)
	at reactor.core.publisher.FluxUsingWhen$UsingWhenSubscriber.deferredComplete(FluxUsingWhen.java:397)
	at reactor.core.publisher.FluxUsingWhen$CommitInner.onComplete(FluxUsingWhen.java:532)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onComplete(Operators.java:2231)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:210)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:239)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:210)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.netty.FutureMono.doSubscribe(FutureMono.java:122)
	at reactor.netty.FutureMono$ImmediateFutureMono.subscribe(FutureMono.java:83)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onComplete(MonoPeekTerminal.java:299)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onComplete(MonoIgnoreElements.java:89)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onComplete(FluxConcatMapNoPrefetch.java:241)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onComplete(FluxPeekFuseable.java:277)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2573)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.request(FluxPeekFuseable.java:144)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onSubscribe(MonoIgnoreElements.java:72)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onSubscribe(FluxPeekFuseable.java:178)
	at reactor.core.publisher.FluxJust.subscribe(FluxJust.java:68)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onError(MonoIgnoreElements.java:84)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.FluxFilter$FilterSubscriber.onError(FluxFilter.java:157)
	at reactor.core.publisher.FluxFilter$FilterConditionalSubscriber.onError(FluxFilter.java:291)
	at reactor.core.publisher.FluxMap$MapConditionalSubscriber.onError(FluxMap.java:265)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
	at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxUsingWhen$UsingWhenSubscriber.onComplete(FluxUsingWhen.java:389)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.checkTerminated(FluxFlatMap.java:850)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.drainLoop(FluxFlatMap.java:612)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.innerComplete(FluxFlatMap.java:898)
	at reactor.core.publisher.FluxFlatMap$FlatMapInner.onComplete(FluxFlatMap.java:1001)
	at reactor.core.publisher.FluxHandle$HandleSubscriber.onComplete(FluxHandle.java:223)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyInner.onComplete(MonoFlatMapMany.java:261)
	at reactor.core.publisher.FluxHandleFuseable$HandleFuseableSubscriber.onComplete(FluxHandleFuseable.java:239)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onComplete(FluxFilterFuseable.java:391)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onComplete(FluxContextWrite.java:126)
	at reactor.core.publisher.FluxPeekFuseable$PeekConditionalSubscriber.onComplete(FluxPeekFuseable.java:940)
	at reactor.core.publisher.FluxPeekFuseable$PeekConditionalSubscriber.onComplete(FluxPeekFuseable.java:940)
	at io.r2dbc.postgresql.util.FluxDiscardOnCancel$FluxDiscardOnCancelSubscriber.onComplete(FluxDiscardOnCancel.java:104)
	at reactor.core.publisher.FluxDoFinally$DoFinallySubscriber.onComplete(FluxDoFinally.java:128)
	at reactor.core.publisher.FluxHandle$HandleSubscriber.onComplete(FluxHandle.java:223)
	at reactor.core.publisher.FluxCreate$BaseSink.complete(FluxCreate.java:465)
	at reactor.core.publisher.FluxCreate$BufferAsyncSink.drain(FluxCreate.java:871)
	at reactor.core.publisher.FluxCreate$BufferAsyncSink.complete(FluxCreate.java:819)
	at reactor.core.publisher.FluxCreate$SerializedFluxSink.drainLoop(FluxCreate.java:249)
	at reactor.core.publisher.FluxCreate$SerializedFluxSink.drain(FluxCreate.java:215)
	at reactor.core.publisher.FluxCreate$SerializedFluxSink.complete(FluxCreate.java:206)
	at io.r2dbc.postgresql.client.ReactorNettyClient$Conversation.complete(ReactorNettyClient.java:680)
	at io.r2dbc.postgresql.client.ReactorNettyClient$BackendMessageSubscriber.emit(ReactorNettyClient.java:946)
	at io.r2dbc.postgresql.client.ReactorNettyClient$BackendMessageSubscriber.onNext(ReactorNettyClient.java:822)
	at io.r2dbc.postgresql.client.ReactorNettyClient$BackendMessageSubscriber.onNext(ReactorNettyClient.java:728)
	at reactor.core.publisher.FluxHandle$HandleSubscriber.onNext(FluxHandle.java:129)
	at reactor.core.publisher.FluxPeekFuseable$PeekConditionalSubscriber.onNext(FluxPeekFuseable.java:854)
	at reactor.core.publisher.FluxMap$MapConditionalSubscriber.onNext(FluxMap.java:224)
	at reactor.core.publisher.FluxMap$MapConditionalSubscriber.onNext(FluxMap.java:224)
	at reactor.netty.channel.FluxReceive.drainReceiver(FluxReceive.java:292)
	at reactor.netty.channel.FluxReceive.onInboundNext(FluxReceive.java:401)
	at reactor.netty.channel.ChannelOperations.onInboundNext(ChannelOperations.java:435)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-04 09:55:58.094 [reactor-tcp-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-04 09:55:58.100 [reactor-tcp-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getAdvanceFeeInfo ===================
2025-08-04 09:56:08.607 [reactor-tcp-epoll-3] ERROR v.o.t.s.i.TransactionOldServiceImpl - Error getAdvanceFeeInfo: 
java.lang.NullPointerException: Cannot invoke "vn.onepay.transaction.dto.TransactionDetailResponse.getMerchantInformation()" because "<parameter1>" is null
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.lambda$25(TransactionOldServiceImpl.java:3176)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:346)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.getAdvanceFeeInfo(TransactionOldServiceImpl.java:3170)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.getTransactionDetail(TransactionOldServiceImpl.java:633)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.approveOrRejectMultipleTransaction(TransactionOldServiceImpl.java:1924)
	at vn.onepay.transaction.controller.TransactionController.lambda$16(TransactionController.java:379)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.Operators$MonoInnerProducerBase.complete(Operators.java:2864)
	at reactor.core.publisher.MonoSingle$SingleSubscriber.onComplete(MonoSingle.java:180)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onComplete(Operators.java:2231)
	at reactor.core.publisher.FluxUsingWhen$UsingWhenSubscriber.deferredComplete(FluxUsingWhen.java:397)
	at reactor.core.publisher.FluxUsingWhen$CommitInner.onComplete(FluxUsingWhen.java:532)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onComplete(Operators.java:2231)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:210)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:239)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:210)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.netty.FutureMono.doSubscribe(FutureMono.java:122)
	at reactor.netty.FutureMono$ImmediateFutureMono.subscribe(FutureMono.java:83)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onComplete(MonoPeekTerminal.java:299)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onComplete(MonoIgnoreElements.java:89)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onComplete(FluxConcatMapNoPrefetch.java:241)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onComplete(FluxPeekFuseable.java:277)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2573)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.request(FluxPeekFuseable.java:144)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onSubscribe(MonoIgnoreElements.java:72)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onSubscribe(FluxPeekFuseable.java:178)
	at reactor.core.publisher.FluxJust.subscribe(FluxJust.java:68)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onError(MonoIgnoreElements.java:84)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.FluxFilter$FilterSubscriber.onError(FluxFilter.java:157)
	at reactor.core.publisher.FluxFilter$FilterConditionalSubscriber.onError(FluxFilter.java:291)
	at reactor.core.publisher.FluxMap$MapConditionalSubscriber.onError(FluxMap.java:265)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
	at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxUsingWhen$UsingWhenSubscriber.onComplete(FluxUsingWhen.java:389)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.checkTerminated(FluxFlatMap.java:850)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.drainLoop(FluxFlatMap.java:612)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.innerComplete(FluxFlatMap.java:898)
	at reactor.core.publisher.FluxFlatMap$FlatMapInner.onComplete(FluxFlatMap.java:1001)
	at reactor.core.publisher.FluxHandle$HandleSubscriber.onComplete(FluxHandle.java:223)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyInner.onComplete(MonoFlatMapMany.java:261)
	at reactor.core.publisher.FluxHandleFuseable$HandleFuseableSubscriber.onComplete(FluxHandleFuseable.java:239)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onComplete(FluxFilterFuseable.java:391)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onComplete(FluxContextWrite.java:126)
	at reactor.core.publisher.FluxPeekFuseable$PeekConditionalSubscriber.onComplete(FluxPeekFuseable.java:940)
	at reactor.core.publisher.FluxPeekFuseable$PeekConditionalSubscriber.onComplete(FluxPeekFuseable.java:940)
	at io.r2dbc.postgresql.util.FluxDiscardOnCancel$FluxDiscardOnCancelSubscriber.onComplete(FluxDiscardOnCancel.java:104)
	at reactor.core.publisher.FluxDoFinally$DoFinallySubscriber.onComplete(FluxDoFinally.java:128)
	at reactor.core.publisher.FluxHandle$HandleSubscriber.onComplete(FluxHandle.java:223)
	at reactor.core.publisher.FluxCreate$BaseSink.complete(FluxCreate.java:465)
	at reactor.core.publisher.FluxCreate$BufferAsyncSink.drain(FluxCreate.java:871)
	at reactor.core.publisher.FluxCreate$BufferAsyncSink.complete(FluxCreate.java:819)
	at reactor.core.publisher.FluxCreate$SerializedFluxSink.drainLoop(FluxCreate.java:249)
	at reactor.core.publisher.FluxCreate$SerializedFluxSink.drain(FluxCreate.java:215)
	at reactor.core.publisher.FluxCreate$SerializedFluxSink.complete(FluxCreate.java:206)
	at io.r2dbc.postgresql.client.ReactorNettyClient$Conversation.complete(ReactorNettyClient.java:680)
	at io.r2dbc.postgresql.client.ReactorNettyClient$BackendMessageSubscriber.emit(ReactorNettyClient.java:946)
	at io.r2dbc.postgresql.client.ReactorNettyClient$BackendMessageSubscriber.onNext(ReactorNettyClient.java:822)
	at io.r2dbc.postgresql.client.ReactorNettyClient$BackendMessageSubscriber.onNext(ReactorNettyClient.java:728)
	at reactor.core.publisher.FluxHandle$HandleSubscriber.onNext(FluxHandle.java:129)
	at reactor.core.publisher.FluxPeekFuseable$PeekConditionalSubscriber.onNext(FluxPeekFuseable.java:854)
	at reactor.core.publisher.FluxMap$MapConditionalSubscriber.onNext(FluxMap.java:224)
	at reactor.core.publisher.FluxMap$MapConditionalSubscriber.onNext(FluxMap.java:224)
	at reactor.netty.channel.FluxReceive.drainReceiver(FluxReceive.java:292)
	at reactor.netty.channel.FluxReceive.onInboundNext(FluxReceive.java:401)
	at reactor.netty.channel.ChannelOperations.onInboundNext(ChannelOperations.java:435)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-04 09:57:13.377 [reactor-tcp-epoll-3] ERROR v.o.t.s.i.TransactionOldServiceImpl - Error call api approveOrRejectMultipleTransaction: 
java.lang.NullPointerException: Cannot invoke "vn.onepay.transaction.dto.TransactionDetailResponse.getTransactionInformation()" because "detail" is null
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.normalizeTransactionStatuses(TransactionOldServiceImpl.java:643)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.getTransactionDetail(TransactionOldServiceImpl.java:639)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.approveOrRejectMultipleTransaction(TransactionOldServiceImpl.java:1924)
	at vn.onepay.transaction.controller.TransactionController.lambda$16(TransactionController.java:379)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.Operators$MonoInnerProducerBase.complete(Operators.java:2864)
	at reactor.core.publisher.MonoSingle$SingleSubscriber.onComplete(MonoSingle.java:180)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onComplete(Operators.java:2231)
	at reactor.core.publisher.FluxUsingWhen$UsingWhenSubscriber.deferredComplete(FluxUsingWhen.java:397)
	at reactor.core.publisher.FluxUsingWhen$CommitInner.onComplete(FluxUsingWhen.java:532)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onComplete(Operators.java:2231)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:210)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:239)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:210)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.netty.FutureMono.doSubscribe(FutureMono.java:122)
	at reactor.netty.FutureMono$ImmediateFutureMono.subscribe(FutureMono.java:83)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onComplete(MonoPeekTerminal.java:299)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onComplete(MonoIgnoreElements.java:89)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onComplete(FluxConcatMapNoPrefetch.java:241)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onComplete(FluxPeekFuseable.java:277)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2573)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.request(FluxPeekFuseable.java:144)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onSubscribe(MonoIgnoreElements.java:72)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onSubscribe(FluxPeekFuseable.java:178)
	at reactor.core.publisher.FluxJust.subscribe(FluxJust.java:68)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onError(MonoIgnoreElements.java:84)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.FluxFilter$FilterSubscriber.onError(FluxFilter.java:157)
	at reactor.core.publisher.FluxFilter$FilterConditionalSubscriber.onError(FluxFilter.java:291)
	at reactor.core.publisher.FluxMap$MapConditionalSubscriber.onError(FluxMap.java:265)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
	at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxUsingWhen$UsingWhenSubscriber.onComplete(FluxUsingWhen.java:389)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.checkTerminated(FluxFlatMap.java:850)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.drainLoop(FluxFlatMap.java:612)
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.innerComplete(FluxFlatMap.java:898)
	at reactor.core.publisher.FluxFlatMap$FlatMapInner.onComplete(FluxFlatMap.java:1001)
	at reactor.core.publisher.FluxHandle$HandleSubscriber.onComplete(FluxHandle.java:223)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyInner.onComplete(MonoFlatMapMany.java:261)
	at reactor.core.publisher.FluxHandleFuseable$HandleFuseableSubscriber.onComplete(FluxHandleFuseable.java:239)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onComplete(FluxFilterFuseable.java:391)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onComplete(FluxContextWrite.java:126)
	at reactor.core.publisher.FluxPeekFuseable$PeekConditionalSubscriber.onComplete(FluxPeekFuseable.java:940)
	at reactor.core.publisher.FluxPeekFuseable$PeekConditionalSubscriber.onComplete(FluxPeekFuseable.java:940)
	at io.r2dbc.postgresql.util.FluxDiscardOnCancel$FluxDiscardOnCancelSubscriber.onComplete(FluxDiscardOnCancel.java:104)
	at reactor.core.publisher.FluxDoFinally$DoFinallySubscriber.onComplete(FluxDoFinally.java:128)
	at reactor.core.publisher.FluxHandle$HandleSubscriber.onComplete(FluxHandle.java:223)
	at reactor.core.publisher.FluxCreate$BaseSink.complete(FluxCreate.java:465)
	at reactor.core.publisher.FluxCreate$BufferAsyncSink.drain(FluxCreate.java:871)
	at reactor.core.publisher.FluxCreate$BufferAsyncSink.complete(FluxCreate.java:819)
	at reactor.core.publisher.FluxCreate$SerializedFluxSink.drainLoop(FluxCreate.java:249)
	at reactor.core.publisher.FluxCreate$SerializedFluxSink.drain(FluxCreate.java:215)
	at reactor.core.publisher.FluxCreate$SerializedFluxSink.complete(FluxCreate.java:206)
	at io.r2dbc.postgresql.client.ReactorNettyClient$Conversation.complete(ReactorNettyClient.java:680)
	at io.r2dbc.postgresql.client.ReactorNettyClient$BackendMessageSubscriber.emit(ReactorNettyClient.java:946)
	at io.r2dbc.postgresql.client.ReactorNettyClient$BackendMessageSubscriber.onNext(ReactorNettyClient.java:822)
	at io.r2dbc.postgresql.client.ReactorNettyClient$BackendMessageSubscriber.onNext(ReactorNettyClient.java:728)
	at reactor.core.publisher.FluxHandle$HandleSubscriber.onNext(FluxHandle.java:129)
	at reactor.core.publisher.FluxPeekFuseable$PeekConditionalSubscriber.onNext(FluxPeekFuseable.java:854)
	at reactor.core.publisher.FluxMap$MapConditionalSubscriber.onNext(FluxMap.java:224)
	at reactor.core.publisher.FluxMap$MapConditionalSubscriber.onNext(FluxMap.java:224)
	at reactor.netty.channel.FluxReceive.drainReceiver(FluxReceive.java:292)
	at reactor.netty.channel.FluxReceive.onInboundNext(FluxReceive.java:401)
	at reactor.netty.channel.ChannelOperations.onInboundNext(ChannelOperations.java:435)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-04 09:57:13.386 [reactor-tcp-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api approveOrRejectMultipleTransaction MA------------
2025-08-04 09:57:13.391 [reactor-tcp-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Response: PATCH http://localhost:8597/ma-service/api/v1/transactions/transaction/multiple/approve-reject - Status: 200 OK
2025-08-04 10:43:49.580 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 3259664 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-08-04 10:43:49.582 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-08-04 10:43:50.403 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-08-04 10:43:50.584 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 174 ms. Found 2 R2DBC repository interfaces.
2025-08-04 10:43:52.276 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-08-04 10:43:52.612 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@156cfa20
2025-08-04 10:43:52.614 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-08-04 10:43:52.696 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-08-04 10:43:52.839 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@56f9de3b
2025-08-04 10:43:52.839 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-08-04 10:43:52.966 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-08-04 10:43:52.975 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-08-04 10:43:52.978 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-08-04 10:43:52.980 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-08-04 10:43:52.983 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-08-04 10:43:52.984 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-08-04 10:43:55.976 [main] WARN  o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-08-04 10:43:55.978 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Shutdown initiated...
2025-08-04 10:43:55.990 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Shutdown completed.
2025-08-04 10:43:55.990 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Shutdown initiated...
2025-08-04 10:43:56.139 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Shutdown completed.
2025-08-04 10:43:56.150 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-04 10:43:56.166 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8591 was already in use.

Action:

Identify and stop the process that's listening on port 8591 or configure this application to listen on another port.

2025-08-04 10:44:38.390 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 3260270 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-08-04 10:44:38.392 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-08-04 10:44:39.220 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-08-04 10:44:39.408 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 180 ms. Found 2 R2DBC repository interfaces.
2025-08-04 10:44:41.068 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-08-04 10:44:41.384 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@592a1882
2025-08-04 10:44:41.386 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-08-04 10:44:41.454 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-08-04 10:44:41.608 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@4ae49387
2025-08-04 10:44:41.609 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-08-04 10:44:41.738 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-08-04 10:44:41.748 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-08-04 10:44:41.751 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-08-04 10:44:41.752 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-08-04 10:44:41.755 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-08-04 10:44:41.756 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-08-04 10:44:42.713 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8592 (http)
2025-08-04 10:44:42.925 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 5.22 seconds (process running for 6.777)
2025-08-04 10:44:55.477 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://localhost:8592/ma-service/api/v1/transactions/transaction/detail?transactionId=PAY-KT6ob050SUKJlYiCI6VZXg&transactionType=Purchase&paymentMethod=BNPL
2025-08-04 10:44:55.478 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-08-04 10:44:55.478 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-08-04 10:44:55.478 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-08-04 10:44:55.478 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-08-04 10:44:55.478 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-08-04 10:44:55.478 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-08-04 10:44:55.478 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-08-04 10:44:55.478 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-08-04 10:44:55.478 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-08-04 10:44:55.478 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-08-04 10:44:55.478 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-08-04 10:44:55.478 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-08-04 10:44:55.478 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-08-04 10:44:55.478 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-08-04 10:44:55.478 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-08-04 10:44:55.479 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1*********; _ga_D5QNXXJGL1=GS2.2.s1*********$o1$g0$t1*********$j60$l0$h0; JSESSIONID=dummy; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ1ODc1NjIsInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.hEjfsrAg9gqUPMiXcjcugUJwwz7RVYeT8g3lnbyOc2Y
2025-08-04 10:44:55.479 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-04 10:44:55.479 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 1d33f168-efec-4220-8a08-a8cc805832fa
2025-08-04 10:44:55.479 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8592
2025-08-04 10:44:55.479 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-08-04 10:44:55.482 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
