2025-07-08 10:26:45.938 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 3773818 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-07-08 10:26:45.940 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-07-08 10:26:46.667 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-07-08 10:26:46.851 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 169 ms. Found 2 R2DBC repository interfaces.
2025-07-08 10:26:48.340 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-07-08 10:26:48.621 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@176333ee
2025-07-08 10:26:48.623 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-07-08 10:26:48.666 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-07-08 10:26:48.785 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@77c692b4
2025-07-08 10:26:48.786 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-07-08 10:26:48.830 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-07-08 10:26:48.838 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-07-08 10:26:48.840 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-07-08 10:26:48.841 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-07-08 10:26:48.843 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-07-08 10:26:48.844 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-07-08 10:26:49.696 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8592 (http)
2025-07-08 10:26:49.900 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 4.612 seconds (process running for 5.578)
2025-07-08 10:33:45.171 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 3776495 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-07-08 10:33:45.173 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-07-08 10:33:45.932 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-07-08 10:33:46.124 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 179 ms. Found 2 R2DBC repository interfaces.
2025-07-08 10:33:47.581 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-07-08 10:33:47.862 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@7c4d1c7b
2025-07-08 10:33:47.864 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-07-08 10:33:47.905 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-07-08 10:33:48.014 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@53f495f0
2025-07-08 10:33:48.014 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-07-08 10:33:48.059 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-07-08 10:33:48.067 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-07-08 10:33:48.070 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-07-08 10:33:48.070 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-07-08 10:33:48.072 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-07-08 10:33:48.073 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-07-08 10:33:48.950 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8592 (http)
2025-07-08 10:33:49.131 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 4.657 seconds (process running for 5.545)
2025-07-08 10:34:09.161 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-08 10:34:09.161 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-08 10:34:09.162 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-08 10:34:09.162 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-08 10:34:09.162 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-08 10:34:09.162 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-08 10:34:09.162 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-08 10:34:09.162 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-08 10:34:09.162 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-08 10:34:09.162 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-08 10:34:09.162 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-08 10:34:09.162 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-08 10:34:09.163 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-08 10:34:09.163 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-08 10:34:09.163 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-08 10:34:09.163 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-08 10:34:09.163 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-08 10:34:09.163 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-08 10:34:09.163 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-08 10:34:09.163 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:34:09.163 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = c596181a-b2e4-4b00-acc5-368386f3371c
2025-07-08 10:34:09.163 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8592
2025-07-08 10:34:09.163 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-08 10:34:09.164 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 324
2025-07-08 10:34:09.168 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-08 10:34:09.178 [reactor-http-epoll-3] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: Actual request host must not be null
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:34:09.203 [reactor-http-epoll-3] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [10d55c74-1]  500 Server Error for HTTP PATCH "/transaction/capture/********"
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP PATCH "/ma-service/api/v1/transactions/transaction/capture/********" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:35:37.422 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-08 10:35:37.423 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-08 10:35:37.423 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-08 10:35:37.423 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-08 10:35:37.423 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-08 10:35:37.423 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-08 10:35:37.423 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-08 10:35:37.423 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-08 10:35:37.423 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-08 10:35:37.423 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-08 10:35:37.423 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-08 10:35:37.424 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-08 10:35:37.424 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-08 10:35:37.424 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-08 10:35:37.424 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-08 10:35:37.424 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-08 10:35:37.424 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-08 10:35:37.424 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-08 10:35:37.424 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-08 10:35:37.424 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:35:37.424 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 39fa258f-2d22-4f32-bef7-0f84e9248689
2025-07-08 10:35:37.424 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8592
2025-07-08 10:35:37.424 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-08 10:35:37.424 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 324
2025-07-08 10:35:37.425 [reactor-http-epoll-4] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-08 10:35:37.425 [reactor-http-epoll-4] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: Actual request host must not be null
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:35:37.428 [reactor-http-epoll-4] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [b6971451-2]  500 Server Error for HTTP PATCH "/transaction/capture/********"
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP PATCH "/ma-service/api/v1/transactions/transaction/capture/********" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:35:39.437 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-08 10:35:39.437 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-08 10:35:39.437 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-08 10:35:39.437 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-08 10:35:39.437 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-08 10:35:39.437 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-08 10:35:39.437 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-08 10:35:39.437 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-08 10:35:39.437 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-08 10:35:39.437 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-08 10:35:39.437 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-08 10:35:39.438 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-08 10:35:39.438 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-08 10:35:39.438 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-08 10:35:39.438 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-08 10:35:39.438 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-08 10:35:39.438 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-08 10:35:39.438 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-08 10:35:39.438 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-08 10:35:39.438 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:35:39.438 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 25e123a1-c731-4a12-8279-100ec9314faa
2025-07-08 10:35:39.438 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8592
2025-07-08 10:35:39.438 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-08 10:35:39.438 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 324
2025-07-08 10:35:39.439 [reactor-http-epoll-4] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-08 10:35:39.439 [reactor-http-epoll-4] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: Actual request host must not be null
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:35:39.441 [reactor-http-epoll-4] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [b6971451-3]  500 Server Error for HTTP PATCH "/transaction/capture/********"
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP PATCH "/ma-service/api/v1/transactions/transaction/capture/********" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:35:54.964 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-08 10:35:54.965 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-08 10:35:54.965 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-08 10:35:54.965 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-08 10:35:54.965 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-08 10:35:54.965 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-08 10:35:54.965 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-08 10:35:54.965 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-08 10:35:54.965 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-08 10:35:54.965 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-08 10:35:54.965 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-08 10:35:54.965 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-08 10:35:54.965 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-08 10:35:54.966 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-08 10:35:54.966 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-08 10:35:54.966 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-08 10:35:54.966 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-08 10:35:54.966 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-08 10:35:54.966 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-08 10:35:54.966 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:35:54.966 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = bf7bcf5d-751f-40b5-b71b-cc4b50b45a20
2025-07-08 10:35:54.966 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8592
2025-07-08 10:35:54.966 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-08 10:35:54.966 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 324
2025-07-08 10:35:54.966 [reactor-http-epoll-4] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-08 10:35:54.967 [reactor-http-epoll-4] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: Actual request host must not be null
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:35:54.968 [reactor-http-epoll-4] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [b6971451-4]  500 Server Error for HTTP PATCH "/transaction/capture/********"
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP PATCH "/ma-service/api/v1/transactions/transaction/capture/********" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:35:59.093 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-08 10:35:59.094 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-08 10:35:59.094 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-08 10:35:59.094 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-08 10:35:59.094 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-08 10:35:59.094 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-08 10:35:59.094 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-08 10:35:59.095 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-08 10:35:59.095 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-08 10:35:59.095 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-08 10:35:59.095 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-08 10:35:59.095 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-08 10:35:59.095 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-08 10:35:59.095 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-08 10:35:59.095 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-08 10:35:59.095 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-08 10:35:59.095 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-08 10:35:59.095 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-08 10:35:59.096 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-08 10:35:59.096 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:35:59.096 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = fa9a12dd-ad4d-4baa-8342-08977e857ca6
2025-07-08 10:35:59.096 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8592
2025-07-08 10:35:59.096 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-08 10:35:59.096 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 324
2025-07-08 10:35:59.096 [reactor-http-epoll-4] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-08 10:35:59.097 [reactor-http-epoll-4] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: Actual request host must not be null
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:35:59.098 [reactor-http-epoll-4] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [b6971451-5]  500 Server Error for HTTP PATCH "/transaction/capture/********"
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP PATCH "/ma-service/api/v1/transactions/transaction/capture/********" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:36:18.415 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.k.l.KafkaMessageListenerContainer - download-task-group: Consumer stopped
2025-07-08 10:36:18.415 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.k.l.KafkaMessageListenerContainer - download-task-group: Consumer stopped
2025-07-08 10:36:18.418 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.netty.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-08 10:36:18.421 [netty-shutdown] INFO  o.s.b.w.e.netty.GracefulShutdown - Graceful shutdown complete
2025-07-08 10:36:20.444 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Shutdown initiated...
2025-07-08 10:36:20.458 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Shutdown completed.
2025-07-08 10:36:20.459 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Shutdown initiated...
2025-07-08 10:36:23.589 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 3777948 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-07-08 10:36:23.591 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-07-08 10:36:24.318 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-07-08 10:36:24.506 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 176 ms. Found 2 R2DBC repository interfaces.
2025-07-08 10:36:26.019 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-07-08 10:36:26.369 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@176333ee
2025-07-08 10:36:26.372 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-07-08 10:36:26.422 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-07-08 10:36:26.544 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@77c692b4
2025-07-08 10:36:26.544 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-07-08 10:36:26.594 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-07-08 10:36:26.603 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-07-08 10:36:26.605 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-07-08 10:36:26.606 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-07-08 10:36:26.608 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-07-08 10:36:26.609 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-07-08 10:36:27.486 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8592 (http)
2025-07-08 10:36:27.694 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 4.784 seconds (process running for 5.786)
2025-07-08 10:36:39.782 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-08 10:36:39.783 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-08 10:36:39.783 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-08 10:36:39.783 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-08 10:36:39.783 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-08 10:36:39.783 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-08 10:36:39.783 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-08 10:36:39.783 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-08 10:36:39.783 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-08 10:36:39.784 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-08 10:36:39.784 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-08 10:36:39.784 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-08 10:36:39.784 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-08 10:36:39.784 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-08 10:36:39.784 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-08 10:36:39.784 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-08 10:36:39.784 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-08 10:36:39.784 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-08 10:36:39.784 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-08 10:36:39.784 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:36:39.784 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = c61d6f8a-511c-46a0-93e0-56453ccf990e
2025-07-08 10:36:39.784 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8592
2025-07-08 10:36:39.784 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-08 10:36:39.784 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 324
2025-07-08 10:36:39.788 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-08 10:36:39.794 [reactor-http-epoll-3] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: Actual request host must not be null
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:36:39.815 [reactor-http-epoll-3] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [3dce1ada-1]  500 Server Error for HTTP PATCH "/transaction/capture/********"
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP PATCH "/ma-service/api/v1/transactions/transaction/capture/********" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:36:43.678 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-08 10:36:43.679 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-08 10:36:43.679 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-08 10:36:43.679 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-08 10:36:43.679 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-08 10:36:43.679 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-08 10:36:43.679 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-08 10:36:43.679 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-08 10:36:43.679 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-08 10:36:43.680 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-08 10:36:43.680 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-08 10:36:43.680 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-08 10:36:43.680 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-08 10:36:43.680 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-08 10:36:43.680 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-08 10:36:43.680 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-08 10:36:43.680 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-08 10:36:43.680 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-08 10:36:43.680 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-08 10:36:43.680 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:36:43.680 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 9e65738e-695e-41db-8949-196162cbfc37
2025-07-08 10:36:43.681 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8592
2025-07-08 10:36:43.681 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-08 10:36:43.681 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 324
2025-07-08 10:36:43.681 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-08 10:36:43.681 [reactor-http-epoll-3] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: Actual request host must not be null
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:36:43.683 [reactor-http-epoll-3] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [3dce1ada-2]  500 Server Error for HTTP PATCH "/transaction/capture/********"
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP PATCH "/ma-service/api/v1/transactions/transaction/capture/********" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:36:56.960 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-08 10:36:56.960 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-08 10:36:56.961 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-08 10:36:56.961 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-08 10:36:56.961 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-08 10:36:56.961 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-08 10:36:56.961 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-08 10:36:56.961 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-08 10:36:56.961 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-08 10:36:56.961 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-08 10:36:56.961 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-08 10:36:56.961 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-08 10:36:56.961 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-08 10:36:56.962 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-08 10:36:56.962 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-08 10:36:56.962 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-08 10:36:56.962 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-08 10:36:56.962 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-08 10:36:56.962 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-08 10:36:56.962 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:36:56.962 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 100215ed-4bd1-46f4-8aa3-49887fbdec76
2025-07-08 10:36:56.962 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8592
2025-07-08 10:36:56.962 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-08 10:36:56.963 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 324
2025-07-08 10:36:56.963 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-08 10:36:56.963 [reactor-http-epoll-3] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: Actual request host must not be null
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:36:56.965 [reactor-http-epoll-3] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [3dce1ada-3]  500 Server Error for HTTP PATCH "/transaction/capture/********"
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP PATCH "/ma-service/api/v1/transactions/transaction/capture/********" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:37:50.813 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 3778573 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-07-08 10:37:50.815 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-07-08 10:37:51.553 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-07-08 10:37:51.749 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 181 ms. Found 2 R2DBC repository interfaces.
2025-07-08 10:37:53.287 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-07-08 10:37:53.592 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@1b1e1f02
2025-07-08 10:37:53.594 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-07-08 10:37:53.638 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-07-08 10:37:53.753 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@26463a6
2025-07-08 10:37:53.754 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-07-08 10:37:53.804 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-07-08 10:37:53.812 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-07-08 10:37:53.814 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-07-08 10:37:53.815 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-07-08 10:37:53.817 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-07-08 10:37:53.818 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-07-08 10:37:54.636 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8592 (http)
2025-07-08 10:37:54.828 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 4.715 seconds (process running for 5.747)
2025-07-08 10:38:01.839 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-08 10:38:01.840 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-08 10:38:01.840 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-08 10:38:01.840 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-08 10:38:01.840 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-08 10:38:01.840 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-08 10:38:01.840 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-08 10:38:01.840 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-08 10:38:01.840 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-08 10:38:01.840 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-08 10:38:01.840 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-08 10:38:01.840 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-08 10:38:01.840 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-08 10:38:01.841 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-08 10:38:01.841 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-08 10:38:01.841 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-08 10:38:01.841 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-08 10:38:01.841 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-08 10:38:01.841 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-08 10:38:01.841 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:38:01.841 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 099291b6-68f0-495b-9bf8-f3a922b179cd
2025-07-08 10:38:01.841 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8592
2025-07-08 10:38:01.841 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-08 10:38:01.841 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 324
2025-07-08 10:38:01.845 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-08 10:38:01.853 [reactor-http-epoll-3] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: Actual request host must not be null
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:38:01.874 [reactor-http-epoll-3] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [e0951277-1]  500 Server Error for HTTP PATCH "/transaction/capture/********"
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP PATCH "/ma-service/api/v1/transactions/transaction/capture/********" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:38:38.931 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://:8592/ma-service/api/v1/transactions/transaction/detail?transactionId=864066&paymentMethod=&transactionType=Request%20Refund
2025-07-08 10:38:38.931 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-07-08 10:38:38.932 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-08 10:38:38.932 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-08 10:38:38.932 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-08 10:38:38.932 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-08 10:38:38.932 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-08 10:38:38.932 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-07-08 10:38:38.932 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-08 10:38:38.932 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-08 10:38:38.932 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-08 10:38:38.932 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-08 10:38:38.932 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-08 10:38:38.933 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-08 10:38:38.933 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-08 10:38:38.933 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-08 10:38:38.933 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************.H-3YlljJ1u6p43JXCfymfAgUKe8CNBljm5TBDIZ_hec
2025-07-08 10:38:38.933 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:38:38.933 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = dedba20b-f4d8-4374-bf5d-cc2d02b152cb
2025-07-08 10:38:38.933 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8592
2025-07-08 10:38:38.933 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-08 10:38:38.933 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-08 10:38:45.576 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=864066 ,paymentMethod= ,transactionType=Request Refund ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:38:45.577 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - Dont have payment method: 
2025-07-08 10:38:45.577 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-08 10:38:46.765 [reactor-http-epoll-3] ERROR v.o.t.e.GlobalExceptionHandler - Unhandled exception: 
org.springframework.web.server.ResponseStatusException: 404 NOT_FOUND "Transaction ID not found"
	at vn.onepay.transaction.controller.TransactionController.getTransactionDetail(TransactionController.java:149)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#getTransactionDetail(ServerWebExchange, String, String, String, String) [DispatcherHandler]
Original Stack Trace:
		at vn.onepay.transaction.controller.TransactionController.getTransactionDetail(TransactionController.java:149)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onComplete(FluxDefaultIfEmpty.java:134)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:85)
		at reactor.core.publisher.MonoSupplier$MonoSupplierSubscription.request(MonoSupplier.java:148)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
		at reactor.core.publisher.MonoSupplier.subscribe(MonoSupplier.java:48)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:82)
		at reactor.core.publisher.Operators.complete(Operators.java:137)
		at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:146)
		at reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onSubscribe(MonoIgnoreThen.java:135)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onComplete(MonoFlatMap.java:189)
		at reactor.core.publisher.Operators.complete(Operators.java:137)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:121)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:259)
		at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:865)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.request(FluxMapFuseable.java:171)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:339)
		at reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
		at reactor.core.publisher.MonoNext$NextSubscriber.onSubscribe(MonoNext.java:70)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:201)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:83)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:38:46.768 [reactor-http-epoll-3] WARN  o.s.w.r.r.m.a.RequestMappingHandlerAdapter - [e0951277-2] Failure in @ExceptionHandler vn.onepay.transaction.exception.GlobalExceptionHandler#handleNotFound(ResponseStatusException, ServerWebExchange)
org.springframework.web.server.ResponseStatusException: 404 NOT_FOUND "Not found"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleNotFound(GlobalExceptionHandler.java:23)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
	at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onSubscribe(MonoPeekTerminal.java:152)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:136)
	at reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onNext(FluxPeek.java:200)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.complete(MonoIgnoreThen.java:294)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onNext(MonoIgnoreThen.java:188)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onComplete(FluxDefaultIfEmpty.java:134)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:85)
	at reactor.core.publisher.MonoSupplier$MonoSupplierSubscription.request(MonoSupplier.java:148)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
	at reactor.core.publisher.MonoSupplier.subscribe(MonoSupplier.java:48)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:82)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:146)
	at reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onSubscribe(MonoIgnoreThen.java:135)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onComplete(MonoFlatMap.java:189)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:121)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
	at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:259)
	at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:865)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.request(FluxMapFuseable.java:171)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:339)
	at reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
	at reactor.core.publisher.MonoNext$NextSubscriber.onSubscribe(MonoNext.java:70)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:201)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:83)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
	at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
	at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
	at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
	at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:38:46.769 [reactor-http-epoll-3] ERROR v.o.t.exception.RequestLoggingFilter - Request error: GET http://:8592/ma-service/api/v1/transactions/transaction/detail?transactionId=864066&paymentMethod=&transactionType=Request%20Refund - Exception: 404 NOT_FOUND "Transaction ID not found"
org.springframework.web.server.ResponseStatusException: 404 NOT_FOUND "Transaction ID not found"
	at vn.onepay.transaction.controller.TransactionController.getTransactionDetail(TransactionController.java:149)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#getTransactionDetail(ServerWebExchange, String, String, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x00007419805bd688 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at vn.onepay.transaction.controller.TransactionController.getTransactionDetail(TransactionController.java:149)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onComplete(FluxDefaultIfEmpty.java:134)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:85)
		at reactor.core.publisher.MonoSupplier$MonoSupplierSubscription.request(MonoSupplier.java:148)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
		at reactor.core.publisher.MonoSupplier.subscribe(MonoSupplier.java:48)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:82)
		at reactor.core.publisher.Operators.complete(Operators.java:137)
		at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:146)
		at reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onSubscribe(MonoIgnoreThen.java:135)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onComplete(MonoFlatMap.java:189)
		at reactor.core.publisher.Operators.complete(Operators.java:137)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:121)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:259)
		at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:865)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.request(FluxMapFuseable.java:171)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:339)
		at reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
		at reactor.core.publisher.MonoNext$NextSubscriber.onSubscribe(MonoNext.java:70)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:201)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:83)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:38:55.564 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-08 10:38:55.564 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-08 10:38:55.564 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-08 10:38:55.564 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-08 10:38:55.564 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-08 10:38:55.564 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-08 10:38:55.565 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-08 10:38:55.565 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-08 10:38:55.565 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-08 10:38:55.565 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-08 10:38:55.565 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-08 10:38:55.565 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-08 10:38:55.565 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-08 10:38:55.565 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-08 10:38:55.565 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-08 10:38:55.565 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-08 10:38:55.565 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-08 10:38:55.565 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-08 10:38:55.565 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-08 10:38:55.565 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:38:55.565 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 2e510acd-4e0e-4987-a41b-c696e4805ce8
2025-07-08 10:38:55.565 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8592
2025-07-08 10:38:55.565 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-08 10:38:55.565 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 324
2025-07-08 10:38:55.566 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-08 10:38:55.566 [reactor-http-epoll-3] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: Actual request host must not be null
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:38:55.567 [reactor-http-epoll-3] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [e0951277-3]  500 Server Error for HTTP PATCH "/transaction/capture/********"
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP PATCH "/ma-service/api/v1/transactions/transaction/capture/********" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:40:02.257 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-08 10:40:02.257 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-08 10:40:02.258 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-08 10:40:02.258 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-08 10:40:02.258 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-08 10:40:02.258 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-08 10:40:02.258 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-08 10:40:02.258 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-08 10:40:02.258 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-08 10:40:02.258 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-08 10:40:02.258 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-08 10:40:02.258 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-08 10:40:02.258 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-08 10:40:02.258 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-08 10:40:02.258 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-08 10:40:02.258 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-08 10:40:02.258 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-08 10:40:02.258 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-08 10:40:02.258 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-08 10:40:02.258 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:40:02.259 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = c062be5f-2646-4649-b83e-e88cb6db421e
2025-07-08 10:40:02.259 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8592
2025-07-08 10:40:02.259 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-08 10:40:02.259 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 324
2025-07-08 10:40:02.259 [reactor-http-epoll-4] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-08 10:40:02.259 [reactor-http-epoll-4] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: Actual request host must not be null
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:40:02.260 [reactor-http-epoll-4] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [ae55bad6-4]  500 Server Error for HTTP PATCH "/transaction/capture/********"
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP PATCH "/ma-service/api/v1/transactions/transaction/capture/********" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:40:09.158 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-08 10:40:09.158 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-08 10:40:09.158 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-08 10:40:09.158 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-08 10:40:09.158 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-08 10:40:09.158 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-08 10:40:09.159 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-08 10:40:09.159 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-08 10:40:09.159 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-08 10:40:09.159 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-08 10:40:09.159 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-08 10:40:09.159 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-08 10:40:09.159 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-08 10:40:09.159 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-08 10:40:09.159 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-08 10:40:09.159 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-08 10:40:09.159 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-08 10:40:09.159 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-08 10:40:09.159 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-08 10:40:09.159 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:40:09.159 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 29d42ab8-5376-457c-8486-73a87dbcdef9
2025-07-08 10:40:09.159 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8592
2025-07-08 10:40:09.159 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-08 10:40:09.159 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 324
2025-07-08 10:40:09.160 [reactor-http-epoll-4] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-08 10:40:09.160 [reactor-http-epoll-4] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: Actual request host must not be null
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:40:09.161 [reactor-http-epoll-4] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [ae55bad6-5]  500 Server Error for HTTP PATCH "/transaction/capture/********"
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP PATCH "/ma-service/api/v1/transactions/transaction/capture/********" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:40:20.810 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 3779581 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-07-08 10:40:20.812 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-07-08 10:40:21.554 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-07-08 10:40:21.733 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 172 ms. Found 2 R2DBC repository interfaces.
2025-07-08 10:40:23.186 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-07-08 10:40:23.460 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@ff03361
2025-07-08 10:40:23.462 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-07-08 10:40:23.502 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-07-08 10:40:23.610 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@4cc7e3ad
2025-07-08 10:40:23.611 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-07-08 10:40:23.658 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-07-08 10:40:23.667 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-07-08 10:40:23.669 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-07-08 10:40:23.670 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-07-08 10:40:23.672 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-07-08 10:40:23.672 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-07-08 10:40:24.498 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8592 (http)
2025-07-08 10:40:24.695 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 4.584 seconds (process running for 5.519)
2025-07-08 10:40:26.999 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-08 10:40:27.000 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-08 10:40:27.000 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-08 10:40:27.000 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-08 10:40:27.000 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-08 10:40:27.000 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-08 10:40:27.000 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-08 10:40:27.000 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-08 10:40:27.000 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-08 10:40:27.001 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-08 10:40:27.001 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-08 10:40:27.001 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-08 10:40:27.001 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-08 10:40:27.001 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-08 10:40:27.001 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-08 10:40:27.001 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-08 10:40:27.001 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-08 10:40:27.001 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-08 10:40:27.001 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-08 10:40:27.001 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:40:27.001 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = c2f22024-c1af-43de-b775-684ce2182a36
2025-07-08 10:40:27.001 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8592
2025-07-08 10:40:27.001 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-08 10:40:27.002 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 324
2025-07-08 10:40:27.005 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-08 10:40:27.013 [reactor-http-epoll-3] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: Actual request host must not be null
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:40:27.034 [reactor-http-epoll-3] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [a60f23da-1]  500 Server Error for HTTP PATCH "/transaction/capture/********"
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP PATCH "/ma-service/api/v1/transactions/transaction/capture/********" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:41:12.212 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 3779988 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-07-08 10:41:12.216 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-07-08 10:41:12.985 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-07-08 10:41:13.198 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 206 ms. Found 2 R2DBC repository interfaces.
2025-07-08 10:41:14.692 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-07-08 10:41:14.984 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@1b1e1f02
2025-07-08 10:41:14.986 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-07-08 10:41:15.028 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-07-08 10:41:15.149 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@26463a6
2025-07-08 10:41:15.149 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-07-08 10:41:15.197 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-07-08 10:41:15.205 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-07-08 10:41:15.208 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-07-08 10:41:15.209 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-07-08 10:41:15.211 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-07-08 10:41:15.213 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-07-08 10:41:16.078 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8592 (http)
2025-07-08 10:41:16.321 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 4.788 seconds (process running for 5.72)
2025-07-08 10:41:27.444 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-08 10:41:27.444 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-08 10:41:27.444 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-08 10:41:27.444 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-08 10:41:27.444 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-08 10:41:27.445 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-08 10:41:27.445 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-08 10:41:27.445 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-08 10:41:27.445 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-08 10:41:27.445 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-08 10:41:27.445 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-08 10:41:27.445 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-08 10:41:27.445 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-08 10:41:27.445 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-08 10:41:27.445 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-08 10:41:27.445 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-08 10:41:27.445 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-08 10:41:27.445 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-08 10:41:27.445 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-08 10:41:27.446 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:41:27.446 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = a21f7ec1-89f9-4ccb-94cc-d400cac03a3c
2025-07-08 10:41:27.446 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8592
2025-07-08 10:41:27.446 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-08 10:41:27.446 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 324
2025-07-08 10:41:27.449 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-08 10:41:27.456 [reactor-http-epoll-3] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: Actual request host must not be null
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:41:27.478 [reactor-http-epoll-3] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [508b3c55-1]  500 Server Error for HTTP PATCH "/transaction/capture/********"
java.lang.IllegalArgumentException: Actual request host must not be null
	at org.springframework.util.Assert.notNull(Assert.java:181)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP PATCH "/ma-service/api/v1/transactions/transaction/capture/********" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at org.springframework.util.Assert.notNull(Assert.java:181)
		at org.springframework.web.cors.reactive.CorsUtils.isSameOrigin(CorsUtils.java:81)
		at org.springframework.web.cors.reactive.CorsUtils.isCorsRequest(CorsUtils.java:44)
		at vn.onepay.transaction.config.WebCorsConfig.lambda$0(WebCorsConfig.java:23)
		at org.springframework.web.server.handler.DefaultWebFilterChain.invokeFilter(DefaultWebFilterChain.java:114)
		at org.springframework.web.server.handler.DefaultWebFilterChain.lambda$filter$0(DefaultWebFilterChain.java:108)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:455)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:42:15.640 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-08 10:42:15.641 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-08 10:42:15.641 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-08 10:42:15.641 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-08 10:42:15.641 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-08 10:42:15.641 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-08 10:42:15.641 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-08 10:42:15.641 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-08 10:42:15.641 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-08 10:42:15.641 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-08 10:42:15.642 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-08 10:42:15.642 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-08 10:42:15.642 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-08 10:42:15.642 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-08 10:42:15.642 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-08 10:42:15.642 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-08 10:42:15.642 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-08 10:42:15.642 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-08 10:42:15.642 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-08 10:42:15.642 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:42:15.642 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 89e4d886-1258-41bf-95c1-46398c076247
2025-07-08 10:42:15.642 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8592
2025-07-08 10:42:15.642 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-08 10:42:15.643 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 324
2025-07-08 10:42:15.643 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-08 10:43:23.782 [reactor-http-epoll-3] WARN  v.o.t.util.DateValidatorUtil - service not found: {
    "id": "********",
    "op": "replace",
    "path": "/capture",
    "value": {
        "merchant_id": "TESTPCI",
        "amount": 1000,
        "currency": "VND",
        "note": "",
        "user_name": "Quản trị viên hệ thống hệ thống hệ thống hệ thống"
    },
    "skipCallSynchronize": false
}
2025-07-08 10:43:23.793 [reactor-http-epoll-3] ERROR v.o.t.e.GlobalExceptionHandler - Unhandled exception: 
org.springframework.web.server.ResponseStatusException: 405 METHOD_NOT_ALLOWED "service not found"
	at vn.onepay.transaction.controller.TransactionController.captureTransaction(TransactionController.java:202)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#captureTransaction(ServerWebExchange, String, String) [DispatcherHandler]
Original Stack Trace:
		at vn.onepay.transaction.controller.TransactionController.captureTransaction(TransactionController.java:202)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:889)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:798)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:43:23.796 [reactor-http-epoll-3] WARN  o.s.w.r.r.m.a.RequestMappingHandlerAdapter - [508b3c55-2] Failure in @ExceptionHandler vn.onepay.transaction.exception.GlobalExceptionHandler#handleNotFound(ResponseStatusException, ServerWebExchange)
org.springframework.web.server.ResponseStatusException: 404 NOT_FOUND "Not found"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleNotFound(GlobalExceptionHandler.java:23)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
	at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onSubscribe(MonoPeekTerminal.java:152)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:136)
	at reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onNext(FluxPeek.java:200)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.complete(MonoIgnoreThen.java:294)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onNext(MonoIgnoreThen.java:188)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:889)
	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:798)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:43:23.797 [reactor-http-epoll-3] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: 405 METHOD_NOT_ALLOWED "service not found"
org.springframework.web.server.ResponseStatusException: 405 METHOD_NOT_ALLOWED "service not found"
	at vn.onepay.transaction.controller.TransactionController.captureTransaction(TransactionController.java:202)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#captureTransaction(ServerWebExchange, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x00007bb8445bd688 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at vn.onepay.transaction.controller.TransactionController.captureTransaction(TransactionController.java:202)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:889)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:798)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:43:44.303 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-08 10:43:44.303 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-08 10:43:44.304 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-08 10:43:44.304 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-08 10:43:44.304 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-08 10:43:44.304 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-08 10:43:44.304 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-08 10:43:44.304 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-08 10:43:44.304 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-08 10:43:44.304 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-08 10:43:44.304 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-08 10:43:44.304 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-08 10:43:44.304 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-08 10:43:44.304 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-08 10:43:44.304 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-08 10:43:44.304 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-08 10:43:44.304 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-08 10:43:44.304 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-08 10:43:44.304 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-08 10:43:44.304 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:43:44.304 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 146732b5-b2a4-46a4-96c8-b808a07aa4d2
2025-07-08 10:43:44.305 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8592
2025-07-08 10:43:44.305 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-08 10:43:44.305 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 324
2025-07-08 10:43:44.305 [reactor-http-epoll-4] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-08 10:43:49.542 [reactor-http-epoll-4] WARN  v.o.t.util.DateValidatorUtil - service not found: {
    "id": "********",
    "op": "replace",
    "path": "/capture",
    "value": {
        "merchant_id": "TESTPCI",
        "amount": 1000,
        "currency": "VND",
        "note": "",
        "user_name": "Quản trị viên hệ thống hệ thống hệ thống hệ thống"
    },
    "skipCallSynchronize": false
}
2025-07-08 10:43:49.545 [reactor-http-epoll-4] ERROR v.o.t.e.GlobalExceptionHandler - Unhandled exception: 
org.springframework.web.server.ResponseStatusException: 405 METHOD_NOT_ALLOWED "service not found"
	at vn.onepay.transaction.controller.TransactionController.captureTransaction(TransactionController.java:202)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#captureTransaction(ServerWebExchange, String, String) [DispatcherHandler]
Original Stack Trace:
		at vn.onepay.transaction.controller.TransactionController.captureTransaction(TransactionController.java:202)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:889)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:798)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:43:49.546 [reactor-http-epoll-4] WARN  o.s.w.r.r.m.a.RequestMappingHandlerAdapter - [529ccfd1-3] Failure in @ExceptionHandler vn.onepay.transaction.exception.GlobalExceptionHandler#handleNotFound(ResponseStatusException, ServerWebExchange)
org.springframework.web.server.ResponseStatusException: 404 NOT_FOUND "Not found"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleNotFound(GlobalExceptionHandler.java:23)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
	at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onSubscribe(MonoPeekTerminal.java:152)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:136)
	at reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onNext(FluxPeek.java:200)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.complete(MonoIgnoreThen.java:294)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onNext(MonoIgnoreThen.java:188)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:889)
	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:798)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:43:49.547 [reactor-http-epoll-4] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: 405 METHOD_NOT_ALLOWED "service not found"
org.springframework.web.server.ResponseStatusException: 405 METHOD_NOT_ALLOWED "service not found"
	at vn.onepay.transaction.controller.TransactionController.captureTransaction(TransactionController.java:202)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#captureTransaction(ServerWebExchange, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x00007bb8445bd688 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at vn.onepay.transaction.controller.TransactionController.captureTransaction(TransactionController.java:202)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:889)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:798)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:44:17.982 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-08 10:44:17.983 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-08 10:44:17.983 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-08 10:44:17.983 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-08 10:44:17.983 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-08 10:44:17.983 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-08 10:44:17.983 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-08 10:44:17.983 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-08 10:44:17.983 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-08 10:44:17.983 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-08 10:44:17.983 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-08 10:44:17.983 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-08 10:44:17.983 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-08 10:44:17.983 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-08 10:44:17.983 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-08 10:44:17.983 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-08 10:44:17.983 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-08 10:44:17.983 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-08 10:44:17.984 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-08 10:44:17.984 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:44:17.984 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = a9f451c2-04e9-4743-a67a-119ce2582be4
2025-07-08 10:44:17.984 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8592
2025-07-08 10:44:17.984 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-08 10:44:17.984 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 345
2025-07-08 10:44:17.984 [reactor-http-epoll-4] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-08 10:44:21.129 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:44:21.129 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-08 10:44:21.193 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=null
2025-07-08 10:44:21.302 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","transaction_id":"********","original_transaction_id":"********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"4","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1751969917209204128727","amount":{"total":123000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"531358****3430","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"26"}},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"831000"},"transaction_time":"2025-07-08T17:18:53Z","transaction_status":"0","transaction_ref_number":"6265400258051234542701","ip_address":"***************","ip_proxy":"N","bin_country":"AUSTRALIA","csc_result_code":"M","enrolled_3ds":"Y","avs":{"address":"","result_code":"Y","province":"","city":"","zip_code":"","country":""},"verification_security_level":"05","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"NATIONAL AUSTRALIA BANK LIMITED","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","status3ds":"Y","riskOverAllResult":"ACCEPT","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00"}
2025-07-08 10:44:21.303 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-08 10:44:21.305 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-08 10:44:21.312 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-08 10:44:21.329 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-08T17:18:53, orderAmount=123000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-07-08T17:18:53, transactionCompletedTime=2025-07-08T17:18:53, transactionAmount=123000.0, transactionCurrency=VND, transactionStatus=0, responseCode=0, merchantTransRef=TEST_1751969917209204128727), paymentMethod=PaymentMethod(cardNumber=531358****3430, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, cardExpiry=1126, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=AUSTRALIA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null))
2025-07-08 10:44:21.329 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-08 10:44:39.909 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-08 10:44:39.910 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-08 10:44:40.104 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-08 10:44:40.105 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - list merchant from permission: [CGV15, CGV17, CGV2, CGV4, CGV18, CGV8, CGV6, CJSHOW, CGVKIOSK, CGV, CGV13, CGV5]
2025-07-08 10:44:40.105 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-08 10:44:40.105 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: []
2025-07-08 10:44:40.106 [reactor-http-epoll-4] ERROR v.o.t.s.i.TransactionOldServiceImpl - No merchantId matched -> returning empty response
2025-07-08 10:44:40.107 [reactor-http-epoll-4] ERROR v.o.t.e.GlobalExceptionHandler - Unhandled exception: 
org.springframework.web.server.ResponseStatusException: 403 FORBIDDEN "Permission denied"
	at vn.onepay.transaction.controller.TransactionController.lambda$5(TransactionController.java:212)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#captureTransaction(ServerWebExchange, String, String) [DispatcherHandler]
Original Stack Trace:
		at vn.onepay.transaction.controller.TransactionController.lambda$5(TransactionController.java:212)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:44:40.108 [reactor-http-epoll-4] WARN  o.s.w.r.r.m.a.RequestMappingHandlerAdapter - [529ccfd1-4] Failure in @ExceptionHandler vn.onepay.transaction.exception.GlobalExceptionHandler#handleNotFound(ResponseStatusException, ServerWebExchange)
org.springframework.web.server.ResponseStatusException: 404 NOT_FOUND "Not found"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleNotFound(GlobalExceptionHandler.java:23)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
	at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onSubscribe(MonoPeekTerminal.java:152)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:149)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:44:40.108 [reactor-http-epoll-4] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: 403 FORBIDDEN "Permission denied"
org.springframework.web.server.ResponseStatusException: 403 FORBIDDEN "Permission denied"
	at vn.onepay.transaction.controller.TransactionController.lambda$5(TransactionController.java:212)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#captureTransaction(ServerWebExchange, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x00007bb8445bd688 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at vn.onepay.transaction.controller.TransactionController.lambda$5(TransactionController.java:212)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:45:08.525 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-08 10:45:08.525 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-08 10:45:08.525 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-08 10:45:08.525 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-08 10:45:08.525 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-08 10:45:08.526 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-08 10:45:08.526 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-08 10:45:08.526 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-08 10:45:08.526 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-08 10:45:08.526 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-08 10:45:08.526 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-08 10:45:08.526 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-08 10:45:08.526 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-08 10:45:08.526 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-08 10:45:08.526 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-08 10:45:08.526 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-08 10:45:08.526 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-08 10:45:08.526 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-08 10:45:08.526 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-08 10:45:08.526 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:45:08.526 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 6c429d67-e94f-492f-83ff-2b0ac4f4d54a
2025-07-08 10:45:08.526 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8592
2025-07-08 10:45:08.526 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-08 10:45:08.526 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 345
2025-07-08 10:45:08.527 [reactor-http-epoll-4] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-08 10:45:15.074 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:45:15.074 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-08 10:45:15.075 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=null
2025-07-08 10:45:15.087 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","transaction_id":"********","original_transaction_id":"********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"4","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1751969917209204128727","amount":{"total":123000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"531358****3430","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"26"}},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"831000"},"transaction_time":"2025-07-08T17:18:53Z","transaction_status":"0","transaction_ref_number":"6265400258051234542701","ip_address":"***************","ip_proxy":"N","bin_country":"AUSTRALIA","csc_result_code":"M","enrolled_3ds":"Y","avs":{"address":"","result_code":"Y","province":"","city":"","zip_code":"","country":""},"verification_security_level":"05","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"NATIONAL AUSTRALIA BANK LIMITED","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","status3ds":"Y","riskOverAllResult":"ACCEPT","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00"}
2025-07-08 10:45:15.087 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-08 10:45:15.088 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-08 10:45:15.089 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-08 10:45:15.089 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-08T17:18:53, orderAmount=123000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-07-08T17:18:53, transactionCompletedTime=2025-07-08T17:18:53, transactionAmount=123000.0, transactionCurrency=VND, transactionStatus=0, responseCode=0, merchantTransRef=TEST_1751969917209204128727), paymentMethod=PaymentMethod(cardNumber=531358****3430, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, cardExpiry=1126, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=AUSTRALIA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null))
2025-07-08 10:45:15.089 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-08 10:45:16.226 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-08 10:45:16.226 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-08 10:45:16.241 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-08 10:45:16.242 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - list merchant from permission: [CGV15, CGV17, CGV2, CGV4, CGV18, CGV8, CGV6, CJSHOW, CGVKIOSK, CGV, CGV13, CGV5]
2025-07-08 10:45:16.242 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-08 10:45:16.242 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: []
2025-07-08 10:45:16.243 [reactor-http-epoll-4] ERROR v.o.t.s.i.TransactionOldServiceImpl - No merchantId matched -> returning empty response
2025-07-08 10:45:51.152 [reactor-http-epoll-4] ERROR v.o.t.e.GlobalExceptionHandler - Unhandled exception: 
org.springframework.web.server.ResponseStatusException: 403 FORBIDDEN "Permission denied"
	at vn.onepay.transaction.controller.TransactionController.lambda$5(TransactionController.java:212)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#captureTransaction(ServerWebExchange, String, String) [DispatcherHandler]
Original Stack Trace:
		at vn.onepay.transaction.controller.TransactionController.lambda$5(TransactionController.java:212)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:45:51.154 [reactor-http-epoll-4] WARN  o.s.w.r.r.m.a.RequestMappingHandlerAdapter - [529ccfd1-5] Failure in @ExceptionHandler vn.onepay.transaction.exception.GlobalExceptionHandler#handleNotFound(ResponseStatusException, ServerWebExchange)
org.springframework.web.server.ResponseStatusException: 404 NOT_FOUND "Not found"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleNotFound(GlobalExceptionHandler.java:23)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
	at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onSubscribe(MonoPeekTerminal.java:152)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:149)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:45:51.154 [reactor-http-epoll-4] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: 403 FORBIDDEN "Permission denied"
org.springframework.web.server.ResponseStatusException: 403 FORBIDDEN "Permission denied"
	at vn.onepay.transaction.controller.TransactionController.lambda$5(TransactionController.java:212)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#captureTransaction(ServerWebExchange, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x00007bb8445bd688 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at vn.onepay.transaction.controller.TransactionController.lambda$5(TransactionController.java:212)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:45:53.026 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-08 10:45:53.026 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-08 10:45:53.026 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-08 10:45:53.026 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-08 10:45:53.026 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-08 10:45:53.026 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-08 10:45:53.026 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-08 10:45:53.026 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-08 10:45:53.026 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-08 10:45:53.026 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-08 10:45:53.026 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-08 10:45:53.026 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-08 10:45:53.027 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-08 10:45:53.027 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-08 10:45:53.027 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-08 10:45:53.027 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-08 10:45:53.027 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-08 10:45:53.027 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-08 10:45:53.027 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-08 10:45:53.027 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:45:53.027 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = f792a0ad-446c-4552-beff-aa2952a123a2
2025-07-08 10:45:53.027 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8592
2025-07-08 10:45:53.027 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-08 10:45:53.027 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 345
2025-07-08 10:45:53.027 [reactor-http-epoll-4] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-08 10:45:55.774 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:45:55.774 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-08 10:45:55.775 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=null
2025-07-08 10:45:55.794 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","transaction_id":"********","original_transaction_id":"********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"4","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1751969917209204128727","amount":{"total":123000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"531358****3430","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"26"}},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"831000"},"transaction_time":"2025-07-08T17:18:53Z","transaction_status":"0","transaction_ref_number":"6265400258051234542701","ip_address":"***************","ip_proxy":"N","bin_country":"AUSTRALIA","csc_result_code":"M","enrolled_3ds":"Y","avs":{"address":"","result_code":"Y","province":"","city":"","zip_code":"","country":""},"verification_security_level":"05","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"NATIONAL AUSTRALIA BANK LIMITED","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","status3ds":"Y","riskOverAllResult":"ACCEPT","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00"}
2025-07-08 10:45:55.794 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-08 10:45:55.795 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-08 10:45:55.796 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-08 10:45:55.796 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-08T17:18:53, orderAmount=123000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-07-08T17:18:53, transactionCompletedTime=2025-07-08T17:18:53, transactionAmount=123000.0, transactionCurrency=VND, transactionStatus=0, responseCode=0, merchantTransRef=TEST_1751969917209204128727), paymentMethod=PaymentMethod(cardNumber=531358****3430, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, cardExpiry=1126, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=AUSTRALIA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null))
2025-07-08 10:45:55.797 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-08 10:46:13.598 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-08 10:46:13.598 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-08 10:46:18.202 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-08 10:46:18.203 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - list merchant from permission: [CGV15, CGV17, CGV2, CGV4, CGV18, CGV8, CGV6, CJSHOW, CGVKIOSK, CGV, CGV13, CGV5]
2025-07-08 10:46:18.203 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-08 10:46:18.203 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: []
2025-07-08 10:47:08.492 [reactor-http-epoll-4] ERROR v.o.t.s.i.TransactionOldServiceImpl - No merchantId matched -> returning empty response
2025-07-08 10:47:10.352 [reactor-http-epoll-4] ERROR v.o.t.e.GlobalExceptionHandler - Unhandled exception: 
org.springframework.web.server.ResponseStatusException: 403 FORBIDDEN "Permission denied"
	at vn.onepay.transaction.controller.TransactionController.lambda$5(TransactionController.java:212)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#captureTransaction(ServerWebExchange, String, String) [DispatcherHandler]
Original Stack Trace:
		at vn.onepay.transaction.controller.TransactionController.lambda$5(TransactionController.java:212)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:47:10.352 [reactor-http-epoll-4] WARN  o.s.w.r.r.m.a.RequestMappingHandlerAdapter - [529ccfd1-6] Failure in @ExceptionHandler vn.onepay.transaction.exception.GlobalExceptionHandler#handleNotFound(ResponseStatusException, ServerWebExchange)
org.springframework.web.server.ResponseStatusException: 404 NOT_FOUND "Not found"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleNotFound(GlobalExceptionHandler.java:23)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
	at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onSubscribe(MonoPeekTerminal.java:152)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:149)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:47:10.353 [reactor-http-epoll-4] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: 403 FORBIDDEN "Permission denied"
org.springframework.web.server.ResponseStatusException: 403 FORBIDDEN "Permission denied"
	at vn.onepay.transaction.controller.TransactionController.lambda$5(TransactionController.java:212)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#captureTransaction(ServerWebExchange, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x00007bb8445bd688 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at vn.onepay.transaction.controller.TransactionController.lambda$5(TransactionController.java:212)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:47:11.723 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-08 10:47:11.724 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-08 10:47:11.724 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-08 10:47:11.724 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-08 10:47:11.724 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-08 10:47:11.724 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-08 10:47:11.724 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-08 10:47:11.724 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-08 10:47:11.724 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-08 10:47:11.724 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-08 10:47:11.724 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-08 10:47:11.724 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-08 10:47:11.724 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-08 10:47:11.724 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-08 10:47:11.724 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-08 10:47:11.724 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-08 10:47:11.724 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-08 10:47:11.724 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-08 10:47:11.724 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-08 10:47:11.725 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:47:11.725 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 668696a6-74ec-4f47-aeba-3f6c534aecfb
2025-07-08 10:47:11.725 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8592
2025-07-08 10:47:11.725 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-08 10:47:11.725 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 345
2025-07-08 10:47:11.725 [reactor-http-epoll-5] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-08 10:47:17.876 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:47:17.876 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-08 10:47:17.878 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=null
2025-07-08 10:47:17.889 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","transaction_id":"********","original_transaction_id":"********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"4","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1751969917209204128727","amount":{"total":123000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"531358****3430","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"26"}},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"831000"},"transaction_time":"2025-07-08T17:18:53Z","transaction_status":"0","transaction_ref_number":"6265400258051234542701","ip_address":"***************","ip_proxy":"N","bin_country":"AUSTRALIA","csc_result_code":"M","enrolled_3ds":"Y","avs":{"address":"","result_code":"Y","province":"","city":"","zip_code":"","country":""},"verification_security_level":"05","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"NATIONAL AUSTRALIA BANK LIMITED","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","status3ds":"Y","riskOverAllResult":"ACCEPT","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00"}
2025-07-08 10:47:17.889 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-08 10:47:17.890 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-08 10:47:17.890 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-08 10:47:17.890 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-08T17:18:53, orderAmount=123000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-07-08T17:18:53, transactionCompletedTime=2025-07-08T17:18:53, transactionAmount=123000.0, transactionCurrency=VND, transactionStatus=0, responseCode=0, merchantTransRef=TEST_1751969917209204128727), paymentMethod=PaymentMethod(cardNumber=531358****3430, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, cardExpiry=1126, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=AUSTRALIA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null))
2025-07-08 10:47:17.891 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-08 10:47:19.858 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-08 10:47:19.858 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-08 10:56:08.569 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-08 10:56:08.569 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - list merchant from permission: [CGV15, CGV17, CGV2, CGV4, CGV18, CGV8, CGV6, CJSHOW, CGVKIOSK, CGV, CGV13, CGV5]
2025-07-08 10:56:09.704 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-08 10:56:09.705 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: []
2025-07-08 10:56:10.541 [reactor-http-epoll-4] ERROR v.o.t.s.i.TransactionOldServiceImpl - No merchantId matched -> returning empty response
2025-07-08 10:56:11.509 [reactor-http-epoll-4] ERROR v.o.t.e.GlobalExceptionHandler - Unhandled exception: 
org.springframework.web.server.ResponseStatusException: 403 FORBIDDEN "Permission denied"
	at vn.onepay.transaction.controller.TransactionController.lambda$5(TransactionController.java:212)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#captureTransaction(ServerWebExchange, String, String) [DispatcherHandler]
Original Stack Trace:
		at vn.onepay.transaction.controller.TransactionController.lambda$5(TransactionController.java:212)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:56:11.510 [reactor-http-epoll-4] WARN  o.s.w.r.r.m.a.RequestMappingHandlerAdapter - [c19ad921-7] Failure in @ExceptionHandler vn.onepay.transaction.exception.GlobalExceptionHandler#handleNotFound(ResponseStatusException, ServerWebExchange)
org.springframework.web.server.ResponseStatusException: 404 NOT_FOUND "Not found"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleNotFound(GlobalExceptionHandler.java:23)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
	at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onSubscribe(MonoPeekTerminal.java:152)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:149)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:56:11.510 [reactor-http-epoll-4] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: 403 FORBIDDEN "Permission denied"
org.springframework.web.server.ResponseStatusException: 403 FORBIDDEN "Permission denied"
	at vn.onepay.transaction.controller.TransactionController.lambda$5(TransactionController.java:212)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#captureTransaction(ServerWebExchange, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x00007bb8445bd688 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at vn.onepay.transaction.controller.TransactionController.lambda$5(TransactionController.java:212)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:56:15.962 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-08 10:56:15.962 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-08 10:56:15.962 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-08 10:56:15.962 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-08 10:56:15.962 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-08 10:56:15.963 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-08 10:56:15.963 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-08 10:56:15.963 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-08 10:56:15.963 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-08 10:56:15.963 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-08 10:56:15.963 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-08 10:56:15.963 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-08 10:56:15.963 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-08 10:56:15.963 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-08 10:56:15.963 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-08 10:56:15.963 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-08 10:56:15.963 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-08 10:56:15.963 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-08 10:56:15.963 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-08 10:56:15.963 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:56:15.963 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 5a8dfbd9-c081-4b56-b871-095d6e421792
2025-07-08 10:56:15.964 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8592
2025-07-08 10:56:15.964 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-08 10:56:15.964 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 345
2025-07-08 10:56:15.964 [reactor-http-epoll-6] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-08 10:56:22.289 [reactor-http-epoll-6] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:56:22.290 [reactor-http-epoll-6] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-08 10:56:22.290 [reactor-http-epoll-6] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=null
2025-07-08 10:56:22.348 [reactor-http-epoll-6] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","transaction_id":"********","original_transaction_id":"********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"4","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1751969917209204128727","amount":{"total":123000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"531358****3430","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"26"}},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"831000"},"transaction_time":"2025-07-08T17:18:53Z","transaction_status":"0","transaction_ref_number":"6265400258051234542701","ip_address":"***************","ip_proxy":"N","bin_country":"AUSTRALIA","csc_result_code":"M","enrolled_3ds":"Y","avs":{"address":"","result_code":"Y","province":"","city":"","zip_code":"","country":""},"verification_security_level":"05","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"NATIONAL AUSTRALIA BANK LIMITED","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","status3ds":"Y","riskOverAllResult":"ACCEPT","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00"}
2025-07-08 10:56:22.348 [reactor-http-epoll-6] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-08 10:56:22.349 [reactor-http-epoll-6] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-08 10:56:22.350 [reactor-http-epoll-6] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-08 10:56:22.350 [reactor-http-epoll-6] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-08T17:18:53, orderAmount=123000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-07-08T17:18:53, transactionCompletedTime=2025-07-08T17:18:53, transactionAmount=123000.0, transactionCurrency=VND, transactionStatus=0, responseCode=0, merchantTransRef=TEST_1751969917209204128727), paymentMethod=PaymentMethod(cardNumber=531358****3430, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, cardExpiry=1126, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=AUSTRALIA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null))
2025-07-08 10:56:22.350 [reactor-http-epoll-6] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-08 10:56:24.773 [reactor-http-epoll-6] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-08 10:56:24.773 [reactor-http-epoll-6] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-08 10:56:29.681 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-08 10:56:29.681 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - list merchant from permission: [CGV15, CGV17, CGV2, CGV4, CGV18, CGV8, CGV6, CJSHOW, CGVKIOSK, CGV, CGV13, CGV5]
2025-07-08 10:56:30.721 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-08 10:56:30.721 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: []
2025-07-08 10:57:01.534 [reactor-http-epoll-4] ERROR v.o.t.s.i.TransactionOldServiceImpl - No merchantId matched -> returning empty response
2025-07-08 10:57:03.203 [reactor-http-epoll-4] ERROR v.o.t.e.GlobalExceptionHandler - Unhandled exception: 
org.springframework.web.server.ResponseStatusException: 403 FORBIDDEN "Permission denied"
	at vn.onepay.transaction.controller.TransactionController.lambda$5(TransactionController.java:212)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#captureTransaction(ServerWebExchange, String, String) [DispatcherHandler]
Original Stack Trace:
		at vn.onepay.transaction.controller.TransactionController.lambda$5(TransactionController.java:212)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:57:03.204 [reactor-http-epoll-4] WARN  o.s.w.r.r.m.a.RequestMappingHandlerAdapter - [71a8f8cb-8] Failure in @ExceptionHandler vn.onepay.transaction.exception.GlobalExceptionHandler#handleNotFound(ResponseStatusException, ServerWebExchange)
org.springframework.web.server.ResponseStatusException: 404 NOT_FOUND "Not found"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleNotFound(GlobalExceptionHandler.java:23)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
	at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onSubscribe(MonoPeekTerminal.java:152)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:149)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:57:03.204 [reactor-http-epoll-4] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: 403 FORBIDDEN "Permission denied"
org.springframework.web.server.ResponseStatusException: 403 FORBIDDEN "Permission denied"
	at vn.onepay.transaction.controller.TransactionController.lambda$5(TransactionController.java:212)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#captureTransaction(ServerWebExchange, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x00007bb8445bd688 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at vn.onepay.transaction.controller.TransactionController.lambda$5(TransactionController.java:212)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-08 10:58:03.325 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-08 10:58:03.325 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-08 10:58:03.325 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-08 10:58:03.325 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-08 10:58:03.325 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-08 10:58:03.325 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-08 10:58:03.325 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-08 10:58:03.325 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-08 10:58:03.325 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-08 10:58:03.325 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-08 10:58:03.325 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-08 10:58:03.325 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-08 10:58:03.325 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-08 10:58:03.325 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-08 10:58:03.325 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-08 10:58:03.325 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-08 10:58:03.325 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-08 10:58:03.325 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-08 10:58:03.325 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-08 10:58:03.326 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:58:03.326 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = c0b70514-be28-4a43-940e-b9ae67947dc1
2025-07-08 10:58:03.326 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8592
2025-07-08 10:58:03.326 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-08 10:58:03.326 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 345
2025-07-08 10:58:03.326 [reactor-http-epoll-7] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-08 10:58:09.008 [reactor-http-epoll-7] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:58:09.009 [reactor-http-epoll-7] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-08 10:58:09.009 [reactor-http-epoll-7] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=null
2025-07-08 10:58:09.020 [reactor-http-epoll-7] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","transaction_id":"********","original_transaction_id":"********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"4","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1751969917209204128727","amount":{"total":123000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"531358****3430","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"26"}},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"831000"},"transaction_time":"2025-07-08T17:18:53Z","transaction_status":"0","transaction_ref_number":"6265400258051234542701","ip_address":"***************","ip_proxy":"N","bin_country":"AUSTRALIA","csc_result_code":"M","enrolled_3ds":"Y","avs":{"address":"","result_code":"Y","province":"","city":"","zip_code":"","country":""},"verification_security_level":"05","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"NATIONAL AUSTRALIA BANK LIMITED","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","status3ds":"Y","riskOverAllResult":"ACCEPT","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00"}
2025-07-08 10:58:09.020 [reactor-http-epoll-7] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-08 10:58:09.021 [reactor-http-epoll-7] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-08 10:58:09.021 [reactor-http-epoll-7] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-08 10:58:09.022 [reactor-http-epoll-7] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-08T17:18:53, orderAmount=123000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-07-08T17:18:53, transactionCompletedTime=2025-07-08T17:18:53, transactionAmount=123000.0, transactionCurrency=VND, transactionStatus=0, responseCode=0, merchantTransRef=TEST_1751969917209204128727), paymentMethod=PaymentMethod(cardNumber=531358****3430, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, cardExpiry=1126, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=AUSTRALIA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null))
2025-07-08 10:58:09.022 [reactor-http-epoll-7] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-08 10:58:11.072 [reactor-http-epoll-7] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-08 10:58:11.072 [reactor-http-epoll-7] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-08 10:58:15.934 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-08 10:58:15.934 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - list merchant from permission: [CGV15, CGV17, CGV2, CGV4, CGV18, CGV8, CGV6, CJSHOW, CGVKIOSK, CGV, CGV13, CGV5, TESTPCI]
2025-07-08 10:58:17.789 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-08 10:58:17.789 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-07-08 10:58:18.763 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api captureTransaction MA-----------
2025-07-08 10:58:34.583 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/transaction/international/******** ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={
    "id": "********",
    "op": "replace",
    "path": "/capture",
    "value": {
        "merchant_id": "TESTPCI",
        "amount": 1000,
        "currency": "VND",
        "note": "",
        "user_name": "Quản trị viên hệ thống hệ thống hệ thống hệ thống"
    },
    "skipCallSynchronize": false,
    "service": "QT"
}
2025-07-08 10:58:34.588 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api captureTransaction MA------------
2025-07-08 10:58:34.598 [ForkJoinPool.commonPool-worker-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND API: response code=404 ,response body=<html><body><h1>Resource not found</h1></body></html>
2025-07-08 10:59:20.943 [ForkJoinPool.commonPool-worker-1] INFO  v.o.t.exception.RequestLoggingFilter - Response: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/******** - Status: 500 INTERNAL_SERVER_ERROR
2025-07-08 10:59:29.784 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-08 10:59:29.784 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-08 10:59:29.784 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-08 10:59:29.784 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-08 10:59:29.784 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-08 10:59:29.784 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-08 10:59:29.784 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-08 10:59:29.784 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-08 10:59:29.784 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-08 10:59:29.784 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-08 10:59:29.784 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-08 10:59:29.785 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-08 10:59:29.785 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-08 10:59:29.785 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-08 10:59:29.785 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-08 10:59:29.785 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-08 10:59:29.785 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-08 10:59:29.785 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-08 10:59:29.785 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-08 10:59:29.785 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:59:29.785 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 29477e60-098f-4c6a-b0b7-8d886e26bf00
2025-07-08 10:59:29.785 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8592
2025-07-08 10:59:29.785 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-08 10:59:29.785 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 345
2025-07-08 10:59:29.785 [reactor-http-epoll-8] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-08 10:59:33.522 [reactor-http-epoll-8] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 10:59:33.523 [reactor-http-epoll-8] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-08 10:59:33.524 [reactor-http-epoll-8] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=null
2025-07-08 10:59:33.535 [reactor-http-epoll-8] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","transaction_id":"********","original_transaction_id":"********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"5","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1751969902989146435","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"512345****0008","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"28"},"commercical_card":"888","commercial_card_indicator":"3"},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"447432"},"transaction_time":"2025-07-08T17:18:37Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"***************","ip_proxy":"N","bin_country":"ANGUILLA","csc_result_code":"","enrolled_3ds":"Y","avs":{"address":"","province":"","city":"","zip_code":"","country":""},"verification_security_level":"","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"VIETINBANK","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"OTHER","status3ds":"Y","riskOverAllResult":"","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00"}
2025-07-08 10:59:33.535 [reactor-http-epoll-8] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-08 10:59:33.535 [reactor-http-epoll-8] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-08 10:59:33.536 [reactor-http-epoll-8] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-08 10:59:33.536 [reactor-http-epoll-8] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-08T17:18:37, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-07-08T17:18:37, transactionCompletedTime=2025-07-08T17:18:37, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=0, responseCode=0, merchantTransRef=TEST_1751969902989146435), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, cardExpiry=1128, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=ANGUILLA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null))
2025-07-08 10:59:33.536 [reactor-http-epoll-8] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-08 10:59:35.323 [reactor-http-epoll-8] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-08 10:59:35.324 [reactor-http-epoll-8] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-08 10:59:37.251 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-08 10:59:37.251 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - list merchant from permission: [CGV15, CGV17, CGV2, CGV4, CGV18, CGV8, CGV6, CJSHOW, CGVKIOSK, CGV, CGV13, CGV5, TESTPCI]
2025-07-08 10:59:42.640 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-08 10:59:42.640 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-07-08 10:59:49.846 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api captureTransaction MA-----------
2025-07-08 10:59:49.847 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/transaction/international/******** ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={
    "id": "********",
    "op": "replace",
    "path": "/capture",
    "value": {
        "merchant_id": "TESTPCI",
        "amount": 1000,
        "currency": "VND",
        "note": "",
        "user_name": "Quản trị viên hệ thống hệ thống hệ thống hệ thống"
    },
    "skipCallSynchronize": false,
    "service": "QT"
}
2025-07-08 10:59:49.847 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api captureTransaction MA------------
2025-07-08 10:59:49.852 [ForkJoinPool.commonPool-worker-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND API: response code=404 ,response body=<html><body><h1>Resource not found</h1></body></html>
2025-07-08 10:59:49.854 [ForkJoinPool.commonPool-worker-1] INFO  v.o.t.exception.RequestLoggingFilter - Response: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/******** - Status: 500 INTERNAL_SERVER_ERROR
2025-07-08 11:01:34.371 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-08 11:01:34.372 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-08 11:01:34.372 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-08 11:01:34.372 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-08 11:01:34.372 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-08 11:01:34.372 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-08 11:01:34.372 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-08 11:01:34.372 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-08 11:01:34.372 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-08 11:01:34.372 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-08 11:01:34.372 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-08 11:01:34.372 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-08 11:01:34.372 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-08 11:01:34.372 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-08 11:01:34.372 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-08 11:01:34.372 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-08 11:01:34.372 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-08 11:01:34.372 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-08 11:01:34.372 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-08 11:01:34.372 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 11:01:34.372 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 9f1b806c-bfef-4921-997f-93bd7fc0e0b5
2025-07-08 11:01:34.372 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8592
2025-07-08 11:01:34.372 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-08 11:01:34.372 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 345
2025-07-08 11:01:34.372 [reactor-http-epoll-9] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-08 11:01:41.056 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 11:01:41.056 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-08 11:01:41.056 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=null
2025-07-08 11:01:41.068 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","transaction_id":"********","original_transaction_id":"********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"5","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1751969902989146435","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"512345****0008","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"28"},"commercical_card":"888","commercial_card_indicator":"3"},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"447432"},"transaction_time":"2025-07-08T17:18:37Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"***************","ip_proxy":"N","bin_country":"ANGUILLA","csc_result_code":"","enrolled_3ds":"Y","avs":{"address":"","province":"","city":"","zip_code":"","country":""},"verification_security_level":"","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"VIETINBANK","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"OTHER","status3ds":"Y","riskOverAllResult":"","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00"}
2025-07-08 11:01:41.068 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-08 11:01:41.069 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-08 11:01:41.069 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-08 11:01:41.069 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-08T17:18:37, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-07-08T17:18:37, transactionCompletedTime=2025-07-08T17:18:37, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=0, responseCode=0, merchantTransRef=TEST_1751969902989146435), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, cardExpiry=1128, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=ANGUILLA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null))
2025-07-08 11:01:41.069 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-08 11:01:42.714 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-08 11:01:42.715 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-08 11:01:47.593 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-08 11:01:47.593 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - list merchant from permission: [CGV15, CGV17, CGV2, CGV4, CGV18, CGV8, CGV6, CJSHOW, CGVKIOSK, CGV, CGV13, CGV5, TESTPCI]
2025-07-08 11:01:47.593 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-08 11:01:47.593 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-07-08 11:01:47.593 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api captureTransaction MA-----------
2025-07-08 11:01:47.594 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/transaction/international/******** ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={
    "id": "********",
    "op": "replace",
    "path": "/capture",
    "value": {
        "merchant_id": "TESTPCI",
        "amount": 1000,
        "currency": "VND",
        "note": "",
        "user_name": "Quản trị viên hệ thống hệ thống hệ thống hệ thống"
    },
    "skipCallSynchronize": false,
    "service": "QT"
}
2025-07-08 11:01:47.595 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api captureTransaction MA------------
2025-07-08 11:01:47.601 [ForkJoinPool.commonPool-worker-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND API: response code=404 ,response body=<html><body><h1>Resource not found</h1></body></html>
2025-07-08 11:01:47.606 [ForkJoinPool.commonPool-worker-2] INFO  v.o.t.exception.RequestLoggingFilter - Response: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/******** - Status: 500 INTERNAL_SERVER_ERROR
2025-07-08 11:02:08.449 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-08 11:02:08.449 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-08 11:02:08.449 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-08 11:02:08.449 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-08 11:02:08.449 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-08 11:02:08.449 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-08 11:02:08.449 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-08 11:02:08.449 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-08 11:02:08.449 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-08 11:02:08.449 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-08 11:02:08.449 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-08 11:02:08.449 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-08 11:02:08.449 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-08 11:02:08.449 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-08 11:02:08.449 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-08 11:02:08.449 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-08 11:02:08.449 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-08 11:02:08.449 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-08 11:02:08.449 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-08 11:02:08.449 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 11:02:08.449 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = d4a5e5bf-d2a0-4a0b-9351-1eef56cf359a
2025-07-08 11:02:08.449 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8592
2025-07-08 11:02:08.449 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-08 11:02:08.449 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 345
2025-07-08 11:02:08.450 [reactor-http-epoll-9] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-08 11:02:11.292 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 11:02:11.292 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-08 11:02:11.293 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=null
2025-07-08 11:02:11.310 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","transaction_id":"********","original_transaction_id":"********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"5","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1751969902989146435","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"512345****0008","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"28"},"commercical_card":"888","commercial_card_indicator":"3"},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"447432"},"transaction_time":"2025-07-08T17:18:37Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"***************","ip_proxy":"N","bin_country":"ANGUILLA","csc_result_code":"","enrolled_3ds":"Y","avs":{"address":"","province":"","city":"","zip_code":"","country":""},"verification_security_level":"","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"VIETINBANK","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"OTHER","status3ds":"Y","riskOverAllResult":"","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00"}
2025-07-08 11:02:11.310 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-08 11:02:11.311 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-08 11:02:11.311 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-08 11:02:11.311 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-08T17:18:37, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-07-08T17:18:37, transactionCompletedTime=2025-07-08T17:18:37, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=0, responseCode=0, merchantTransRef=TEST_1751969902989146435), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, cardExpiry=1128, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=ANGUILLA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null))
2025-07-08 11:02:11.311 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-08 11:02:21.508 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-08 11:02:21.508 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-08 11:02:21.522 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-08 11:02:21.522 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - list merchant from permission: [CGV15, CGV17, CGV2, CGV4, CGV18, CGV8, CGV6, CJSHOW, CGVKIOSK, CGV, CGV13, CGV5, TESTPCI]
2025-07-08 11:02:21.522 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-08 11:02:21.522 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-07-08 11:02:21.522 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api captureTransaction MA-----------
2025-07-08 11:02:21.523 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/transaction/international/******** ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={
    "id": "********",
    "op": "replace",
    "path": "/capture",
    "value": {
        "merchant_id": "TESTPCI",
        "amount": 1000,
        "currency": "VND",
        "note": "",
        "user_name": "Quản trị viên hệ thống hệ thống hệ thống hệ thống"
    },
    "skipCallSynchronize": false,
    "service": "QT"
}
2025-07-08 11:03:31.686 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api captureTransaction MA------------
2025-07-08 11:03:31.692 [ForkJoinPool.commonPool-worker-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND API: response code=404 ,response body=<html><body><h1>Resource not found</h1></body></html>
2025-07-08 11:03:31.697 [ForkJoinPool.commonPool-worker-3] INFO  v.o.t.exception.RequestLoggingFilter - Response: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/******** - Status: 500 INTERNAL_SERVER_ERROR
2025-07-08 11:04:38.847 [reactor-http-epoll-10] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-08 11:04:38.848 [reactor-http-epoll-10] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-08 11:04:38.848 [reactor-http-epoll-10] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-08 11:04:38.848 [reactor-http-epoll-10] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-08 11:04:38.848 [reactor-http-epoll-10] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-08 11:04:38.848 [reactor-http-epoll-10] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-08 11:04:38.848 [reactor-http-epoll-10] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-08 11:04:38.848 [reactor-http-epoll-10] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-08 11:04:38.848 [reactor-http-epoll-10] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-08 11:04:38.848 [reactor-http-epoll-10] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-08 11:04:38.848 [reactor-http-epoll-10] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-08 11:04:38.848 [reactor-http-epoll-10] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-08 11:04:38.848 [reactor-http-epoll-10] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-08 11:04:38.848 [reactor-http-epoll-10] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-08 11:04:38.848 [reactor-http-epoll-10] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-08 11:04:38.848 [reactor-http-epoll-10] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-08 11:04:38.848 [reactor-http-epoll-10] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-08 11:04:38.848 [reactor-http-epoll-10] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-08 11:04:38.848 [reactor-http-epoll-10] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-08 11:04:38.848 [reactor-http-epoll-10] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 11:04:38.848 [reactor-http-epoll-10] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 487f79dd-463b-4da5-ac75-50ee3ba54a58
2025-07-08 11:04:38.848 [reactor-http-epoll-10] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8592
2025-07-08 11:04:38.848 [reactor-http-epoll-10] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-08 11:04:38.848 [reactor-http-epoll-10] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 345
2025-07-08 11:04:38.848 [reactor-http-epoll-10] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-08 11:04:41.956 [reactor-http-epoll-10] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-08 11:04:41.957 [reactor-http-epoll-10] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-08 11:04:41.957 [reactor-http-epoll-10] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=null
2025-07-08 11:04:41.969 [reactor-http-epoll-10] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","transaction_id":"********","original_transaction_id":"********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"5","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1751969902989146435","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"512345****0008","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"28"},"commercical_card":"888","commercial_card_indicator":"3"},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"447432"},"transaction_time":"2025-07-08T17:18:37Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"***************","ip_proxy":"N","bin_country":"ANGUILLA","csc_result_code":"","enrolled_3ds":"Y","avs":{"address":"","province":"","city":"","zip_code":"","country":""},"verification_security_level":"","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"VIETINBANK","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"OTHER","status3ds":"Y","riskOverAllResult":"","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00"}
2025-07-08 11:04:41.969 [reactor-http-epoll-10] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-08 11:04:41.970 [reactor-http-epoll-10] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-08 11:04:41.970 [reactor-http-epoll-10] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-08 11:04:41.970 [reactor-http-epoll-10] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-08T17:18:37, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-07-08T17:18:37, transactionCompletedTime=2025-07-08T17:18:37, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=0, responseCode=0, merchantTransRef=TEST_1751969902989146435), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, cardExpiry=1128, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=ANGUILLA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null))
2025-07-08 11:04:41.970 [reactor-http-epoll-10] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-08 11:04:43.442 [reactor-http-epoll-10] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-08 11:04:43.442 [reactor-http-epoll-10] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-08 11:04:43.452 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-08 11:04:43.452 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - list merchant from permission: [CGV15, CGV17, CGV2, CGV4, CGV18, CGV8, CGV6, CJSHOW, CGVKIOSK, CGV, CGV13, CGV5, TESTPCI]
2025-07-08 11:04:43.452 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-08 11:04:43.452 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-07-08 11:04:43.452 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api captureTransaction MA-----------
2025-07-08 11:04:43.453 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/transaction/international/******** ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={
    "id": "********",
    "op": "replace",
    "path": "/capture",
    "value": {
        "merchant_id": "TESTPCI",
        "amount": 1000,
        "currency": "VND",
        "note": "",
        "user_name": "Quản trị viên hệ thống hệ thống hệ thống hệ thống"
    },
    "skipCallSynchronize": false,
    "service": "QT"
}
2025-07-08 11:04:44.510 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api captureTransaction MA------------
2025-07-08 11:04:44.515 [ForkJoinPool.commonPool-worker-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND API: response code=404 ,response body=<html><body><h1>Resource not found</h1></body></html>
2025-07-08 11:04:44.520 [ForkJoinPool.commonPool-worker-4] INFO  v.o.t.exception.RequestLoggingFilter - Response: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/******** - Status: 500 INTERNAL_SERVER_ERROR
