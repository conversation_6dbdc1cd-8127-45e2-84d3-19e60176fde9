2025-07-03 02:44:13.516 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 1301721 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-07-03 02:44:13.520 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-07-03 02:44:14.302 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-07-03 02:44:14.515 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 205 ms. Found 2 R2DBC repository interfaces.
2025-07-03 02:44:16.000 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-07-03 02:44:16.320 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@5f935d49
2025-07-03 02:44:16.322 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-07-03 02:44:16.363 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-07-03 02:44:16.477 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@4cc7e3ad
2025-07-03 02:44:16.478 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-07-03 02:44:16.534 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-07-03 02:44:16.543 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-07-03 02:44:16.546 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-07-03 02:44:16.547 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-07-03 02:44:16.549 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-07-03 02:44:16.550 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-07-03 02:44:17.400 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8592 (http)
2025-07-03 02:44:17.587 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 4.728 seconds (process running for 5.407)
2025-07-03 02:51:12.456 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 1303818 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-07-03 02:51:12.459 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-07-03 02:51:13.289 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-07-03 02:51:13.519 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 218 ms. Found 2 R2DBC repository interfaces.
2025-07-03 02:51:15.021 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-07-03 02:51:15.320 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@790654d5
2025-07-03 02:51:15.321 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-07-03 02:51:15.363 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-07-03 02:51:15.479 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@3d235635
2025-07-03 02:51:15.479 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-07-03 02:51:15.523 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-07-03 02:51:15.531 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-07-03 02:51:15.533 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-07-03 02:51:15.534 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-07-03 02:51:15.536 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-07-03 02:51:15.538 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-07-03 02:51:16.356 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8592 (http)
2025-07-03 02:51:16.545 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 4.868 seconds (process running for 5.485)
2025-07-03 02:53:52.536 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://:8592/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=
2025-07-03 02:53:52.537 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-07-03 02:53:52.537 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-03 02:53:52.537 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-03 02:53:52.537 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-03 02:53:52.538 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-03 02:53:52.538 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-03 02:53:52.538 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-07-03 02:53:52.538 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-03 02:53:52.538 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-03 02:53:52.538 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-03 02:53:52.538 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-03 02:53:52.538 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-03 02:53:52.538 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-03 02:53:52.538 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-03 02:53:52.538 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-03 02:53:52.538 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.o593TYqeZtRscylfcCRROq9JDBpd5QOozHmx0zwqyFY
2025-07-03 02:53:52.538 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 63e0a555-3924-4140-9c5f-ebd6f3c9b3fb
2025-07-03 02:53:52.539 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8592
2025-07-03 02:53:52.539 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-03 02:53:52.542 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-03 02:53:52.543 [reactor-http-epoll-3] WARN  v.o.t.exception.PartnerIdFilter - Missing x-user-id header
2025-07-03 02:53:52.563 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Response: GET http://:8592/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType= - Status: 400 BAD_REQUEST
2025-07-03 02:54:41.720 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://:8592/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=&
2025-07-03 02:54:41.720 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-07-03 02:54:41.720 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-03 02:54:41.720 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-03 02:54:41.720 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-03 02:54:41.720 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-03 02:54:41.720 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-03 02:54:41.720 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-07-03 02:54:41.721 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-03 02:54:41.721 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-03 02:54:41.721 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-03 02:54:41.721 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-03 02:54:41.721 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-03 02:54:41.721 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-03 02:54:41.721 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-03 02:54:41.721 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-03 02:54:41.721 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.o593TYqeZtRscylfcCRROq9JDBpd5QOozHmx0zwqyFY
2025-07-03 02:54:41.721 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-03 02:54:41.721 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = ba9a94de-f148-450e-9184-436197195c5c
2025-07-03 02:54:41.721 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8592
2025-07-03 02:54:41.722 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-03 02:54:41.722 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-03 02:54:58.458 [reactor-http-epoll-3] ERROR v.o.t.s.impl.DownloadTaskServiceImpl - Error creating task: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
org.springframework.web.reactive.function.client.WebClientResponseException$InternalServerError: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
	at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ 500 INTERNAL_SERVER_ERROR from GET http://localhost/ma-service/api/v1/permission/merchants-by-user [DefaultWebClient]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
		at org.springframework.web.reactive.function.client.DefaultClientResponse.lambda$createException$1(DefaultClientResponse.java:214)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:106)
		at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 02:54:58.467 [reactor-http-epoll-3] WARN  o.s.w.r.r.m.a.RequestMappingHandlerAdapter - [be9265f3-2] Failure in @ExceptionHandler vn.onepay.transaction.exception.GlobalExceptionHandler#handleRuntimeException(RuntimeException)
org.springframework.web.server.ResponseStatusException: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleRuntimeException(GlobalExceptionHandler.java:29)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onError(Operators.java:2236)
	at reactor.core.publisher.MonoSingle$SingleSubscriber.onError(MonoSingle.java:150)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onError(MonoPeekTerminal.java:258)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onError(Operators.java:2236)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onError(MonoIgnoreThen.java:280)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:232)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onComplete(FluxOnErrorReturn.java:169)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onComplete(MonoIgnoreElements.java:89)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onComplete(FluxMapFuseable.java:152)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.netty.channel.FluxReceive.startReceiver(FluxReceive.java:179)
	at reactor.netty.channel.FluxReceive.subscribe(FluxReceive.java:145)
	at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:68)
	at reactor.netty.ByteBufFlux.subscribe(ByteBufFlux.java:340)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onError(MonoIgnoreThen.java:280)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:232)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onComplete(FluxOnErrorReturn.java:169)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onComplete(MonoIgnoreElements.java:89)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onComplete(FluxMapFuseable.java:152)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.netty.channel.FluxReceive.startReceiver(FluxReceive.java:179)
	at reactor.netty.channel.FluxReceive.subscribe(FluxReceive.java:145)
	at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:68)
	at reactor.netty.ByteBufFlux.subscribe(ByteBufFlux.java:340)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
	at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 02:54:58.468 [reactor-http-epoll-3] ERROR v.o.t.exception.RequestLoggingFilter - Request error: GET http://:8592/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=& - Exception: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
org.springframework.web.reactive.function.client.WebClientResponseException$InternalServerError: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
	at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ 500 INTERNAL_SERVER_ERROR from GET http://localhost/ma-service/api/v1/permission/merchants-by-user [DefaultWebClient]
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#createExportTask(ServerWebExchange, String, String, String, String, String, String, String, String, Boolean, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x0000772c205be8e8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
		at org.springframework.web.reactive.function.client.DefaultClientResponse.lambda$createException$1(DefaultClientResponse.java:214)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:106)
		at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 02:54:58.479 [reactor-http-epoll-3] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [be9265f3-2]  500 Server Error for HTTP GET "/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=&"
org.springframework.web.reactive.function.client.WebClientResponseException$InternalServerError: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
	at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ 500 INTERNAL_SERVER_ERROR from GET http://localhost/ma-service/api/v1/permission/merchants-by-user [DefaultWebClient]
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#createExportTask(ServerWebExchange, String, String, String, String, String, String, String, String, Boolean, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x0000772c205be8e8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP GET "/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=&" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
		at org.springframework.web.reactive.function.client.DefaultClientResponse.lambda$createException$1(DefaultClientResponse.java:214)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:106)
		at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 02:55:12.557 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://:8592/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=&
2025-07-03 02:55:12.557 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-07-03 02:55:12.557 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-03 02:55:12.557 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-03 02:55:12.557 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-03 02:55:12.557 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-03 02:55:12.557 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-03 02:55:12.557 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-07-03 02:55:12.557 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-03 02:55:12.558 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-03 02:55:12.558 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-03 02:55:12.558 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-03 02:55:12.558 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-03 02:55:12.558 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-03 02:55:12.558 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-03 02:55:12.558 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-03 02:55:12.558 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.o593TYqeZtRscylfcCRROq9JDBpd5QOozHmx0zwqyFY
2025-07-03 02:55:12.558 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-03 02:55:12.558 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 8480442f-150c-423a-9da5-d75ab7afe7c0
2025-07-03 02:55:12.558 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8592
2025-07-03 02:55:12.558 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-03 02:55:12.558 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-03 02:55:39.658 [reactor-http-epoll-3] ERROR v.o.t.s.impl.DownloadTaskServiceImpl - Error creating task: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
org.springframework.web.reactive.function.client.WebClientResponseException$InternalServerError: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
	at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ 500 INTERNAL_SERVER_ERROR from GET http://localhost/ma-service/api/v1/permission/merchants-by-user [DefaultWebClient]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
		at org.springframework.web.reactive.function.client.DefaultClientResponse.lambda$createException$1(DefaultClientResponse.java:214)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:106)
		at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 02:55:39.661 [reactor-http-epoll-3] WARN  o.s.w.r.r.m.a.RequestMappingHandlerAdapter - [be9265f3-3] Failure in @ExceptionHandler vn.onepay.transaction.exception.GlobalExceptionHandler#handleRuntimeException(RuntimeException)
org.springframework.web.server.ResponseStatusException: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleRuntimeException(GlobalExceptionHandler.java:29)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onError(Operators.java:2236)
	at reactor.core.publisher.MonoSingle$SingleSubscriber.onError(MonoSingle.java:150)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onError(MonoPeekTerminal.java:258)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onError(Operators.java:2236)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onError(MonoIgnoreThen.java:280)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:232)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onComplete(FluxOnErrorReturn.java:169)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onComplete(MonoIgnoreElements.java:89)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onComplete(FluxMapFuseable.java:152)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.netty.channel.FluxReceive.startReceiver(FluxReceive.java:179)
	at reactor.netty.channel.FluxReceive.subscribe(FluxReceive.java:145)
	at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:68)
	at reactor.netty.ByteBufFlux.subscribe(ByteBufFlux.java:340)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onError(MonoIgnoreThen.java:280)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:232)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onComplete(FluxOnErrorReturn.java:169)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onComplete(MonoIgnoreElements.java:89)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onComplete(FluxMapFuseable.java:152)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.netty.channel.FluxReceive.startReceiver(FluxReceive.java:179)
	at reactor.netty.channel.FluxReceive.subscribe(FluxReceive.java:145)
	at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:68)
	at reactor.netty.ByteBufFlux.subscribe(ByteBufFlux.java:340)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
	at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 02:55:39.662 [reactor-http-epoll-3] ERROR v.o.t.exception.RequestLoggingFilter - Request error: GET http://:8592/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=& - Exception: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
org.springframework.web.reactive.function.client.WebClientResponseException$InternalServerError: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
	at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ 500 INTERNAL_SERVER_ERROR from GET http://localhost/ma-service/api/v1/permission/merchants-by-user [DefaultWebClient]
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#createExportTask(ServerWebExchange, String, String, String, String, String, String, String, String, Boolean, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x0000772c205be8e8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
		at org.springframework.web.reactive.function.client.DefaultClientResponse.lambda$createException$1(DefaultClientResponse.java:214)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:106)
		at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 02:55:39.663 [reactor-http-epoll-3] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [be9265f3-3]  500 Server Error for HTTP GET "/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=&"
org.springframework.web.reactive.function.client.WebClientResponseException$InternalServerError: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
	at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ 500 INTERNAL_SERVER_ERROR from GET http://localhost/ma-service/api/v1/permission/merchants-by-user [DefaultWebClient]
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#createExportTask(ServerWebExchange, String, String, String, String, String, String, String, String, Boolean, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x0000772c205be8e8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP GET "/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=&" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
		at org.springframework.web.reactive.function.client.DefaultClientResponse.lambda$createException$1(DefaultClientResponse.java:214)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:106)
		at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 02:55:48.093 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://:8592/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=&
2025-07-03 02:55:48.094 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-07-03 02:55:48.094 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-03 02:55:48.094 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-03 02:55:48.094 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-03 02:55:48.094 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-03 02:55:48.094 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-03 02:55:48.094 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-07-03 02:55:48.094 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-03 02:55:48.094 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-03 02:55:48.094 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-03 02:55:48.095 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-03 02:55:48.095 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-03 02:55:48.095 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-03 02:55:48.095 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-03 02:55:48.095 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-03 02:55:48.095 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.o593TYqeZtRscylfcCRROq9JDBpd5QOozHmx0zwqyFY
2025-07-03 02:55:48.095 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-03 02:55:48.095 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = b05a3893-2c88-41c3-8b3a-69cc2133af5c
2025-07-03 02:55:48.095 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8592
2025-07-03 02:55:48.095 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-03 02:55:48.095 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-03 02:58:27.503 [reactor-http-epoll-3] ERROR v.o.t.s.impl.DownloadTaskServiceImpl - Error creating task: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
org.springframework.web.reactive.function.client.WebClientResponseException$InternalServerError: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
	at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ 500 INTERNAL_SERVER_ERROR from GET http://localhost/ma-service/api/v1/permission/merchants-by-user [DefaultWebClient]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
		at org.springframework.web.reactive.function.client.DefaultClientResponse.lambda$createException$1(DefaultClientResponse.java:214)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:106)
		at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 02:58:27.506 [reactor-http-epoll-3] WARN  o.s.w.r.r.m.a.RequestMappingHandlerAdapter - [be9265f3-4] Failure in @ExceptionHandler vn.onepay.transaction.exception.GlobalExceptionHandler#handleRuntimeException(RuntimeException)
org.springframework.web.server.ResponseStatusException: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleRuntimeException(GlobalExceptionHandler.java:29)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onError(Operators.java:2236)
	at reactor.core.publisher.MonoSingle$SingleSubscriber.onError(MonoSingle.java:150)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onError(MonoPeekTerminal.java:258)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onError(Operators.java:2236)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onError(MonoIgnoreThen.java:280)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:232)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onComplete(FluxOnErrorReturn.java:169)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onComplete(MonoIgnoreElements.java:89)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onComplete(FluxMapFuseable.java:152)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.netty.channel.FluxReceive.startReceiver(FluxReceive.java:179)
	at reactor.netty.channel.FluxReceive.subscribe(FluxReceive.java:145)
	at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:68)
	at reactor.netty.ByteBufFlux.subscribe(ByteBufFlux.java:340)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onError(MonoIgnoreThen.java:280)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:232)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onComplete(FluxOnErrorReturn.java:169)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onComplete(MonoIgnoreElements.java:89)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onComplete(FluxMapFuseable.java:152)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.netty.channel.FluxReceive.startReceiver(FluxReceive.java:179)
	at reactor.netty.channel.FluxReceive.subscribe(FluxReceive.java:145)
	at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:68)
	at reactor.netty.ByteBufFlux.subscribe(ByteBufFlux.java:340)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
	at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 02:58:27.506 [reactor-http-epoll-3] ERROR v.o.t.exception.RequestLoggingFilter - Request error: GET http://:8592/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=& - Exception: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
org.springframework.web.reactive.function.client.WebClientResponseException$InternalServerError: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
	at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ 500 INTERNAL_SERVER_ERROR from GET http://localhost/ma-service/api/v1/permission/merchants-by-user [DefaultWebClient]
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#createExportTask(ServerWebExchange, String, String, String, String, String, String, String, String, Boolean, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x0000772c205be8e8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
		at org.springframework.web.reactive.function.client.DefaultClientResponse.lambda$createException$1(DefaultClientResponse.java:214)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:106)
		at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 02:58:27.508 [reactor-http-epoll-3] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [be9265f3-4]  500 Server Error for HTTP GET "/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=&"
org.springframework.web.reactive.function.client.WebClientResponseException$InternalServerError: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
	at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ 500 INTERNAL_SERVER_ERROR from GET http://localhost/ma-service/api/v1/permission/merchants-by-user [DefaultWebClient]
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#createExportTask(ServerWebExchange, String, String, String, String, String, String, String, String, Boolean, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x0000772c205be8e8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP GET "/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=&" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
		at org.springframework.web.reactive.function.client.DefaultClientResponse.lambda$createException$1(DefaultClientResponse.java:214)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:106)
		at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 02:58:31.503 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://:8592/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=&
2025-07-03 02:58:31.503 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-07-03 02:58:31.504 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-03 02:58:31.504 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-03 02:58:31.504 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-03 02:58:31.504 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-03 02:58:31.504 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-03 02:58:31.504 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-07-03 02:58:31.504 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-03 02:58:31.504 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-03 02:58:31.504 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-03 02:58:31.504 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-03 02:58:31.504 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-03 02:58:31.504 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-03 02:58:31.504 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-03 02:58:31.504 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-03 02:58:31.504 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.o593TYqeZtRscylfcCRROq9JDBpd5QOozHmx0zwqyFY
2025-07-03 02:58:31.504 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-03 02:58:31.505 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 394771d8-7fa5-419f-9a8d-af4bb484181b
2025-07-03 02:58:31.505 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8592
2025-07-03 02:58:31.505 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-03 02:58:31.505 [reactor-http-epoll-4] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-03 02:58:34.342 [reactor-http-epoll-4] ERROR v.o.t.s.impl.DownloadTaskServiceImpl - Error creating task: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
org.springframework.web.reactive.function.client.WebClientResponseException$InternalServerError: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
	at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ 500 INTERNAL_SERVER_ERROR from GET http://localhost/ma-service/api/v1/permission/merchants-by-user [DefaultWebClient]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
		at org.springframework.web.reactive.function.client.DefaultClientResponse.lambda$createException$1(DefaultClientResponse.java:214)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:106)
		at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 02:58:34.343 [reactor-http-epoll-4] WARN  o.s.w.r.r.m.a.RequestMappingHandlerAdapter - [7342ad4d-5] Failure in @ExceptionHandler vn.onepay.transaction.exception.GlobalExceptionHandler#handleRuntimeException(RuntimeException)
org.springframework.web.server.ResponseStatusException: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleRuntimeException(GlobalExceptionHandler.java:29)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onError(Operators.java:2236)
	at reactor.core.publisher.MonoSingle$SingleSubscriber.onError(MonoSingle.java:150)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onError(MonoPeekTerminal.java:258)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onError(Operators.java:2236)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onError(MonoIgnoreThen.java:280)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:232)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onComplete(FluxOnErrorReturn.java:169)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onComplete(MonoIgnoreElements.java:89)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onComplete(FluxMapFuseable.java:152)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.netty.channel.FluxReceive.startReceiver(FluxReceive.java:179)
	at reactor.netty.channel.FluxReceive.subscribe(FluxReceive.java:145)
	at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:68)
	at reactor.netty.ByteBufFlux.subscribe(ByteBufFlux.java:340)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onError(MonoIgnoreThen.java:280)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:232)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onComplete(FluxOnErrorReturn.java:169)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onComplete(MonoIgnoreElements.java:89)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onComplete(FluxMapFuseable.java:152)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.netty.channel.FluxReceive.startReceiver(FluxReceive.java:179)
	at reactor.netty.channel.FluxReceive.subscribe(FluxReceive.java:145)
	at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:68)
	at reactor.netty.ByteBufFlux.subscribe(ByteBufFlux.java:340)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
	at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 02:58:34.344 [reactor-http-epoll-4] ERROR v.o.t.exception.RequestLoggingFilter - Request error: GET http://:8592/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=& - Exception: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
org.springframework.web.reactive.function.client.WebClientResponseException$InternalServerError: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
	at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ 500 INTERNAL_SERVER_ERROR from GET http://localhost/ma-service/api/v1/permission/merchants-by-user [DefaultWebClient]
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#createExportTask(ServerWebExchange, String, String, String, String, String, String, String, String, Boolean, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x0000772c205be8e8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
		at org.springframework.web.reactive.function.client.DefaultClientResponse.lambda$createException$1(DefaultClientResponse.java:214)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:106)
		at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 02:58:34.345 [reactor-http-epoll-4] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [7342ad4d-5]  500 Server Error for HTTP GET "/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=&"
org.springframework.web.reactive.function.client.WebClientResponseException$InternalServerError: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
	at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ 500 INTERNAL_SERVER_ERROR from GET http://localhost/ma-service/api/v1/permission/merchants-by-user [DefaultWebClient]
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#createExportTask(ServerWebExchange, String, String, String, String, String, String, String, String, Boolean, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x0000772c205be8e8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP GET "/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=&" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
		at org.springframework.web.reactive.function.client.DefaultClientResponse.lambda$createException$1(DefaultClientResponse.java:214)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:106)
		at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 02:58:42.733 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://:8592/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=&
2025-07-03 02:58:42.733 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-07-03 02:58:42.733 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-03 02:58:42.733 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-03 02:58:42.733 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-03 02:58:42.733 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-03 02:58:42.733 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-03 02:58:42.733 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-07-03 02:58:42.733 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-03 02:58:42.733 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-03 02:58:42.733 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-03 02:58:42.733 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-03 02:58:42.734 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-03 02:58:42.734 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-03 02:58:42.734 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-03 02:58:42.734 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-03 02:58:42.734 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.o593TYqeZtRscylfcCRROq9JDBpd5QOozHmx0zwqyFY
2025-07-03 02:58:42.734 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-03 02:58:42.734 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 865fe76f-d46d-41e3-86f5-511aefb14a46
2025-07-03 02:58:42.734 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8592
2025-07-03 02:58:42.734 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-03 02:58:42.734 [reactor-http-epoll-4] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-03 02:59:23.160 [reactor-http-epoll-4] ERROR v.o.t.s.impl.DownloadTaskServiceImpl - Error creating task: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
org.springframework.web.reactive.function.client.WebClientResponseException$InternalServerError: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
	at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ 500 INTERNAL_SERVER_ERROR from GET http://localhost/ma-service/api/v1/permission/merchants-by-user [DefaultWebClient]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
		at org.springframework.web.reactive.function.client.DefaultClientResponse.lambda$createException$1(DefaultClientResponse.java:214)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:106)
		at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 02:59:23.162 [reactor-http-epoll-4] WARN  o.s.w.r.r.m.a.RequestMappingHandlerAdapter - [7342ad4d-6] Failure in @ExceptionHandler vn.onepay.transaction.exception.GlobalExceptionHandler#handleRuntimeException(RuntimeException)
org.springframework.web.server.ResponseStatusException: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleRuntimeException(GlobalExceptionHandler.java:29)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onError(Operators.java:2236)
	at reactor.core.publisher.MonoSingle$SingleSubscriber.onError(MonoSingle.java:150)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onError(MonoPeekTerminal.java:258)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onError(Operators.java:2236)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onError(MonoIgnoreThen.java:280)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:232)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onComplete(FluxOnErrorReturn.java:169)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onComplete(MonoIgnoreElements.java:89)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onComplete(FluxMapFuseable.java:152)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.netty.channel.FluxReceive.startReceiver(FluxReceive.java:179)
	at reactor.netty.channel.FluxReceive.subscribe(FluxReceive.java:145)
	at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:68)
	at reactor.netty.ByteBufFlux.subscribe(ByteBufFlux.java:340)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onError(MonoIgnoreThen.java:280)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:232)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onComplete(FluxOnErrorReturn.java:169)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onComplete(MonoIgnoreElements.java:89)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onComplete(FluxMapFuseable.java:152)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.netty.channel.FluxReceive.startReceiver(FluxReceive.java:179)
	at reactor.netty.channel.FluxReceive.subscribe(FluxReceive.java:145)
	at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:68)
	at reactor.netty.ByteBufFlux.subscribe(ByteBufFlux.java:340)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
	at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 02:59:23.163 [reactor-http-epoll-4] ERROR v.o.t.exception.RequestLoggingFilter - Request error: GET http://:8592/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=& - Exception: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
org.springframework.web.reactive.function.client.WebClientResponseException$InternalServerError: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
	at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ 500 INTERNAL_SERVER_ERROR from GET http://localhost/ma-service/api/v1/permission/merchants-by-user [DefaultWebClient]
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#createExportTask(ServerWebExchange, String, String, String, String, String, String, String, String, Boolean, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x0000772c205be8e8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
		at org.springframework.web.reactive.function.client.DefaultClientResponse.lambda$createException$1(DefaultClientResponse.java:214)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:106)
		at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 02:59:23.165 [reactor-http-epoll-4] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [7342ad4d-6]  500 Server Error for HTTP GET "/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=&"
org.springframework.web.reactive.function.client.WebClientResponseException$InternalServerError: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
	at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ 500 INTERNAL_SERVER_ERROR from GET http://localhost/ma-service/api/v1/permission/merchants-by-user [DefaultWebClient]
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#createExportTask(ServerWebExchange, String, String, String, String, String, String, String, String, Boolean, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x0000772c205be8e8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP GET "/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=&" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
		at org.springframework.web.reactive.function.client.DefaultClientResponse.lambda$createException$1(DefaultClientResponse.java:214)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:106)
		at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 02:59:25.109 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://:8592/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=&
2025-07-03 02:59:25.109 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-07-03 02:59:25.109 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-03 02:59:25.109 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-03 02:59:25.109 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-03 02:59:25.109 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-03 02:59:25.109 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-03 02:59:25.109 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-07-03 02:59:25.109 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-03 02:59:25.110 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-03 02:59:25.110 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-03 02:59:25.110 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-03 02:59:25.110 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-03 02:59:25.110 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-03 02:59:25.110 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-03 02:59:25.110 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-03 02:59:25.110 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.o593TYqeZtRscylfcCRROq9JDBpd5QOozHmx0zwqyFY
2025-07-03 02:59:25.110 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-03 02:59:25.110 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 84928ef4-f9a6-415a-9226-786697434d78
2025-07-03 02:59:25.110 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8592
2025-07-03 02:59:25.110 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-03 02:59:25.110 [reactor-http-epoll-4] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-03 03:00:41.877 [reactor-http-epoll-4] ERROR v.o.t.s.impl.DownloadTaskServiceImpl - Error creating task: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
org.springframework.web.reactive.function.client.WebClientResponseException$InternalServerError: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
	at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ 500 INTERNAL_SERVER_ERROR from GET http://localhost/ma-service/api/v1/permission/merchants-by-user [DefaultWebClient]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
		at org.springframework.web.reactive.function.client.DefaultClientResponse.lambda$createException$1(DefaultClientResponse.java:214)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:106)
		at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 03:00:41.879 [reactor-http-epoll-4] WARN  o.s.w.r.r.m.a.RequestMappingHandlerAdapter - [7342ad4d-7] Failure in @ExceptionHandler vn.onepay.transaction.exception.GlobalExceptionHandler#handleRuntimeException(RuntimeException)
org.springframework.web.server.ResponseStatusException: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleRuntimeException(GlobalExceptionHandler.java:29)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onError(Operators.java:2236)
	at reactor.core.publisher.MonoSingle$SingleSubscriber.onError(MonoSingle.java:150)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onError(MonoPeekTerminal.java:258)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onError(Operators.java:2236)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onError(MonoIgnoreThen.java:280)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:232)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onComplete(FluxOnErrorReturn.java:169)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onComplete(MonoIgnoreElements.java:89)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onComplete(FluxMapFuseable.java:152)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.netty.channel.FluxReceive.startReceiver(FluxReceive.java:179)
	at reactor.netty.channel.FluxReceive.subscribe(FluxReceive.java:145)
	at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:68)
	at reactor.netty.ByteBufFlux.subscribe(ByteBufFlux.java:340)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onError(MonoIgnoreThen.java:280)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:232)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onComplete(FluxOnErrorReturn.java:169)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onComplete(MonoIgnoreElements.java:89)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onComplete(FluxMapFuseable.java:152)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.netty.channel.FluxReceive.startReceiver(FluxReceive.java:179)
	at reactor.netty.channel.FluxReceive.subscribe(FluxReceive.java:145)
	at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:68)
	at reactor.netty.ByteBufFlux.subscribe(ByteBufFlux.java:340)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
	at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 03:00:41.880 [reactor-http-epoll-4] ERROR v.o.t.exception.RequestLoggingFilter - Request error: GET http://:8592/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=& - Exception: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
org.springframework.web.reactive.function.client.WebClientResponseException$InternalServerError: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
	at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ 500 INTERNAL_SERVER_ERROR from GET http://localhost/ma-service/api/v1/permission/merchants-by-user [DefaultWebClient]
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#createExportTask(ServerWebExchange, String, String, String, String, String, String, String, String, Boolean, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x0000772c205be8e8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
		at org.springframework.web.reactive.function.client.DefaultClientResponse.lambda$createException$1(DefaultClientResponse.java:214)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:106)
		at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 03:00:41.881 [reactor-http-epoll-4] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [7342ad4d-7]  500 Server Error for HTTP GET "/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=&"
org.springframework.web.reactive.function.client.WebClientResponseException$InternalServerError: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
	at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ 500 INTERNAL_SERVER_ERROR from GET http://localhost/ma-service/api/v1/permission/merchants-by-user [DefaultWebClient]
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#createExportTask(ServerWebExchange, String, String, String, String, String, String, String, String, Boolean, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x0000772c205be8e8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP GET "/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=&" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
		at org.springframework.web.reactive.function.client.DefaultClientResponse.lambda$createException$1(DefaultClientResponse.java:214)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:106)
		at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 03:00:49.739 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://:8592/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=&
2025-07-03 03:00:49.739 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-07-03 03:00:49.739 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-03 03:00:49.739 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-03 03:00:49.739 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-03 03:00:49.739 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-03 03:00:49.740 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-03 03:00:49.740 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-07-03 03:00:49.740 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-03 03:00:49.740 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-03 03:00:49.740 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-03 03:00:49.740 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-03 03:00:49.740 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-03 03:00:49.740 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-03 03:00:49.740 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-03 03:00:49.740 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-03 03:00:49.740 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.o593TYqeZtRscylfcCRROq9JDBpd5QOozHmx0zwqyFY
2025-07-03 03:00:49.740 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-03 03:00:49.740 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = e64d347c-411d-4151-a5fc-433dd02acc8d
2025-07-03 03:00:49.740 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8592
2025-07-03 03:00:49.740 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-03 03:00:49.740 [reactor-http-epoll-5] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-03 03:03:40.094 [reactor-http-epoll-5] ERROR v.o.t.s.impl.DownloadTaskServiceImpl - Error creating task: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
org.springframework.web.reactive.function.client.WebClientResponseException$InternalServerError: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
	at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ 500 INTERNAL_SERVER_ERROR from GET http://localhost/ma-service/api/v1/permission/merchants-by-user [DefaultWebClient]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
		at org.springframework.web.reactive.function.client.DefaultClientResponse.lambda$createException$1(DefaultClientResponse.java:214)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:106)
		at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 03:03:40.095 [reactor-http-epoll-5] WARN  o.s.w.r.r.m.a.RequestMappingHandlerAdapter - [d269849b-8] Failure in @ExceptionHandler vn.onepay.transaction.exception.GlobalExceptionHandler#handleRuntimeException(RuntimeException)
org.springframework.web.server.ResponseStatusException: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleRuntimeException(GlobalExceptionHandler.java:29)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onError(Operators.java:2236)
	at reactor.core.publisher.MonoSingle$SingleSubscriber.onError(MonoSingle.java:150)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onError(MonoPeekTerminal.java:258)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onError(Operators.java:2236)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onError(MonoIgnoreThen.java:280)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:232)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onComplete(FluxOnErrorReturn.java:169)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onComplete(MonoIgnoreElements.java:89)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onComplete(FluxMapFuseable.java:152)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.netty.channel.FluxReceive.startReceiver(FluxReceive.java:179)
	at reactor.netty.channel.FluxReceive.subscribe(FluxReceive.java:145)
	at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:68)
	at reactor.netty.ByteBufFlux.subscribe(ByteBufFlux.java:340)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onError(MonoIgnoreThen.java:280)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:232)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onComplete(FluxOnErrorReturn.java:169)
	at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onComplete(MonoIgnoreElements.java:89)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onComplete(FluxMapFuseable.java:152)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.netty.channel.FluxReceive.startReceiver(FluxReceive.java:179)
	at reactor.netty.channel.FluxReceive.subscribe(FluxReceive.java:145)
	at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:68)
	at reactor.netty.ByteBufFlux.subscribe(ByteBufFlux.java:340)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
	at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 03:03:40.096 [reactor-http-epoll-5] ERROR v.o.t.exception.RequestLoggingFilter - Request error: GET http://:8592/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=& - Exception: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
org.springframework.web.reactive.function.client.WebClientResponseException$InternalServerError: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
	at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ 500 INTERNAL_SERVER_ERROR from GET http://localhost/ma-service/api/v1/permission/merchants-by-user [DefaultWebClient]
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#createExportTask(ServerWebExchange, String, String, String, String, String, String, String, String, Boolean, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x0000772c205be8e8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
		at org.springframework.web.reactive.function.client.DefaultClientResponse.lambda$createException$1(DefaultClientResponse.java:214)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:106)
		at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 03:03:40.097 [reactor-http-epoll-5] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [d269849b-8]  500 Server Error for HTTP GET "/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=&"
org.springframework.web.reactive.function.client.WebClientResponseException$InternalServerError: 500 Internal Server Error from GET http://localhost/ma-service/api/v1/permission/merchants-by-user
	at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ 500 INTERNAL_SERVER_ERROR from GET http://localhost/ma-service/api/v1/permission/merchants-by-user [DefaultWebClient]
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#createExportTask(ServerWebExchange, String, String, String, String, String, String, String, String, Boolean, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x0000772c205be8e8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP GET "/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=&" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:332)
		at org.springframework.web.reactive.function.client.DefaultClientResponse.lambda$createException$1(DefaultClientResponse.java:214)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:106)
		at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onNext(FluxOnErrorReturn.java:162)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 03:03:42.671 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://:8592/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=&
2025-07-03 03:03:42.671 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-07-03 03:03:42.671 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-03 03:03:42.671 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-03 03:03:42.671 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-03 03:03:42.671 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-03 03:03:42.671 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-03 03:03:42.671 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-07-03 03:03:42.671 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-03 03:03:42.671 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-03 03:03:42.672 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-03 03:03:42.672 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-03 03:03:42.672 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-03 03:03:42.672 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-03 03:03:42.672 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-03 03:03:42.672 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-03 03:03:42.672 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.o593TYqeZtRscylfcCRROq9JDBpd5QOozHmx0zwqyFY
2025-07-03 03:03:42.672 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-03 03:03:42.672 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 759f80e9-a4b6-4f28-a848-037d3a35beea
2025-07-03 03:03:42.672 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8592
2025-07-03 03:03:42.672 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-03 03:03:42.672 [reactor-http-epoll-6] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-03 03:05:07.065 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 1308424 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-07-03 03:05:07.069 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-07-03 03:05:07.841 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-07-03 03:05:08.023 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 175 ms. Found 2 R2DBC repository interfaces.
2025-07-03 03:05:09.550 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-07-03 03:05:09.832 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@176333ee
2025-07-03 03:05:09.834 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-07-03 03:05:09.876 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-07-03 03:05:09.988 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@4ebfb045
2025-07-03 03:05:09.988 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-07-03 03:05:10.026 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-07-03 03:05:10.034 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-07-03 03:05:10.037 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-07-03 03:05:10.038 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-07-03 03:05:10.040 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-07-03 03:05:10.041 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-07-03 03:05:10.866 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8592 (http)
2025-07-03 03:05:11.082 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 4.678 seconds (process running for 5.376)
2025-07-03 03:05:37.975 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://:8592/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=&
2025-07-03 03:05:37.976 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-07-03 03:05:37.976 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-03 03:05:37.976 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-03 03:05:37.976 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-03 03:05:37.976 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-03 03:05:37.976 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-03 03:05:37.976 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-07-03 03:05:37.976 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-03 03:05:37.976 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-03 03:05:37.976 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-03 03:05:37.976 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-03 03:05:37.976 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-03 03:05:37.976 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-03 03:05:37.977 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-03 03:05:37.977 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-03 03:05:37.977 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.o593TYqeZtRscylfcCRROq9JDBpd5QOozHmx0zwqyFY
2025-07-03 03:05:37.977 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-03 03:05:37.977 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = a4b5f786-063b-4e0e-846f-d1bfcdf03601
2025-07-03 03:05:37.977 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8592
2025-07-03 03:05:37.977 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-03 03:05:37.980 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-03 03:06:33.201 [reactor-tcp-epoll-1] WARN  o.s.w.r.r.m.a.RequestMappingHandlerAdapter - [900c18a8-1] Failure in @ExceptionHandler vn.onepay.transaction.exception.GlobalExceptionHandler#handleRuntimeException(RuntimeException)
org.springframework.web.server.ResponseStatusException: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleRuntimeException(GlobalExceptionHandler.java:29)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onError(Operators.java:2236)
	at reactor.core.publisher.MonoSingle$SingleSubscriber.onError(MonoSingle.java:150)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.FluxHide$SuppressFuseableSubscriber.onError(FluxHide.java:142)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onError(MonoIgnoreThen.java:280)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.MonoUsingWhen$ResourceSubscriber.onError(MonoUsingWhen.java:203)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
	at reactor.core.publisher.MonoSingle$SingleSubscriber.onError(MonoSingle.java:150)
	at io.r2dbc.postgresql.util.FluxDiscardOnCancel$FluxDiscardOnCancelSubscriber.onError(FluxDiscardOnCancel.java:97)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.MonoDelayUntil$DelayUntilCoordinator.onError(MonoDelayUntil.java:201)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
	at reactor.netty.resources.NewConnectionProvider$DisposableConnect.onError(NewConnectionProvider.java:156)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at reactor.netty.transport.TransportConnector$MonoChannelPromise._subscribe(TransportConnector.java:638)
	at reactor.netty.transport.TransportConnector$MonoChannelPromise.lambda$subscribe$0(TransportConnector.java:550)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:405)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-03 03:06:33.204 [reactor-tcp-epoll-1] ERROR v.o.t.exception.RequestLoggingFilter - Request error: GET http://:8592/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=& - Exception: Failed to obtain R2DBC Connection
org.springframework.dao.DataAccessResourceFailureException: Failed to obtain R2DBC Connection
	at org.springframework.r2dbc.connection.ConnectionFactoryUtils.lambda$getConnection$0(ConnectionFactoryUtils.java:100)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#createExportTask(ServerWebExchange, String, String, String, String, String, String, String, String, Boolean, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x00007e728c5be508 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at org.springframework.r2dbc.connection.ConnectionFactoryUtils.lambda$getConnection$0(ConnectionFactoryUtils.java:100)
		at reactor.core.publisher.Mono.lambda$onErrorMap$29(Mono.java:3862)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:94)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
		at reactor.core.publisher.MonoSingle$SingleSubscriber.onError(MonoSingle.java:150)
		at io.r2dbc.postgresql.util.FluxDiscardOnCancel$FluxDiscardOnCancelSubscriber.onError(FluxDiscardOnCancel.java:97)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
		at reactor.core.publisher.MonoDelayUntil$DelayUntilCoordinator.onError(MonoDelayUntil.java:201)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.NewConnectionProvider$DisposableConnect.onError(NewConnectionProvider.java:156)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise._subscribe(TransportConnector.java:638)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.lambda$subscribe$0(TransportConnector.java:550)
		at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:405)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: io.r2dbc.postgresql.PostgresqlConnectionFactory$PostgresConnectionException: Cannot connect to db1514.onepay.vn/<unresolved>:5432
	at io.r2dbc.postgresql.PostgresqlConnectionFactory.cannotConnect(PostgresqlConnectionFactory.java:188)
	at io.r2dbc.postgresql.PostgresqlConnectionFactory.lambda$doCreateConnection$7(PostgresqlConnectionFactory.java:153)
	at reactor.core.publisher.Mono.lambda$onErrorMap$29(Mono.java:3862)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:94)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.MonoDelayUntil$DelayUntilCoordinator.onError(MonoDelayUntil.java:201)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
	at reactor.netty.resources.NewConnectionProvider$DisposableConnect.onError(NewConnectionProvider.java:156)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at reactor.netty.transport.TransportConnector$MonoChannelPromise._subscribe(TransportConnector.java:638)
	at reactor.netty.transport.TransportConnector$MonoChannelPromise.lambda$subscribe$0(TransportConnector.java:550)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:405)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.net.UnknownHostException: db1514.onepay.vn: Name or service not known
	at java.base/java.net.Inet6AddressImpl.lookupAllHostAddr(Native Method)
	at java.base/java.net.Inet6AddressImpl.lookupAllHostAddr(Inet6AddressImpl.java:52)
	at java.base/java.net.InetAddress$PlatformResolver.lookupByName(InetAddress.java:1211)
	at java.base/java.net.InetAddress.getAddressesFromNameService(InetAddress.java:1828)
	at java.base/java.net.InetAddress$NameServiceAddresses.get(InetAddress.java:1139)
	at java.base/java.net.InetAddress.getAllByName0(InetAddress.java:1818)
	at java.base/java.net.InetAddress.getAllByName(InetAddress.java:1688)
	at io.netty.util.internal.SocketUtils$9.run(SocketUtils.java:169)
	at io.netty.util.internal.SocketUtils$9.run(SocketUtils.java:166)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:571)
	at io.netty.util.internal.SocketUtils.allAddressesByName(SocketUtils.java:166)
	at io.netty.resolver.DefaultNameResolver.doResolveAll(DefaultNameResolver.java:50)
	at io.netty.resolver.SimpleNameResolver.resolveAll(SimpleNameResolver.java:79)
	at io.netty.resolver.SimpleNameResolver.resolveAll(SimpleNameResolver.java:71)
	at io.netty.resolver.RoundRobinInetAddressResolver.doResolveAll(RoundRobinInetAddressResolver.java:77)
	at io.netty.resolver.SimpleNameResolver.resolveAll(SimpleNameResolver.java:79)
	at io.netty.resolver.SimpleNameResolver.resolveAll(SimpleNameResolver.java:71)
	at io.netty.resolver.InetSocketAddressResolver.doResolveAll(InetSocketAddressResolver.java:73)
	at io.netty.resolver.InetSocketAddressResolver.doResolveAll(InetSocketAddressResolver.java:31)
	at io.netty.resolver.AbstractAddressResolver.resolveAll(AbstractAddressResolver.java:158)
	at reactor.netty.transport.TransportConnector.doResolveAndConnect(TransportConnector.java:335)
	at reactor.netty.transport.TransportConnector.lambda$connect$6(TransportConnector.java:165)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	... 10 common frames omitted
2025-07-03 03:06:33.223 [reactor-tcp-epoll-1] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [900c18a8-1]  500 Server Error for HTTP GET "/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=&"
org.springframework.dao.DataAccessResourceFailureException: Failed to obtain R2DBC Connection
	at org.springframework.r2dbc.connection.ConnectionFactoryUtils.lambda$getConnection$0(ConnectionFactoryUtils.java:100)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#createExportTask(ServerWebExchange, String, String, String, String, String, String, String, String, Boolean, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x00007e728c5be508 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP GET "/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=&" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at org.springframework.r2dbc.connection.ConnectionFactoryUtils.lambda$getConnection$0(ConnectionFactoryUtils.java:100)
		at reactor.core.publisher.Mono.lambda$onErrorMap$29(Mono.java:3862)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:94)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
		at reactor.core.publisher.MonoSingle$SingleSubscriber.onError(MonoSingle.java:150)
		at io.r2dbc.postgresql.util.FluxDiscardOnCancel$FluxDiscardOnCancelSubscriber.onError(FluxDiscardOnCancel.java:97)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
		at reactor.core.publisher.MonoDelayUntil$DelayUntilCoordinator.onError(MonoDelayUntil.java:201)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.resources.NewConnectionProvider$DisposableConnect.onError(NewConnectionProvider.java:156)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.Operators.error(Operators.java:198)
		at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise._subscribe(TransportConnector.java:638)
		at reactor.netty.transport.TransportConnector$MonoChannelPromise.lambda$subscribe$0(TransportConnector.java:550)
		at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:405)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: io.r2dbc.postgresql.PostgresqlConnectionFactory$PostgresConnectionException: Cannot connect to db1514.onepay.vn/<unresolved>:5432
	at io.r2dbc.postgresql.PostgresqlConnectionFactory.cannotConnect(PostgresqlConnectionFactory.java:188)
	at io.r2dbc.postgresql.PostgresqlConnectionFactory.lambda$doCreateConnection$7(PostgresqlConnectionFactory.java:153)
	at reactor.core.publisher.Mono.lambda$onErrorMap$29(Mono.java:3862)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:94)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.MonoDelayUntil$DelayUntilCoordinator.onError(MonoDelayUntil.java:201)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
	at reactor.netty.resources.NewConnectionProvider$DisposableConnect.onError(NewConnectionProvider.java:156)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.core.publisher.MonoError.subscribe(MonoError.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at reactor.netty.transport.TransportConnector$MonoChannelPromise._subscribe(TransportConnector.java:638)
	at reactor.netty.transport.TransportConnector$MonoChannelPromise.lambda$subscribe$0(TransportConnector.java:550)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:405)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.net.UnknownHostException: db1514.onepay.vn: Name or service not known
	at java.base/java.net.Inet6AddressImpl.lookupAllHostAddr(Native Method)
	at java.base/java.net.Inet6AddressImpl.lookupAllHostAddr(Inet6AddressImpl.java:52)
	at java.base/java.net.InetAddress$PlatformResolver.lookupByName(InetAddress.java:1211)
	at java.base/java.net.InetAddress.getAddressesFromNameService(InetAddress.java:1828)
	at java.base/java.net.InetAddress$NameServiceAddresses.get(InetAddress.java:1139)
	at java.base/java.net.InetAddress.getAllByName0(InetAddress.java:1818)
	at java.base/java.net.InetAddress.getAllByName(InetAddress.java:1688)
	at io.netty.util.internal.SocketUtils$9.run(SocketUtils.java:169)
	at io.netty.util.internal.SocketUtils$9.run(SocketUtils.java:166)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:571)
	at io.netty.util.internal.SocketUtils.allAddressesByName(SocketUtils.java:166)
	at io.netty.resolver.DefaultNameResolver.doResolveAll(DefaultNameResolver.java:50)
	at io.netty.resolver.SimpleNameResolver.resolveAll(SimpleNameResolver.java:79)
	at io.netty.resolver.SimpleNameResolver.resolveAll(SimpleNameResolver.java:71)
	at io.netty.resolver.RoundRobinInetAddressResolver.doResolveAll(RoundRobinInetAddressResolver.java:77)
	at io.netty.resolver.SimpleNameResolver.resolveAll(SimpleNameResolver.java:79)
	at io.netty.resolver.SimpleNameResolver.resolveAll(SimpleNameResolver.java:71)
	at io.netty.resolver.InetSocketAddressResolver.doResolveAll(InetSocketAddressResolver.java:73)
	at io.netty.resolver.InetSocketAddressResolver.doResolveAll(InetSocketAddressResolver.java:31)
	at io.netty.resolver.AbstractAddressResolver.resolveAll(AbstractAddressResolver.java:158)
	at reactor.netty.transport.TransportConnector.doResolveAndConnect(TransportConnector.java:335)
	at reactor.netty.transport.TransportConnector.lambda$connect$6(TransportConnector.java:165)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	... 10 common frames omitted
2025-07-03 03:09:39.332 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://:8592/ma-service/api/v1/transactions/transaction/export?merchantId=&keyword=&fromDate=2025-05-15T00:00:00.000Z&toDate=2025-06-07T23:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchKeyword=&searchType=&
2025-07-03 03:09:39.332 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-07-03 03:09:39.332 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-03 03:09:39.332 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-03 03:09:39.333 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-03 03:09:39.333 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-03 03:09:39.333 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-03 03:09:39.333 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-07-03 03:09:39.333 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-03 03:09:39.333 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-03 03:09:39.333 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-03 03:09:39.333 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-03 03:09:39.333 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-03 03:09:39.333 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-03 03:09:39.333 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-03 03:09:39.333 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-03 03:09:39.333 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.o593TYqeZtRscylfcCRROq9JDBpd5QOozHmx0zwqyFY
2025-07-03 03:09:39.334 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-03 03:09:39.334 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 4b47e975-5f8e-4464-a63f-564f0fc6a254
2025-07-03 03:09:39.334 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8592
2025-07-03 03:09:39.334 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-03 03:09:39.334 [reactor-http-epoll-4] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
