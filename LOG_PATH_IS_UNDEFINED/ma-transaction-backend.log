2025-07-01 03:01:39.550 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 432481 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-07-01 03:01:39.552 [main] INFO  v.o.t.TransactionAppApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-01 03:01:40.292 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-07-01 03:01:40.485 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 184 ms. Found 2 R2DBC repository interfaces.
2025-07-01 03:01:41.776 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-07-01 03:01:41.785 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-07-01 03:01:42.672 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8592 (http)
2025-07-01 03:01:42.730 [main] INFO  o.a.k.c.consumer.ConsumerConfig - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.include.jmx.reporter = true
	auto.offset.reset = latest
	bootstrap.servers = [************:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-download-task-group-1
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	enable.metrics.push = true
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = download-task-group
	group.instance.id = null
	group.protocol = classic
	group.remote.assignor = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metadata.recovery.strategy = none
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.max.ms = 1000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.UUIDDeserializer

2025-07-01 03:01:42.791 [main] INFO  o.a.k.c.t.i.KafkaMetricsCollector - initializing Kafka metrics collector
2025-07-01 03:01:43.027 [main] INFO  o.a.kafka.common.utils.AppInfoParser - Kafka version: 3.8.1
2025-07-01 03:01:43.027 [main] INFO  o.a.kafka.common.utils.AppInfoParser - Kafka commitId: 70d6ff42debf7e17
2025-07-01 03:01:43.027 [main] INFO  o.a.kafka.common.utils.AppInfoParser - Kafka startTimeMs: 1751338903025
2025-07-01 03:01:43.031 [main] INFO  o.a.k.c.c.i.LegacyKafkaConsumer - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Subscribed to topic(s): download-task-topic
2025-07-01 03:01:43.053 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 4.165 seconds (process running for 4.741)
2025-07-01 03:01:43.316 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:01:43.319 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:01:43.319 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:01:43.411 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:01:43.412 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:01:43.412 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:01:43.536 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:01:43.536 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:01:43.536 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:01:43.789 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:01:43.790 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:01:43.790 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:01:44.261 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:01:44.261 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:01:44.262 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:01:45.019 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:01:45.019 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:01:45.019 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:01:46.054 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:01:46.055 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:01:46.055 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:01:47.092 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:01:47.093 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:01:47.093 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:01:48.050 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:01:48.050 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:01:48.051 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:01:49.059 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:01:49.059 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:01:49.059 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:01:50.067 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:01:50.068 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:01:50.068 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:01:51.068 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:01:51.068 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:01:51.068 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:01:51.925 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:01:51.925 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:01:51.925 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:01:52.975 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:01:52.975 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:01:52.975 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:01:54.025 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:01:54.025 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:01:54.025 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:01:55.032 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:01:55.032 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:01:55.033 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:01:55.889 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:01:55.889 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:01:55.889 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:01:56.845 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:01:56.846 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:01:56.846 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:01:57.851 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:01:57.852 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:01:57.852 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:01:58.881 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:01:58.881 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:01:58.881 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:01:59.414 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-07-01 03:01:59.414 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Request joining group due to: consumer pro-actively leaving the group
2025-07-01 03:01:59.414 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.LegacyKafkaConsumer - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Unsubscribed all topics or patterns and assigned partitions
2025-07-01 03:01:59.416 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-07-01 03:01:59.416 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Request joining group due to: consumer pro-actively leaving the group
2025-07-01 03:01:59.422 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.kafka.common.metrics.Metrics - Metrics scheduler closed
2025-07-01 03:01:59.422 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.kafka.common.metrics.Metrics - Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-07-01 03:01:59.422 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.kafka.common.metrics.Metrics - Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-07-01 03:01:59.423 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.kafka.common.metrics.Metrics - Metrics reporters closed
2025-07-01 03:01:59.430 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.kafka.common.utils.AppInfoParser - App info kafka.consumer for consumer-download-task-group-1 unregistered
2025-07-01 03:01:59.430 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.k.l.KafkaMessageListenerContainer - download-task-group: Consumer stopped
2025-07-01 03:01:59.433 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.netty.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-01 03:01:59.437 [netty-shutdown] INFO  o.s.b.w.e.netty.GracefulShutdown - Graceful shutdown complete
2025-07-01 03:06:10.355 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 434456 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-07-01 03:06:10.357 [main] INFO  v.o.t.TransactionAppApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-01 03:06:11.073 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-07-01 03:06:11.251 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 170 ms. Found 2 R2DBC repository interfaces.
2025-07-01 03:06:12.474 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-07-01 03:06:12.484 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-07-01 03:06:13.395 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8592 (http)
2025-07-01 03:06:13.438 [main] INFO  o.a.k.c.consumer.ConsumerConfig - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.include.jmx.reporter = true
	auto.offset.reset = latest
	bootstrap.servers = [************:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-download-task-group-1
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	enable.metrics.push = true
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = download-task-group
	group.instance.id = null
	group.protocol = classic
	group.remote.assignor = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metadata.recovery.strategy = none
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.max.ms = 1000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.UUIDDeserializer

2025-07-01 03:06:13.485 [main] INFO  o.a.k.c.t.i.KafkaMetricsCollector - initializing Kafka metrics collector
2025-07-01 03:06:13.671 [main] INFO  o.a.kafka.common.utils.AppInfoParser - Kafka version: 3.8.1
2025-07-01 03:06:13.671 [main] INFO  o.a.kafka.common.utils.AppInfoParser - Kafka commitId: 70d6ff42debf7e17
2025-07-01 03:06:13.671 [main] INFO  o.a.kafka.common.utils.AppInfoParser - Kafka startTimeMs: 1751339173669
2025-07-01 03:06:13.675 [main] INFO  o.a.k.c.c.i.LegacyKafkaConsumer - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Subscribed to topic(s): download-task-topic
2025-07-01 03:06:13.692 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 3.953 seconds (process running for 4.37)
2025-07-01 03:06:13.927 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:13.929 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:13.929 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:14.042 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:14.043 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:14.043 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:14.195 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:14.195 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:14.196 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:14.424 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:14.424 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:14.424 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:14.888 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:14.888 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:14.888 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:15.598 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:15.599 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:15.599 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:16.606 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:16.606 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:16.607 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:17.607 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:17.608 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:17.608 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:18.609 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:18.609 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:18.609 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:19.580 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:19.581 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:19.581 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:20.538 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:20.538 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:20.538 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:21.539 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:21.540 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:21.540 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:22.396 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:22.396 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:22.396 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:23.397 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:23.397 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:23.397 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:24.398 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:24.398 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:24.398 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:25.399 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:25.399 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:25.399 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:26.425 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:26.426 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:26.426 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:27.426 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:27.427 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:27.427 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:28.230 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:28.231 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:28.231 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:29.245 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:29.246 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:29.246 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:30.245 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:30.246 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:30.246 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:31.274 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:31.274 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:31.274 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:32.275 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:32.275 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:32.275 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:33.276 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:33.276 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:33.276 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:34.262 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:34.263 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:34.263 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:35.116 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:35.116 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:35.116 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:36.020 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:36.020 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:36.021 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:36.875 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:36.875 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:36.875 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:37.879 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:37.879 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:37.879 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:38.880 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:38.880 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:38.880 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:39.881 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:39.881 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:39.881 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:40.735 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:40.736 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:40.736 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:41.674 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:41.675 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:41.675 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:42.629 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:42.630 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:42.630 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:43.551 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:43.551 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:43.551 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:44.578 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:44.579 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:44.579 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:45.579 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-01 03:06:45.579 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (/************:9092) could not be established. Node may not be available.
2025-07-01 03:06:45.579 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker ************:9092 (id: -1 rack: null) disconnected
2025-07-01 03:06:46.256 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-07-01 03:06:46.256 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Request joining group due to: consumer pro-actively leaving the group
2025-07-01 03:06:46.256 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.LegacyKafkaConsumer - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Unsubscribed all topics or patterns and assigned partitions
2025-07-01 03:06:46.257 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-07-01 03:06:46.258 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Request joining group due to: consumer pro-actively leaving the group
2025-07-01 03:06:46.261 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.kafka.common.metrics.Metrics - Metrics scheduler closed
2025-07-01 03:06:46.262 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.kafka.common.metrics.Metrics - Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-07-01 03:06:46.262 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.kafka.common.metrics.Metrics - Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-07-01 03:06:46.262 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.kafka.common.metrics.Metrics - Metrics reporters closed
2025-07-01 03:06:46.267 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.kafka.common.utils.AppInfoParser - App info kafka.consumer for consumer-download-task-group-1 unregistered
2025-07-01 03:06:46.268 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.k.l.KafkaMessageListenerContainer - download-task-group: Consumer stopped
2025-07-01 03:06:46.269 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.netty.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-01 03:06:46.272 [netty-shutdown] INFO  o.s.b.w.e.netty.GracefulShutdown - Graceful shutdown complete
