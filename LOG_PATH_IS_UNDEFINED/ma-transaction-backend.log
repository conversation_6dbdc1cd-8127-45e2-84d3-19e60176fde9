2025-07-18 04:07:26.219 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 3989571 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-07-18 04:07:26.224 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-07-18 04:07:27.072 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-07-18 04:07:27.259 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 178 ms. Found 2 R2DBC repository interfaces.
2025-07-18 04:07:29.013 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-07-18 04:07:29.303 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@1d6f77d7
2025-07-18 04:07:29.305 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-07-18 04:07:29.356 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-07-18 04:07:29.457 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@6fbe4800
2025-07-18 04:07:29.458 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-07-18 04:07:29.563 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-07-18 04:07:29.572 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-07-18 04:07:29.574 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-07-18 04:07:29.575 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-07-18 04:07:29.577 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-07-18 04:07:29.594 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-07-18 04:07:30.649 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8595 (http)
2025-07-18 04:07:30.853 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 5.381 seconds (process running for 7.132)
2025-07-18 04:13:26.169 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:26.169 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:26.170 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:26.433 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:26.433 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:26.434 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:27.073 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:27.073 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:27.073 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:27.434 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:27.434 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:27.434 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:28.123 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:28.123 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:28.123 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:28.437 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:28.438 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:28.438 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:29.124 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:29.124 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:29.124 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:29.341 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:29.341 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:29.341 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:30.082 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:30.083 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:30.083 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:30.244 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:30.244 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:30.244 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:30.995 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:30.995 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:30.996 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:31.249 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:31.249 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:31.249 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:31.854 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:31.854 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:31.854 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:32.250 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:32.250 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:32.250 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:32.898 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:32.899 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:32.899 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:33.103 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:33.104 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:33.104 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:33.808 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:33.809 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:33.809 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:34.104 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:34.104 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:34.104 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:34.644 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 3992961 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-07-18 04:13:34.647 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-07-18 04:13:34.801 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:34.802 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:34.802 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:35.007 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:35.007 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:35.007 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:35.452 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-07-18 04:13:35.633 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 173 ms. Found 2 R2DBC repository interfaces.
2025-07-18 04:13:35.805 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:35.805 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:35.806 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:35.998 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:35.999 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:35.999 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:36.652 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:36.653 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:36.653 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:36.999 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:36.999 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:36.999 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:37.273 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-07-18 04:13:37.565 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@58aa5c94
2025-07-18 04:13:37.567 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-07-18 04:13:37.642 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-07-18 04:13:37.658 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:37.659 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:37.659 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:37.784 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@73b0ed03
2025-07-18 04:13:37.785 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-07-18 04:13:37.915 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-07-18 04:13:37.926 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-07-18 04:13:37.929 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-07-18 04:13:37.930 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-07-18 04:13:37.933 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-07-18 04:13:37.935 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-07-18 04:13:38.000 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:38.000 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:38.000 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:38.612 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:38.613 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:38.613 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:38.903 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:38.904 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:38.904 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:39.416 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:39.416 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:39.416 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:39.757 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:39.757 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:39.757 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:40.419 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:40.419 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:40.419 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:40.806 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:40.806 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:40.806 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:40.836 [main] WARN  o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-07-18 04:13:40.838 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Shutdown initiated...
2025-07-18 04:13:40.851 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Shutdown completed.
2025-07-18 04:13:40.852 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Shutdown initiated...
2025-07-18 04:13:40.872 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Shutdown completed.
2025-07-18 04:13:40.881 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-18 04:13:40.897 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8595 was already in use.

Action:

Identify and stop the process that's listening on port 8595 or configure this application to listen on another port.

2025-07-18 04:13:41.341 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:41.342 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:41.342 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:41.827 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:41.827 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:41.827 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:42.272 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:42.272 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:42.272 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:42.827 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:42.828 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:42.828 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:43.273 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:43.273 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:43.273 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:43.828 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:43.828 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:43.828 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:44.274 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:44.274 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:44.274 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:44.721 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:44.722 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:44.722 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:45.233 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:45.234 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:45.234 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:45.722 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:45.722 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:45.722 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:46.278 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:46.278 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:46.278 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:46.650 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:46.650 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:46.651 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:47.284 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:47.285 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:47.285 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:47.654 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:47.654 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:47.654 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:48.188 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:48.188 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:48.188 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:48.655 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:48.655 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:48.655 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:49.041 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:49.041 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:49.041 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:49.658 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:49.659 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:49.659 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:50.042 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:50.042 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:50.042 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:50.658 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:50.658 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:50.658 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:50.887 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:50.887 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:50.887 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:51.678 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:51.678 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:51.678 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:51.895 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:51.895 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:51.895 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:51.936 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://:8595/ma-service/api/v1/transactions/transaction/detail?transactionId=********&paymentMethod=QT&transactionType=Purchase
2025-07-18 04:13:51.936 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-07-18 04:13:51.936 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-18 04:13:51.936 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-18 04:13:51.936 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-18 04:13:51.937 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-18 04:13:51.937 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-18 04:13:51.937 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-07-18 04:13:51.937 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-18 04:13:51.937 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-18 04:13:51.937 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-18 04:13:51.937 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-18 04:13:51.937 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-18 04:13:51.937 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-18 04:13:51.937 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-18 04:13:51.937 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-07-18 04:13:51.937 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTM3MTE4MTksInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.uFHvM51Hq3zbz_0QNIPAnz5t7fDwjs8LqXvOtvvcZrA
2025-07-18 04:13:51.937 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = b7635361-5549-41e8-ba46-d00fc5ec4712
2025-07-18 04:13:51.937 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8595
2025-07-18 04:13:51.937 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-18 04:13:51.942 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-18 04:13:51.943 [reactor-http-epoll-3] WARN  v.o.t.exception.PartnerIdFilter - Missing x-user-id header
2025-07-18 04:13:51.966 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Response: GET http://:8595/ma-service/api/v1/transactions/transaction/detail?transactionId=********&paymentMethod=QT&transactionType=Purchase - Status: 400 BAD_REQUEST
2025-07-18 04:13:52.679 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:52.679 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:52.679 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:52.749 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:52.749 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:52.749 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:53.680 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:53.680 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:53.680 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:53.799 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:53.799 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:53.799 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:54.534 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:54.534 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:54.534 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:54.803 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:54.803 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:54.803 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:55.488 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:55.488 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:55.488 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:55.656 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:55.656 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:55.656 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:56.400 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:56.400 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:56.400 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:56.539 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:56.540 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:56.540 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:57.253 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:57.253 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:57.253 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:57.566 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:57.566 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:57.566 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:58.253 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:58.253 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:58.253 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:58.566 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:58.567 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:58.567 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:59.254 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:59.254 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:59.254 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:13:59.442 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:13:59.442 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:13:59.442 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:00.254 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:00.254 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:00.255 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:00.441 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:00.442 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:00.442 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:01.190 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:01.191 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:01.191 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:01.479 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:01.479 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:01.479 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:02.191 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:02.191 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:02.191 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:02.480 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:02.480 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:02.480 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:03.194 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:03.194 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:03.194 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:03.481 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:03.481 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:03.481 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:04.228 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:04.228 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:04.228 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:04.434 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:04.434 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:04.434 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:05.228 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:05.228 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:05.228 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:05.483 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:05.483 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:05.483 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:06.163 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:06.164 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:06.164 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:06.328 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:06.328 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:06.328 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:07.164 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:07.164 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:07.164 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:07.181 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:07.181 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:07.181 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:08.017 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:08.017 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:08.017 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:08.033 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:08.034 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:08.034 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:08.883 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:08.883 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:08.883 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:09.067 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:09.067 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:09.067 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:09.883 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:09.883 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:09.884 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:10.044 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:10.044 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:10.045 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:10.886 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:10.887 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:10.887 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:11.053 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:11.053 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:11.053 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:11.804 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:11.804 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:11.804 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:12.054 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:12.054 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:12.054 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:12.197 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://:8595/ma-service/api/v1/transactions/transaction/detail?transactionId=********&paymentMethod=QT&transactionType=Purchase
2025-07-18 04:14:12.197 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-07-18 04:14:12.197 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-18 04:14:12.197 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-18 04:14:12.198 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-18 04:14:12.198 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-18 04:14:12.198 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-18 04:14:12.198 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-07-18 04:14:12.198 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-18 04:14:12.198 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-18 04:14:12.198 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-18 04:14:12.198 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-18 04:14:12.198 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-18 04:14:12.198 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-18 04:14:12.198 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-18 04:14:12.198 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-07-18 04:14:12.198 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTM3MTE4MTksInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.uFHvM51Hq3zbz_0QNIPAnz5t7fDwjs8LqXvOtvvcZrA
2025-07-18 04:14:12.198 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:14:12.198 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 97bca157-c986-48e0-873c-4f6dea1989a2
2025-07-18 04:14:12.198 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8595
2025-07-18 04:14:12.198 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-18 04:14:12.198 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-18 04:14:12.807 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:12.807 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:12.807 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:13.055 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:13.055 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:13.055 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:13.660 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:13.660 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:13.660 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:14.056 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:14.056 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:14.056 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:14.610 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:14.610 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:14.610 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:15.059 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:15.059 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:15.059 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:15.613 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:15.613 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:15.613 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:15.969 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:15.969 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:15.969 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:16.574 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:16.575 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:16.575 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:16.992 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:16.992 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:16.992 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:17.616 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:17.616 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:17.616 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:17.993 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:17.993 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:17.993 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:18.569 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:18.569 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:18.569 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:18.895 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:18.896 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:18.896 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:19.572 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:19.572 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:19.573 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:19.860 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:19.860 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:19.860 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:20.596 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:20.596 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:20.596 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:20.861 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:20.861 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:20.861 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:21.487 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:21.487 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:21.487 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:21.878 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:21.878 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:21.878 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:22.470 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:22.470 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:22.471 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:22.880 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:22.880 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:22.880 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:23.374 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:23.375 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:23.375 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:23.881 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:23.881 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:23.881 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:24.378 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:24.378 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:24.378 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:24.734 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:24.734 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:24.734 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:25.391 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:25.391 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:25.391 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:25.737 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:14:25.737 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-07-18 04:14:25.737 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-07-18 04:14:26.409 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-07-18 04:15:19.439 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 3994257 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-07-18 04:15:19.444 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-07-18 04:15:20.221 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-07-18 04:15:20.400 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 172 ms. Found 2 R2DBC repository interfaces.
2025-07-18 04:15:21.980 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-07-18 04:15:22.263 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@23396fc0
2025-07-18 04:15:22.265 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-07-18 04:15:22.313 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-07-18 04:15:22.427 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@56f9de3b
2025-07-18 04:15:22.427 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-07-18 04:15:22.537 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-07-18 04:15:22.545 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-07-18 04:15:22.548 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-07-18 04:15:22.549 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-07-18 04:15:22.551 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-07-18 04:15:22.551 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-07-18 04:15:23.421 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8591 (http)
2025-07-18 04:15:23.613 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 4.858 seconds (process running for 6.336)
2025-07-18 04:15:34.923 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://localhost:8591/ma-service/api/v1/transactions/transaction/detail?transactionId=********&paymentMethod=QT&transactionType=Purchase
2025-07-18 04:15:34.925 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-07-18 04:15:34.926 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-18 04:15:34.926 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-18 04:15:34.926 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-18 04:15:34.926 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-18 04:15:34.926 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-18 04:15:34.926 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-07-18 04:15:34.926 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-18 04:15:34.926 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-18 04:15:34.926 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-18 04:15:34.926 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-18 04:15:34.926 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-18 04:15:34.926 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-18 04:15:34.927 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-18 04:15:34.927 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-07-18 04:15:34.927 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTM3MTE4MTksInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.uFHvM51Hq3zbz_0QNIPAnz5t7fDwjs8LqXvOtvvcZrA
2025-07-18 04:15:34.927 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:15:34.927 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 734402ca-8254-45a6-a5b8-4dba9f139b81
2025-07-18 04:15:34.927 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8591
2025-07-18 04:15:34.927 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-18 04:15:34.933 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-18 04:15:38.501 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:15:38.502 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-18 04:15:38.565 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:15:38.607 [reactor-http-epoll-3] ERROR v.o.t.s.i.TransactionOldServiceImpl - Error call api get transaction detail: 
java.net.ConnectException: null
	at java.net.http/jdk.internal.net.http.HttpClientImpl.send(HttpClientImpl.java:951)
	at java.net.http/jdk.internal.net.http.HttpClientFacade.send(HttpClientFacade.java:133)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.callApiGetTransDetail(TransactionOldServiceImpl.java:1621)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.getTransactionDetail(TransactionOldServiceImpl.java:481)
	at vn.onepay.transaction.controller.TransactionController.getTransactionDetail(TransactionController.java:148)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onComplete(FluxDefaultIfEmpty.java:134)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:85)
	at reactor.core.publisher.MonoSupplier$MonoSupplierSubscription.request(MonoSupplier.java:148)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
	at reactor.core.publisher.MonoSupplier.subscribe(MonoSupplier.java:48)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:82)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:146)
	at reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onSubscribe(MonoIgnoreThen.java:135)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onComplete(MonoFlatMap.java:189)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:121)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
	at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:259)
	at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:865)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.request(FluxMapFuseable.java:171)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:339)
	at reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
	at reactor.core.publisher.MonoNext$NextSubscriber.onSubscribe(MonoNext.java:70)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:201)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:83)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
	at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
	at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
	at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
	at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.net.ConnectException: null
	at java.net.http/jdk.internal.net.http.common.Utils.toConnectException(Utils.java:1041)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.connectAsync(PlainHttpConnection.java:227)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.checkRetryConnect(PlainHttpConnection.java:280)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.lambda$connectAsync$2(PlainHttpConnection.java:238)
	at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
	at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1773)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	... 1 common frames omitted
Caused by: java.nio.channels.ClosedChannelException: null
	at java.base/sun.nio.ch.SocketChannelImpl.ensureOpen(SocketChannelImpl.java:202)
	at java.base/sun.nio.ch.SocketChannelImpl.beginConnect(SocketChannelImpl.java:786)
	at java.base/sun.nio.ch.SocketChannelImpl.connect(SocketChannelImpl.java:874)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.lambda$connectAsync$1(PlainHttpConnection.java:210)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:571)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.connectAsync(PlainHttpConnection.java:212)
	... 9 common frames omitted
2025-07-18 04:15:38.610 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-18 04:15:38.611 [reactor-http-epoll-3] ERROR v.o.t.s.i.TransactionOldServiceImpl - Error getTransactionDetail: 
java.lang.IllegalArgumentException: argument "content" is null
	at com.fasterxml.jackson.databind.ObjectMapper._assertNotNull(ObjectMapper.java:5086)
	at com.fasterxml.jackson.databind.ObjectMapper.readTree(ObjectMapper.java:3279)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.getTransactionDetail(TransactionOldServiceImpl.java:484)
	at vn.onepay.transaction.controller.TransactionController.getTransactionDetail(TransactionController.java:148)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onComplete(FluxDefaultIfEmpty.java:134)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:85)
	at reactor.core.publisher.MonoSupplier$MonoSupplierSubscription.request(MonoSupplier.java:148)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
	at reactor.core.publisher.MonoSupplier.subscribe(MonoSupplier.java:48)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:82)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:146)
	at reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onSubscribe(MonoIgnoreThen.java:135)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onComplete(MonoFlatMap.java:189)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:121)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
	at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:259)
	at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:865)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.request(FluxMapFuseable.java:171)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:339)
	at reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
	at reactor.core.publisher.MonoNext$NextSubscriber.onSubscribe(MonoNext.java:70)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:201)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:83)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
	at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
	at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
	at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
	at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-18 04:15:38.612 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-18 04:15:45.579 [reactor-http-epoll-3] ERROR v.o.t.e.GlobalExceptionHandler - Unhandled exception: 
org.springframework.web.server.ResponseStatusException: 404 NOT_FOUND "Transaction ID not found"
	at vn.onepay.transaction.controller.TransactionController.getTransactionDetail(TransactionController.java:150)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#getTransactionDetail(ServerWebExchange, String, String, String, String) [DispatcherHandler]
Original Stack Trace:
		at vn.onepay.transaction.controller.TransactionController.getTransactionDetail(TransactionController.java:150)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onComplete(FluxDefaultIfEmpty.java:134)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:85)
		at reactor.core.publisher.MonoSupplier$MonoSupplierSubscription.request(MonoSupplier.java:148)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
		at reactor.core.publisher.MonoSupplier.subscribe(MonoSupplier.java:48)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:82)
		at reactor.core.publisher.Operators.complete(Operators.java:137)
		at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:146)
		at reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onSubscribe(MonoIgnoreThen.java:135)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onComplete(MonoFlatMap.java:189)
		at reactor.core.publisher.Operators.complete(Operators.java:137)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:121)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:259)
		at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:865)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.request(FluxMapFuseable.java:171)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:339)
		at reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
		at reactor.core.publisher.MonoNext$NextSubscriber.onSubscribe(MonoNext.java:70)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:201)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:83)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-18 04:15:45.588 [reactor-http-epoll-3] WARN  o.s.w.r.r.m.a.RequestMappingHandlerAdapter - [37d33f8f-1] Failure in @ExceptionHandler vn.onepay.transaction.exception.GlobalExceptionHandler#handleNotFound(ResponseStatusException, ServerWebExchange)
org.springframework.web.server.ResponseStatusException: 404 NOT_FOUND "Not found"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleNotFound(GlobalExceptionHandler.java:23)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
	at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onSubscribe(MonoPeekTerminal.java:152)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:136)
	at reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onNext(FluxPeek.java:200)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.complete(MonoIgnoreThen.java:294)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onNext(MonoIgnoreThen.java:188)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onComplete(FluxDefaultIfEmpty.java:134)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:85)
	at reactor.core.publisher.MonoSupplier$MonoSupplierSubscription.request(MonoSupplier.java:148)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
	at reactor.core.publisher.MonoSupplier.subscribe(MonoSupplier.java:48)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:82)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:146)
	at reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onSubscribe(MonoIgnoreThen.java:135)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onComplete(MonoFlatMap.java:189)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:121)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
	at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:259)
	at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:865)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.request(FluxMapFuseable.java:171)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:339)
	at reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
	at reactor.core.publisher.MonoNext$NextSubscriber.onSubscribe(MonoNext.java:70)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:201)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:83)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
	at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
	at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
	at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
	at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-18 04:15:45.589 [reactor-http-epoll-3] ERROR v.o.t.exception.RequestLoggingFilter - Request error: GET http://localhost:8591/ma-service/api/v1/transactions/transaction/detail?transactionId=********&paymentMethod=QT&transactionType=Purchase - Exception: 404 NOT_FOUND "Transaction ID not found"
org.springframework.web.server.ResponseStatusException: 404 NOT_FOUND "Transaction ID not found"
	at vn.onepay.transaction.controller.TransactionController.getTransactionDetail(TransactionController.java:150)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#getTransactionDetail(ServerWebExchange, String, String, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x000076e6786068e8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at vn.onepay.transaction.controller.TransactionController.getTransactionDetail(TransactionController.java:150)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onComplete(FluxDefaultIfEmpty.java:134)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:85)
		at reactor.core.publisher.MonoSupplier$MonoSupplierSubscription.request(MonoSupplier.java:148)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
		at reactor.core.publisher.MonoSupplier.subscribe(MonoSupplier.java:48)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:82)
		at reactor.core.publisher.Operators.complete(Operators.java:137)
		at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:146)
		at reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onSubscribe(MonoIgnoreThen.java:135)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onComplete(MonoFlatMap.java:189)
		at reactor.core.publisher.Operators.complete(Operators.java:137)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:121)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:259)
		at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:865)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.request(FluxMapFuseable.java:171)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:339)
		at reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
		at reactor.core.publisher.MonoNext$NextSubscriber.onSubscribe(MonoNext.java:70)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:201)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:83)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-18 04:16:14.176 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://localhost:8591/ma-service/api/v1/transactions/transaction/detail?transactionId=********&paymentMethod=QT&transactionType=Purchase
2025-07-18 04:16:14.176 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-07-18 04:16:14.176 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-18 04:16:14.176 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-18 04:16:14.176 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-18 04:16:14.176 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-18 04:16:14.176 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-18 04:16:14.177 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-07-18 04:16:14.177 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-18 04:16:14.177 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-18 04:16:14.177 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-18 04:16:14.177 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-18 04:16:14.177 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-18 04:16:14.177 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-18 04:16:14.177 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-18 04:16:14.177 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-07-18 04:16:14.177 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTM3MTE4MTksInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.uFHvM51Hq3zbz_0QNIPAnz5t7fDwjs8LqXvOtvvcZrA
2025-07-18 04:16:14.177 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:16:14.177 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 425ba55a-263c-4207-b9f9-4060a8fa7925
2025-07-18 04:16:14.177 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8591
2025-07-18 04:16:14.178 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-18 04:16:14.178 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-18 04:16:18.441 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:16:33.266 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-18 04:16:33.269 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:18:17.585 [reactor-http-epoll-3] ERROR v.o.t.s.i.TransactionOldServiceImpl - Error call api get transaction detail: 
java.net.ConnectException: null
	at java.net.http/jdk.internal.net.http.HttpClientImpl.send(HttpClientImpl.java:951)
	at java.net.http/jdk.internal.net.http.HttpClientFacade.send(HttpClientFacade.java:133)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.callApiGetTransDetail(TransactionOldServiceImpl.java:1621)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.getTransactionDetail(TransactionOldServiceImpl.java:481)
	at vn.onepay.transaction.controller.TransactionController.getTransactionDetail(TransactionController.java:148)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onComplete(FluxDefaultIfEmpty.java:134)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:85)
	at reactor.core.publisher.MonoSupplier$MonoSupplierSubscription.request(MonoSupplier.java:148)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
	at reactor.core.publisher.MonoSupplier.subscribe(MonoSupplier.java:48)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:82)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:146)
	at reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onSubscribe(MonoIgnoreThen.java:135)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onComplete(MonoFlatMap.java:189)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:121)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
	at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:259)
	at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:865)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.request(FluxMapFuseable.java:171)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:339)
	at reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
	at reactor.core.publisher.MonoNext$NextSubscriber.onSubscribe(MonoNext.java:70)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:201)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:83)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
	at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
	at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
	at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
	at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.net.ConnectException: null
	at java.net.http/jdk.internal.net.http.common.Utils.toConnectException(Utils.java:1041)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.connectAsync(PlainHttpConnection.java:227)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.checkRetryConnect(PlainHttpConnection.java:280)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.lambda$connectAsync$2(PlainHttpConnection.java:238)
	at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
	at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1773)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	... 1 common frames omitted
Caused by: java.nio.channels.ClosedChannelException: null
	at java.base/sun.nio.ch.SocketChannelImpl.ensureOpen(SocketChannelImpl.java:202)
	at java.base/sun.nio.ch.SocketChannelImpl.beginConnect(SocketChannelImpl.java:786)
	at java.base/sun.nio.ch.SocketChannelImpl.connect(SocketChannelImpl.java:874)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.lambda$connectAsync$1(PlainHttpConnection.java:210)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:571)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.connectAsync(PlainHttpConnection.java:212)
	... 9 common frames omitted
2025-07-18 04:18:17.594 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-18 04:18:17.595 [reactor-http-epoll-3] ERROR v.o.t.s.i.TransactionOldServiceImpl - Error getTransactionDetail: 
java.lang.IllegalArgumentException: argument "content" is null
	at com.fasterxml.jackson.databind.ObjectMapper._assertNotNull(ObjectMapper.java:5086)
	at com.fasterxml.jackson.databind.ObjectMapper.readTree(ObjectMapper.java:3279)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.getTransactionDetail(TransactionOldServiceImpl.java:484)
	at vn.onepay.transaction.controller.TransactionController.getTransactionDetail(TransactionController.java:148)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onComplete(FluxDefaultIfEmpty.java:134)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:85)
	at reactor.core.publisher.MonoSupplier$MonoSupplierSubscription.request(MonoSupplier.java:148)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
	at reactor.core.publisher.MonoSupplier.subscribe(MonoSupplier.java:48)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:82)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:146)
	at reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onSubscribe(MonoIgnoreThen.java:135)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onComplete(MonoFlatMap.java:189)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:121)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
	at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:259)
	at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:865)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.request(FluxMapFuseable.java:171)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:339)
	at reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
	at reactor.core.publisher.MonoNext$NextSubscriber.onSubscribe(MonoNext.java:70)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:201)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:83)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
	at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
	at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
	at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
	at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-18 04:18:17.601 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-18 04:18:25.401 [reactor-http-epoll-3] ERROR v.o.t.e.GlobalExceptionHandler - Unhandled exception: 
org.springframework.web.server.ResponseStatusException: 404 NOT_FOUND "Transaction ID not found"
	at vn.onepay.transaction.controller.TransactionController.getTransactionDetail(TransactionController.java:150)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#getTransactionDetail(ServerWebExchange, String, String, String, String) [DispatcherHandler]
Original Stack Trace:
		at vn.onepay.transaction.controller.TransactionController.getTransactionDetail(TransactionController.java:150)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onComplete(FluxDefaultIfEmpty.java:134)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:85)
		at reactor.core.publisher.MonoSupplier$MonoSupplierSubscription.request(MonoSupplier.java:148)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
		at reactor.core.publisher.MonoSupplier.subscribe(MonoSupplier.java:48)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:82)
		at reactor.core.publisher.Operators.complete(Operators.java:137)
		at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:146)
		at reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onSubscribe(MonoIgnoreThen.java:135)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onComplete(MonoFlatMap.java:189)
		at reactor.core.publisher.Operators.complete(Operators.java:137)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:121)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:259)
		at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:865)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.request(FluxMapFuseable.java:171)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:339)
		at reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
		at reactor.core.publisher.MonoNext$NextSubscriber.onSubscribe(MonoNext.java:70)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:201)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:83)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-18 04:18:25.403 [reactor-http-epoll-3] WARN  o.s.w.r.r.m.a.RequestMappingHandlerAdapter - [37d33f8f-2] Failure in @ExceptionHandler vn.onepay.transaction.exception.GlobalExceptionHandler#handleNotFound(ResponseStatusException, ServerWebExchange)
org.springframework.web.server.ResponseStatusException: 404 NOT_FOUND "Not found"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleNotFound(GlobalExceptionHandler.java:23)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
	at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onSubscribe(MonoPeekTerminal.java:152)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:136)
	at reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onNext(FluxPeek.java:200)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.complete(MonoIgnoreThen.java:294)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onNext(MonoIgnoreThen.java:188)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onComplete(FluxDefaultIfEmpty.java:134)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:85)
	at reactor.core.publisher.MonoSupplier$MonoSupplierSubscription.request(MonoSupplier.java:148)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
	at reactor.core.publisher.MonoSupplier.subscribe(MonoSupplier.java:48)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:82)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:146)
	at reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onSubscribe(MonoIgnoreThen.java:135)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onComplete(MonoFlatMap.java:189)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:121)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
	at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:259)
	at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:865)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.request(FluxMapFuseable.java:171)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:339)
	at reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
	at reactor.core.publisher.MonoNext$NextSubscriber.onSubscribe(MonoNext.java:70)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:201)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:83)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
	at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
	at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
	at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
	at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-18 04:18:25.404 [reactor-http-epoll-3] ERROR v.o.t.exception.RequestLoggingFilter - Request error: GET http://localhost:8591/ma-service/api/v1/transactions/transaction/detail?transactionId=********&paymentMethod=QT&transactionType=Purchase - Exception: 404 NOT_FOUND "Transaction ID not found"
org.springframework.web.server.ResponseStatusException: 404 NOT_FOUND "Transaction ID not found"
	at vn.onepay.transaction.controller.TransactionController.getTransactionDetail(TransactionController.java:150)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#getTransactionDetail(ServerWebExchange, String, String, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x000076e6786068e8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at vn.onepay.transaction.controller.TransactionController.getTransactionDetail(TransactionController.java:150)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onComplete(FluxDefaultIfEmpty.java:134)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:85)
		at reactor.core.publisher.MonoSupplier$MonoSupplierSubscription.request(MonoSupplier.java:148)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
		at reactor.core.publisher.MonoSupplier.subscribe(MonoSupplier.java:48)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:82)
		at reactor.core.publisher.Operators.complete(Operators.java:137)
		at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:146)
		at reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onSubscribe(MonoIgnoreThen.java:135)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onComplete(MonoFlatMap.java:189)
		at reactor.core.publisher.Operators.complete(Operators.java:137)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:121)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:259)
		at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:865)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.request(FluxMapFuseable.java:171)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:339)
		at reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
		at reactor.core.publisher.MonoNext$NextSubscriber.onSubscribe(MonoNext.java:70)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:201)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:83)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-18 04:18:27.277 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://localhost:8591/ma-service/api/v1/transactions/transaction/detail?transactionId=********&paymentMethod=QT&transactionType=Purchase
2025-07-18 04:18:27.278 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-07-18 04:18:27.278 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-18 04:18:27.278 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-18 04:18:27.278 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-18 04:18:27.278 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-18 04:18:27.278 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-18 04:18:27.278 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-07-18 04:18:27.278 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-18 04:18:27.278 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-18 04:18:27.278 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-18 04:18:27.278 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-18 04:18:27.278 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-18 04:18:27.279 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-18 04:18:27.279 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-18 04:18:27.279 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-07-18 04:18:27.279 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTM3MTE4MTksInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.uFHvM51Hq3zbz_0QNIPAnz5t7fDwjs8LqXvOtvvcZrA
2025-07-18 04:18:27.279 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:18:27.279 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 1759e36d-c020-41ff-8a72-1f8d1e4867dc
2025-07-18 04:18:27.279 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8591
2025-07-18 04:18:27.279 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-18 04:18:27.279 [reactor-http-epoll-4] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-18 04:18:31.985 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:18:33.366 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-18 04:18:33.367 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:18:33.421 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"********","original_transaction_id":"********","transaction_type":"Purchase","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"4","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1752*********-*********","amount":{"total":123000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"531358****3430","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"26"}},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"831000"},"transaction_time":"2025-07-17T14:45:50Z","transaction_status":"0","transaction_ref_number":"5864104180072653227050","ip_address":"***************","ip_proxy":"N","bin_country":"AUSTRALIA","csc_result_code":"M","enrolled_3ds":"Y","avs":{"address":"","result_code":"Y","province":"","city":"","zip_code":"","country":""},"verification_security_level":"05","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"NATIONAL AUSTRALIA BANK LIMITED","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","acqResponseCode":"","status3ds":"Y","riskOverAllResult":"ACCEPT","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","contractType":"2B"}
2025-07-18 04:18:36.121 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-18 04:18:36.136 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-18 04:18:36.146 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-18 04:18:36.172 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-17T14:45:50, orderAmount=123000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Purchase, paymentMethod=QT, transactionCreatedTime=2025-07-17T14:45:50, transactionCompletedTime=2025-07-17T14:45:50, transactionAmount=123000.0, transactionCurrency=VND, transactionStatus=Successful, responseCode=0, merchantTransRef=TEST_1752*********-*********, paymentChannel=null, installmentStatus=), paymentMethod=PaymentMethod(cardNumber=531358****3430, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, acquirer=4, cardExpiry=1126, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=AUSTRALIA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null), actions=null)
2025-07-18 04:18:36.172 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-18 04:18:36.172 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-18 04:18:36.173 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-18 04:18:36.419 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-18 04:18:36.419 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - list merchant from permission: [TESTENETVIET, TESTIDTEST1, TESTIDTEST2, TESTIDTEST3, TESTIDTEST4, TESTIDTEST6, TESTIDTEST5, OPTESTHB, GIANGTESTASSIGN, OP_TESTCONFIG, TEST3DS2_LT, TEST3DS2_MK, TEST3DS2_TN, OP_VPBMPGS3, TESTTGDD3D2, ONEPAYHB, TEST_BNPL, FDSFDSF, TESTJBAVND, TEST_BNPL2, TESTPCI, tcreate2, thanhdanh, danh1289, TESTTGDD, OP_SAMSUNG, BIDVEXPIRE, AUTOREFUNDUSD, danh5140, HVTT, SIVU1, SIVU, D_TEST, OP_TESTVND, OP_VND01, TESTONEPAY1, TESTHANH3, TESTBIDVV2, TESTVF, TESTIDR, TESTCNY, TESTSCB3D2, TESTTRAGOP2, TESTAPPLEND, TESTTWD, TESTSGD, TESTAPPLE, ONEPAYHM, OPMPAY, OP_TEST3DS2V, TESTTNSUSD, TESTTGPHYTO, TESTINVOICE, OP_MPAYVN1, TESTBIDV1, TESTONEPAY4, danh128950, OP_TESTAUTH, TESTONEPAY2, TESTINDOCHINAUSD, TESTMCD, HOABT, TESTNAMVH, TESTONEPAY20, OP_TESTOP20, OP_MPGSUSD, TESTSACOMCBV, TESTBIDV, TEST3DSMPGS, OP_TEST3DS, MIGS3DS, TESTBIDVCSU2, OP_CYBSVND, OP_CYBSUSD, OP_SACOMCBSV, OP_SACOMCBSU, 9.70436E+15, TESTSACOMCBU, OP_TESTUSD, ONEPAYMT, OPPREPAID, TESTAPPAY, ANHTVTEST20, TESTTEROP, 9.70436E+15, TESTATM, TESTDUONG, OP_SACOMTRVU, TESTGATEWAY, TESTAPP, TESTUSD, TESTPR, TESTVFVND, TCREATE1, TCREATE2, TCREATE3, TCREATE4, OTELBIDV, TESTMISA, TESTEXPIRED, TESTHANH5, TESTHANH6, TESTHANH7, thanhdanh2211, VTL, TESTKRW, TESTJPY, 1.23457E+19, TESTQNM, thanhdanh12, danh12890, danh510, danh3140, AHIHI, OP_SACOMTRVV, OP_MPGSVND, danh12891, TESTSAMSUNGPL, TESTSAMSUNG, DPVD, TESTOP3D2, danh31240, SIVU_DANH, THL, TESTONEPAY3, TESTTHB, TESTONEPAY7, TESTAXA1, TESTHC1, TESTMEGA1, TESTONEPAY9, TESTONEPAY10, TESTMYR, TESTOCB, TESTONEPAY, ACVNC, AOSVNBANK, AOSVNC, TESTAXA, TESTHC, TESTMEGA, TESTVTB3D2, TESTVJU, SHIHANLIFETG, TESTBNPL, TESTONEPAYUSD, FALSE, MPAYVND, OP_TESTSS, OP_SACOM2SCU, INVOICETG, OP_VPBMPGS1, OP_VPBMPGS2, TESTVCB3D2O, TESTTNS, TESTTOKEN, TESTTOKENUSD, OP_VCBVND, OP_VCBUSD, OP_MPAYVND3D, TESTVCB3D2, TESTBIDVCBSV, TESTBIDVCBSU, TESTBIDVU3, TESTBIDVV3, TESTTHSC, TESTLZD, OP_MPAYVND, TESTMAFC, TESTTRAGOP, OP_SACOM2SCV, TESTDMX, TESTSAPO2, TESTSAPO]
2025-07-18 04:18:36.420 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-18 04:18:36.420 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-07-18 04:18:47.667 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call getTransactionHistory-----------
2025-07-18 04:18:47.679 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: originalTransactionId=******** ,paymentMethod=QT ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:18:47.687 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionHistory MA-----------
2025-07-18 04:18:47.708 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/********/history ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:18:47.756 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY API: response code=200 ,response body={"transactions":[{"transaction_id":"********","original_transaction_id":"********","response_code":"0","reference_number":"5864104180072653227050","merchant_transaction_ref":"TEST_1752*********-*********","transaction_type":"Purchase","amount":{"total":123000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-07-17T14:45:50Z","advance_status":"Successful","financial_transaction_id":"5864104180072653227050","note":"","parent_id":"********","dadId":"","subHistories":""}]}
2025-07-18 04:18:47.760 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionHistory MA------------
2025-07-18 04:18:47.767 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY INTERNATIONAL: {"transactions":[{"transaction_id":"********","original_transaction_id":"********","response_code":"0","reference_number":"5864104180072653227050","merchant_transaction_ref":"TEST_1752*********-*********","transaction_type":"Purchase","amount":{"total":123000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-07-17T14:45:50Z","advance_status":"Successful","financial_transaction_id":"5864104180072653227050","note":"","parent_id":"********","dadId":"","subHistories":""}]}
2025-07-18 04:18:47.817 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call getTransactionHistory------------
2025-07-18 04:18:59.050 [reactor-http-epoll-4] INFO  v.o.t.repository.AcquirerRepository - Starting getListAcquirer - calling function ONEDATA.GET_LIST_ACQUIRER
2025-07-18 04:20:01.458 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - Error when checkPermissionByUserId: transactionId=********, error={}
java.lang.IllegalStateException: block()/blockFirst()/blockLast() are blocking, which is not supported in thread reactor-http-epoll-4
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:87)
	at reactor.core.publisher.Mono.block(Mono.java:1779)
	at vn.onepay.transaction.util.CheckPermissionUtil.checkPermission(CheckPermissionUtil.java:56)
	at vn.onepay.transaction.util.CheckPermissionUtil.checkPerActionVoid(CheckPermissionUtil.java:107)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.checkPerAndActionByUserId(TransactionOldServiceImpl.java:2559)
	at vn.onepay.transaction.controller.TransactionController.lambda$2(TransactionController.java:159)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-18 04:20:14.636 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Response: GET http://localhost:8591/ma-service/api/v1/transactions/transaction/detail?transactionId=********&paymentMethod=QT&transactionType=Purchase - Status: 200 OK
2025-07-18 04:21:20.405 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 3998592 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-07-18 04:21:20.408 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-07-18 04:21:21.213 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-07-18 04:21:21.444 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 222 ms. Found 2 R2DBC repository interfaces.
2025-07-18 04:21:23.054 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-07-18 04:21:23.342 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@58aa5c94
2025-07-18 04:21:23.344 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-07-18 04:21:23.408 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-07-18 04:21:23.511 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@73b0ed03
2025-07-18 04:21:23.512 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-07-18 04:21:23.621 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-07-18 04:21:23.631 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-07-18 04:21:23.633 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-07-18 04:21:23.634 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-07-18 04:21:23.636 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-07-18 04:21:23.637 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-07-18 04:21:24.774 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8591 (http)
2025-07-18 04:21:24.989 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 5.282 seconds (process running for 6.838)
2025-07-18 04:21:31.750 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://localhost:8591/ma-service/api/v1/transactions/transaction/detail?transactionId=********&paymentMethod=QT&transactionType=Purchase
2025-07-18 04:21:31.751 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-07-18 04:21:31.751 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-18 04:21:31.751 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-18 04:21:31.751 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-18 04:21:31.751 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-18 04:21:31.752 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-18 04:21:31.752 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-07-18 04:21:31.752 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-18 04:21:31.752 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-18 04:21:31.752 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-18 04:21:31.752 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-18 04:21:31.752 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-18 04:21:31.752 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-18 04:21:31.752 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-18 04:21:31.753 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-07-18 04:21:31.753 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTM3MTE4MTksInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.uFHvM51Hq3zbz_0QNIPAnz5t7fDwjs8LqXvOtvvcZrA
2025-07-18 04:21:31.753 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:21:31.753 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = c40d9ac7-d058-4800-8780-b41be3fac6e5
2025-07-18 04:21:31.753 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8591
2025-07-18 04:21:31.753 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-18 04:21:31.757 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-18 04:21:36.387 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:21:37.869 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-18 04:21:37.946 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:21:38.026 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"********","original_transaction_id":"********","transaction_type":"Purchase","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"4","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1752*********-*********","amount":{"total":123000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"531358****3430","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"26"}},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"831000"},"transaction_time":"2025-07-17T14:45:50Z","transaction_status":"0","transaction_ref_number":"5864104180072653227050","ip_address":"***************","ip_proxy":"N","bin_country":"AUSTRALIA","csc_result_code":"M","enrolled_3ds":"Y","avs":{"address":"","result_code":"Y","province":"","city":"","zip_code":"","country":""},"verification_security_level":"05","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"NATIONAL AUSTRALIA BANK LIMITED","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","acqResponseCode":"","status3ds":"Y","riskOverAllResult":"ACCEPT","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","contractType":"2B"}
2025-07-18 04:21:39.049 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-18 04:21:39.067 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-18 04:21:39.076 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-18 04:21:39.095 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-17T14:45:50, orderAmount=123000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Purchase, paymentMethod=QT, transactionCreatedTime=2025-07-17T14:45:50, transactionCompletedTime=2025-07-17T14:45:50, transactionAmount=123000.0, transactionCurrency=VND, transactionStatus=Successful, responseCode=0, merchantTransRef=TEST_1752*********-*********, paymentChannel=null, installmentStatus=), paymentMethod=PaymentMethod(cardNumber=531358****3430, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, acquirer=4, cardExpiry=1126, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=AUSTRALIA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null), actions=null)
2025-07-18 04:21:39.095 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-18 04:21:39.095 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-18 04:21:39.096 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-18 04:21:39.297 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-18 04:21:39.298 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [TESTENETVIET, TESTIDTEST1, TESTIDTEST2, TESTIDTEST3, TESTIDTEST4, TESTIDTEST6, TESTIDTEST5, OPTESTHB, GIANGTESTASSIGN, OP_TESTCONFIG, TEST3DS2_LT, TEST3DS2_MK, TEST3DS2_TN, OP_VPBMPGS3, TESTTGDD3D2, ONEPAYHB, TEST_BNPL, FDSFDSF, TESTJBAVND, TEST_BNPL2, TESTPCI, tcreate2, thanhdanh, danh1289, TESTTGDD, OP_SAMSUNG, BIDVEXPIRE, AUTOREFUNDUSD, danh5140, HVTT, SIVU1, SIVU, D_TEST, OP_TESTVND, OP_VND01, TESTONEPAY1, TESTHANH3, TESTBIDVV2, TESTVF, TESTIDR, TESTCNY, TESTSCB3D2, TESTTRAGOP2, TESTAPPLEND, TESTTWD, TESTSGD, TESTAPPLE, ONEPAYHM, OPMPAY, OP_TEST3DS2V, TESTTNSUSD, TESTTGPHYTO, TESTINVOICE, OP_MPAYVN1, TESTBIDV1, TESTONEPAY4, danh128950, OP_TESTAUTH, TESTONEPAY2, TESTINDOCHINAUSD, TESTMCD, HOABT, TESTNAMVH, TESTONEPAY20, OP_TESTOP20, OP_MPGSUSD, TESTSACOMCBV, TESTBIDV, TEST3DSMPGS, OP_TEST3DS, MIGS3DS, TESTBIDVCSU2, OP_CYBSVND, OP_CYBSUSD, OP_SACOMCBSV, OP_SACOMCBSU, 9.70436E+15, TESTSACOMCBU, OP_TESTUSD, ONEPAYMT, OPPREPAID, TESTAPPAY, ANHTVTEST20, TESTTEROP, 9.70436E+15, TESTATM, TESTDUONG, OP_SACOMTRVU, TESTGATEWAY, TESTAPP, TESTUSD, TESTPR, TESTVFVND, TCREATE1, TCREATE2, TCREATE3, TCREATE4, OTELBIDV, TESTMISA, TESTEXPIRED, TESTHANH5, TESTHANH6, TESTHANH7, thanhdanh2211, VTL, TESTKRW, TESTJPY, 1.23457E+19, TESTQNM, thanhdanh12, danh12890, danh510, danh3140, AHIHI, OP_SACOMTRVV, OP_MPGSVND, danh12891, TESTSAMSUNGPL, TESTSAMSUNG, DPVD, TESTOP3D2, danh31240, SIVU_DANH, THL, TESTONEPAY3, TESTTHB, TESTONEPAY7, TESTAXA1, TESTHC1, TESTMEGA1, TESTONEPAY9, TESTONEPAY10, TESTMYR, TESTOCB, TESTONEPAY, ACVNC, AOSVNBANK, AOSVNC, TESTAXA, TESTHC, TESTMEGA, TESTVTB3D2, TESTVJU, SHIHANLIFETG, TESTBNPL, TESTONEPAYUSD, FALSE, MPAYVND, OP_TESTSS, OP_SACOM2SCU, INVOICETG, OP_VPBMPGS1, OP_VPBMPGS2, TESTVCB3D2O, TESTTNS, TESTTOKEN, TESTTOKENUSD, OP_VCBVND, OP_VCBUSD, OP_MPAYVND3D, TESTVCB3D2, TESTBIDVCBSV, TESTBIDVCBSU, TESTBIDVU3, TESTBIDVV3, TESTTHSC, TESTLZD, OP_MPAYVND, TESTMAFC, TESTTRAGOP, OP_SACOM2SCV, TESTDMX, TESTSAPO2, TESTSAPO]
2025-07-18 04:21:39.298 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-18 04:21:39.299 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-07-18 04:21:40.818 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call getTransactionHistory-----------
2025-07-18 04:21:40.818 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: originalTransactionId=******** ,paymentMethod=QT ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:21:40.819 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionHistory MA-----------
2025-07-18 04:21:40.819 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/********/history ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:21:40.832 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY API: response code=200 ,response body={"transactions":[{"transaction_id":"********","original_transaction_id":"********","response_code":"0","reference_number":"5864104180072653227050","merchant_transaction_ref":"TEST_1752*********-*********","transaction_type":"Purchase","amount":{"total":123000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-07-17T14:45:50Z","advance_status":"Successful","financial_transaction_id":"5864104180072653227050","note":"","parent_id":"********","dadId":"","subHistories":""}]}
2025-07-18 04:21:40.832 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionHistory MA------------
2025-07-18 04:21:40.832 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY INTERNATIONAL: {"transactions":[{"transaction_id":"********","original_transaction_id":"********","response_code":"0","reference_number":"5864104180072653227050","merchant_transaction_ref":"TEST_1752*********-*********","transaction_type":"Purchase","amount":{"total":123000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-07-17T14:45:50Z","advance_status":"Successful","financial_transaction_id":"5864104180072653227050","note":"","parent_id":"********","dadId":"","subHistories":""}]}
2025-07-18 04:21:40.836 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call getTransactionHistory------------
2025-07-18 04:21:40.844 [reactor-http-epoll-3] INFO  v.o.t.repository.AcquirerRepository - Starting getListAcquirer - calling function ONEDATA.GET_LIST_ACQUIRER
2025-07-18 04:22:34.702 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - Error when checkPermissionByUserId: transactionId=********, error={}
java.lang.IllegalStateException: block()/blockFirst()/blockLast() are blocking, which is not supported in thread reactor-http-epoll-3
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:87)
	at reactor.core.publisher.Mono.block(Mono.java:1779)
	at vn.onepay.transaction.util.CheckPermissionUtil.checkPermission(CheckPermissionUtil.java:56)
	at vn.onepay.transaction.util.CheckPermissionUtil.checkPerActionVoid(CheckPermissionUtil.java:107)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.checkPerAndActionByUserId(TransactionOldServiceImpl.java:2559)
	at vn.onepay.transaction.controller.TransactionController.lambda$2(TransactionController.java:159)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-18 04:22:35.725 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Response: GET http://localhost:8591/ma-service/api/v1/transactions/transaction/detail?transactionId=********&paymentMethod=QT&transactionType=Purchase - Status: 200 OK
2025-07-18 04:22:38.941 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://localhost:8591/ma-service/api/v1/transactions/transaction/detail?transactionId=********&paymentMethod=QT&transactionType=Purchase
2025-07-18 04:22:38.941 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-07-18 04:22:38.941 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-18 04:22:38.941 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-18 04:22:38.941 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-18 04:22:38.941 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-18 04:22:38.941 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-18 04:22:38.942 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-07-18 04:22:38.942 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-18 04:22:38.942 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-18 04:22:38.942 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-18 04:22:38.942 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-18 04:22:38.942 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-18 04:22:38.942 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-18 04:22:38.942 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-18 04:22:38.942 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-07-18 04:22:38.942 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTM3MTE4MTksInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.uFHvM51Hq3zbz_0QNIPAnz5t7fDwjs8LqXvOtvvcZrA
2025-07-18 04:22:38.942 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:22:38.942 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 9561ebaa-6826-485e-80f4-8792577cfcc0
2025-07-18 04:22:38.942 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8591
2025-07-18 04:22:38.942 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-18 04:22:38.942 [reactor-http-epoll-4] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-18 04:22:42.886 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:22:45.945 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-18 04:22:45.947 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:22:45.965 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"********","original_transaction_id":"********","transaction_type":"Purchase","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"4","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1752*********-*********","amount":{"total":123000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"531358****3430","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"26"}},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"831000"},"transaction_time":"2025-07-17T14:45:50Z","transaction_status":"0","transaction_ref_number":"5864104180072653227050","ip_address":"***************","ip_proxy":"N","bin_country":"AUSTRALIA","csc_result_code":"M","enrolled_3ds":"Y","avs":{"address":"","result_code":"Y","province":"","city":"","zip_code":"","country":""},"verification_security_level":"05","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"NATIONAL AUSTRALIA BANK LIMITED","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","acqResponseCode":"","status3ds":"Y","riskOverAllResult":"ACCEPT","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","contractType":"2B"}
2025-07-18 04:22:45.965 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-18 04:22:45.966 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-18 04:22:45.967 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-18 04:22:45.967 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-17T14:45:50, orderAmount=123000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Purchase, paymentMethod=QT, transactionCreatedTime=2025-07-17T14:45:50, transactionCompletedTime=2025-07-17T14:45:50, transactionAmount=123000.0, transactionCurrency=VND, transactionStatus=Successful, responseCode=0, merchantTransRef=TEST_1752*********-*********, paymentChannel=null, installmentStatus=), paymentMethod=PaymentMethod(cardNumber=531358****3430, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, acquirer=4, cardExpiry=1126, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=AUSTRALIA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null), actions=null)
2025-07-18 04:22:45.967 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-18 04:22:45.967 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-18 04:22:45.967 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-18 04:22:45.980 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-18 04:22:45.980 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [TESTENETVIET, TESTIDTEST1, TESTIDTEST2, TESTIDTEST3, TESTIDTEST4, TESTIDTEST6, TESTIDTEST5, OPTESTHB, GIANGTESTASSIGN, OP_TESTCONFIG, TEST3DS2_LT, TEST3DS2_MK, TEST3DS2_TN, OP_VPBMPGS3, TESTTGDD3D2, ONEPAYHB, TEST_BNPL, FDSFDSF, TESTJBAVND, TEST_BNPL2, TESTPCI, tcreate2, thanhdanh, danh1289, TESTTGDD, OP_SAMSUNG, BIDVEXPIRE, AUTOREFUNDUSD, danh5140, HVTT, SIVU1, SIVU, D_TEST, OP_TESTVND, OP_VND01, TESTONEPAY1, TESTHANH3, TESTBIDVV2, TESTVF, TESTIDR, TESTCNY, TESTSCB3D2, TESTTRAGOP2, TESTAPPLEND, TESTTWD, TESTSGD, TESTAPPLE, ONEPAYHM, OPMPAY, OP_TEST3DS2V, TESTTNSUSD, TESTTGPHYTO, TESTINVOICE, OP_MPAYVN1, TESTBIDV1, TESTONEPAY4, danh128950, OP_TESTAUTH, TESTONEPAY2, TESTINDOCHINAUSD, TESTMCD, HOABT, TESTNAMVH, TESTONEPAY20, OP_TESTOP20, OP_MPGSUSD, TESTSACOMCBV, TESTBIDV, TEST3DSMPGS, OP_TEST3DS, MIGS3DS, TESTBIDVCSU2, OP_CYBSVND, OP_CYBSUSD, OP_SACOMCBSV, OP_SACOMCBSU, 9.70436E+15, TESTSACOMCBU, OP_TESTUSD, ONEPAYMT, OPPREPAID, TESTAPPAY, ANHTVTEST20, TESTTEROP, 9.70436E+15, TESTATM, TESTDUONG, OP_SACOMTRVU, TESTGATEWAY, TESTAPP, TESTUSD, TESTPR, TESTVFVND, TCREATE1, TCREATE2, TCREATE3, TCREATE4, OTELBIDV, TESTMISA, TESTEXPIRED, TESTHANH5, TESTHANH6, TESTHANH7, thanhdanh2211, VTL, TESTKRW, TESTJPY, 1.23457E+19, TESTQNM, thanhdanh12, danh12890, danh510, danh3140, AHIHI, OP_SACOMTRVV, OP_MPGSVND, danh12891, TESTSAMSUNGPL, TESTSAMSUNG, DPVD, TESTOP3D2, danh31240, SIVU_DANH, THL, TESTONEPAY3, TESTTHB, TESTONEPAY7, TESTAXA1, TESTHC1, TESTMEGA1, TESTONEPAY9, TESTONEPAY10, TESTMYR, TESTOCB, TESTONEPAY, ACVNC, AOSVNBANK, AOSVNC, TESTAXA, TESTHC, TESTMEGA, TESTVTB3D2, TESTVJU, SHIHANLIFETG, TESTBNPL, TESTONEPAYUSD, FALSE, MPAYVND, OP_TESTSS, OP_SACOM2SCU, INVOICETG, OP_VPBMPGS1, OP_VPBMPGS2, TESTVCB3D2O, TESTTNS, TESTTOKEN, TESTTOKENUSD, OP_VCBVND, OP_VCBUSD, OP_MPAYVND3D, TESTVCB3D2, TESTBIDVCBSV, TESTBIDVCBSU, TESTBIDVU3, TESTBIDVV3, TESTTHSC, TESTLZD, OP_MPAYVND, TESTMAFC, TESTTRAGOP, OP_SACOM2SCV, TESTDMX, TESTSAPO2, TESTSAPO]
2025-07-18 04:22:45.980 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-18 04:22:45.980 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-07-18 04:22:50.049 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call getTransactionHistory-----------
2025-07-18 04:22:50.050 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: originalTransactionId=******** ,paymentMethod=QT ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:22:50.050 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionHistory MA-----------
2025-07-18 04:22:50.051 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/********/history ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:22:50.069 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY API: response code=200 ,response body={"transactions":[{"transaction_id":"********","original_transaction_id":"********","response_code":"0","reference_number":"5864104180072653227050","merchant_transaction_ref":"TEST_1752*********-*********","transaction_type":"Purchase","amount":{"total":123000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-07-17T14:45:50Z","advance_status":"Successful","financial_transaction_id":"5864104180072653227050","note":"","parent_id":"********","dadId":"","subHistories":""}]}
2025-07-18 04:22:50.069 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionHistory MA------------
2025-07-18 04:22:50.069 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY INTERNATIONAL: {"transactions":[{"transaction_id":"********","original_transaction_id":"********","response_code":"0","reference_number":"5864104180072653227050","merchant_transaction_ref":"TEST_1752*********-*********","transaction_type":"Purchase","amount":{"total":123000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-07-17T14:45:50Z","advance_status":"Successful","financial_transaction_id":"5864104180072653227050","note":"","parent_id":"********","dadId":"","subHistories":""}]}
2025-07-18 04:22:50.070 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call getTransactionHistory------------
2025-07-18 04:22:50.071 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - Error when checkPermissionByUserId: transactionId=********, error={}
java.lang.IllegalStateException: block()/blockFirst()/blockLast() are blocking, which is not supported in thread reactor-http-epoll-3
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:87)
	at reactor.core.publisher.Mono.block(Mono.java:1779)
	at vn.onepay.transaction.util.CheckPermissionUtil.checkPermission(CheckPermissionUtil.java:56)
	at vn.onepay.transaction.util.CheckPermissionUtil.checkPerActionVoid(CheckPermissionUtil.java:107)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.checkPerAndActionByUserId(TransactionOldServiceImpl.java:2559)
	at vn.onepay.transaction.controller.TransactionController.lambda$2(TransactionController.java:159)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-18 04:22:51.222 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Response: GET http://localhost:8591/ma-service/api/v1/transactions/transaction/detail?transactionId=********&paymentMethod=QT&transactionType=Purchase - Status: 200 OK
2025-07-18 04:23:05.941 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://localhost:8591/ma-service/api/v1/transactions/transaction/detail?transactionId=********&paymentMethod=QT&transactionType=Purchase
2025-07-18 04:23:05.941 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-07-18 04:23:05.941 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-18 04:23:05.941 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-18 04:23:05.941 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-18 04:23:05.941 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-18 04:23:05.941 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-18 04:23:05.941 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-07-18 04:23:05.941 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-18 04:23:05.941 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-18 04:23:05.941 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-18 04:23:05.941 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-18 04:23:05.942 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-18 04:23:05.942 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-18 04:23:05.942 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-18 04:23:05.942 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-07-18 04:23:05.942 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTM3MTE4MTksInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.uFHvM51Hq3zbz_0QNIPAnz5t7fDwjs8LqXvOtvvcZrA
2025-07-18 04:23:05.942 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:23:05.942 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = eaccda84-475e-4698-84eb-605457df3f5c
2025-07-18 04:23:05.942 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8591
2025-07-18 04:23:05.942 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-18 04:23:05.942 [reactor-http-epoll-4] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-18 04:23:12.003 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:23:12.004 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-18 04:23:12.004 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:23:12.027 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"********","original_transaction_id":"********","transaction_type":"Purchase","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"4","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1752*********-*********","amount":{"total":123000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"531358****3430","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"26"}},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"831000"},"transaction_time":"2025-07-17T14:45:50Z","transaction_status":"0","transaction_ref_number":"5864104180072653227050","ip_address":"***************","ip_proxy":"N","bin_country":"AUSTRALIA","csc_result_code":"M","enrolled_3ds":"Y","avs":{"address":"","result_code":"Y","province":"","city":"","zip_code":"","country":""},"verification_security_level":"05","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"NATIONAL AUSTRALIA BANK LIMITED","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","acqResponseCode":"","status3ds":"Y","riskOverAllResult":"ACCEPT","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","contractType":"2B"}
2025-07-18 04:23:12.027 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-18 04:23:12.028 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-18 04:23:12.029 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-18 04:23:12.029 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-17T14:45:50, orderAmount=123000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Purchase, paymentMethod=QT, transactionCreatedTime=2025-07-17T14:45:50, transactionCompletedTime=2025-07-17T14:45:50, transactionAmount=123000.0, transactionCurrency=VND, transactionStatus=Successful, responseCode=0, merchantTransRef=TEST_1752*********-*********, paymentChannel=null, installmentStatus=), paymentMethod=PaymentMethod(cardNumber=531358****3430, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, acquirer=4, cardExpiry=1126, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=AUSTRALIA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null), actions=null)
2025-07-18 04:23:12.029 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-18 04:23:12.029 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-18 04:23:12.029 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-18 04:23:12.040 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-18 04:23:12.040 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [TESTENETVIET, TESTIDTEST1, TESTIDTEST2, TESTIDTEST3, TESTIDTEST4, TESTIDTEST6, TESTIDTEST5, OPTESTHB, GIANGTESTASSIGN, OP_TESTCONFIG, TEST3DS2_LT, TEST3DS2_MK, TEST3DS2_TN, OP_VPBMPGS3, TESTTGDD3D2, ONEPAYHB, TEST_BNPL, FDSFDSF, TESTJBAVND, TEST_BNPL2, TESTPCI, tcreate2, thanhdanh, danh1289, TESTTGDD, OP_SAMSUNG, BIDVEXPIRE, AUTOREFUNDUSD, danh5140, HVTT, SIVU1, SIVU, D_TEST, OP_TESTVND, OP_VND01, TESTONEPAY1, TESTHANH3, TESTBIDVV2, TESTVF, TESTIDR, TESTCNY, TESTSCB3D2, TESTTRAGOP2, TESTAPPLEND, TESTTWD, TESTSGD, TESTAPPLE, ONEPAYHM, OPMPAY, OP_TEST3DS2V, TESTTNSUSD, TESTTGPHYTO, TESTINVOICE, OP_MPAYVN1, TESTBIDV1, TESTONEPAY4, danh128950, OP_TESTAUTH, TESTONEPAY2, TESTINDOCHINAUSD, TESTMCD, HOABT, TESTNAMVH, TESTONEPAY20, OP_TESTOP20, OP_MPGSUSD, TESTSACOMCBV, TESTBIDV, TEST3DSMPGS, OP_TEST3DS, MIGS3DS, TESTBIDVCSU2, OP_CYBSVND, OP_CYBSUSD, OP_SACOMCBSV, OP_SACOMCBSU, 9.70436E+15, TESTSACOMCBU, OP_TESTUSD, ONEPAYMT, OPPREPAID, TESTAPPAY, ANHTVTEST20, TESTTEROP, 9.70436E+15, TESTATM, TESTDUONG, OP_SACOMTRVU, TESTGATEWAY, TESTAPP, TESTUSD, TESTPR, TESTVFVND, TCREATE1, TCREATE2, TCREATE3, TCREATE4, OTELBIDV, TESTMISA, TESTEXPIRED, TESTHANH5, TESTHANH6, TESTHANH7, thanhdanh2211, VTL, TESTKRW, TESTJPY, 1.23457E+19, TESTQNM, thanhdanh12, danh12890, danh510, danh3140, AHIHI, OP_SACOMTRVV, OP_MPGSVND, danh12891, TESTSAMSUNGPL, TESTSAMSUNG, DPVD, TESTOP3D2, danh31240, SIVU_DANH, THL, TESTONEPAY3, TESTTHB, TESTONEPAY7, TESTAXA1, TESTHC1, TESTMEGA1, TESTONEPAY9, TESTONEPAY10, TESTMYR, TESTOCB, TESTONEPAY, ACVNC, AOSVNBANK, AOSVNC, TESTAXA, TESTHC, TESTMEGA, TESTVTB3D2, TESTVJU, SHIHANLIFETG, TESTBNPL, TESTONEPAYUSD, FALSE, MPAYVND, OP_TESTSS, OP_SACOM2SCU, INVOICETG, OP_VPBMPGS1, OP_VPBMPGS2, TESTVCB3D2O, TESTTNS, TESTTOKEN, TESTTOKENUSD, OP_VCBVND, OP_VCBUSD, OP_MPAYVND3D, TESTVCB3D2, TESTBIDVCBSV, TESTBIDVCBSU, TESTBIDVU3, TESTBIDVV3, TESTTHSC, TESTLZD, OP_MPAYVND, TESTMAFC, TESTTRAGOP, OP_SACOM2SCV, TESTDMX, TESTSAPO2, TESTSAPO]
2025-07-18 04:23:12.040 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-18 04:23:12.041 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-07-18 04:23:12.968 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call getTransactionHistory-----------
2025-07-18 04:23:12.969 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: originalTransactionId=******** ,paymentMethod=QT ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:23:12.969 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionHistory MA-----------
2025-07-18 04:23:12.970 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/********/history ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:23:12.982 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY API: response code=200 ,response body={"transactions":[{"transaction_id":"********","original_transaction_id":"********","response_code":"0","reference_number":"5864104180072653227050","merchant_transaction_ref":"TEST_1752*********-*********","transaction_type":"Purchase","amount":{"total":123000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-07-17T14:45:50Z","advance_status":"Successful","financial_transaction_id":"5864104180072653227050","note":"","parent_id":"********","dadId":"","subHistories":""}]}
2025-07-18 04:23:12.982 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionHistory MA------------
2025-07-18 04:23:12.982 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY INTERNATIONAL: {"transactions":[{"transaction_id":"********","original_transaction_id":"********","response_code":"0","reference_number":"5864104180072653227050","merchant_transaction_ref":"TEST_1752*********-*********","transaction_type":"Purchase","amount":{"total":123000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-07-17T14:45:50Z","advance_status":"Successful","financial_transaction_id":"5864104180072653227050","note":"","parent_id":"********","dadId":"","subHistories":""}]}
2025-07-18 04:23:12.983 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call getTransactionHistory------------
2025-07-18 04:29:03.821 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 4003851 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-07-18 04:29:03.824 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-07-18 04:29:04.672 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-07-18 04:29:04.884 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 204 ms. Found 2 R2DBC repository interfaces.
2025-07-18 04:29:06.431 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-07-18 04:29:06.722 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@156cfa20
2025-07-18 04:29:06.724 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-07-18 04:29:06.787 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-07-18 04:29:06.913 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@34cf0e80
2025-07-18 04:29:06.915 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-07-18 04:29:07.061 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-07-18 04:29:07.069 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-07-18 04:29:07.073 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-07-18 04:29:07.076 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-07-18 04:29:07.079 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-07-18 04:29:07.080 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-07-18 04:29:08.036 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8591 (http)
2025-07-18 04:29:08.237 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 5.154 seconds (process running for 6.604)
2025-07-18 04:29:25.470 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://localhost:8591/ma-service/api/v1/transactions/transaction/detail?transactionId=********&paymentMethod=QT&transactionType=Purchase
2025-07-18 04:29:25.471 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-07-18 04:29:25.471 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-18 04:29:25.471 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-18 04:29:25.471 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-18 04:29:25.471 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-18 04:29:25.471 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-18 04:29:25.471 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-07-18 04:29:25.472 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-18 04:29:25.472 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-18 04:29:25.472 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-18 04:29:25.472 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-18 04:29:25.472 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-18 04:29:25.472 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-18 04:29:25.472 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-18 04:29:25.472 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-07-18 04:29:25.472 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTM3MTE4MTksInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.uFHvM51Hq3zbz_0QNIPAnz5t7fDwjs8LqXvOtvvcZrA
2025-07-18 04:29:25.472 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:29:25.472 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = a96a3964-b123-4360-ab97-dc52219200d3
2025-07-18 04:29:25.472 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8591
2025-07-18 04:29:25.473 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-18 04:29:25.476 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-18 04:29:28.203 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:29:28.204 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-18 04:29:28.264 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:29:28.379 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"********","original_transaction_id":"********","transaction_type":"Purchase","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"4","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1752*********-*********","amount":{"total":123000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"531358****3430","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"26"}},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"831000"},"transaction_time":"2025-07-17T14:45:50Z","transaction_status":"0","transaction_ref_number":"5864104180072653227050","ip_address":"***************","ip_proxy":"N","bin_country":"AUSTRALIA","csc_result_code":"M","enrolled_3ds":"Y","avs":{"address":"","result_code":"Y","province":"","city":"","zip_code":"","country":""},"verification_security_level":"05","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"NATIONAL AUSTRALIA BANK LIMITED","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","acqResponseCode":"","status3ds":"Y","riskOverAllResult":"ACCEPT","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","contractType":"2B"}
2025-07-18 04:29:28.379 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-18 04:29:28.399 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-18 04:29:28.410 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-18 04:29:28.436 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-17T14:45:50, orderAmount=123000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Purchase, paymentMethod=QT, transactionCreatedTime=2025-07-17T14:45:50, transactionCompletedTime=2025-07-17T14:45:50, transactionAmount=123000.0, transactionCurrency=VND, transactionStatus=Successful, responseCode=0, merchantTransRef=TEST_1752*********-*********, paymentChannel=null, installmentStatus=), paymentMethod=PaymentMethod(cardNumber=531358****3430, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, acquirer=4, cardExpiry=1126, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=AUSTRALIA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null), actions=null)
2025-07-18 04:29:28.437 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-18 04:29:28.437 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-18 04:29:28.437 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-18 04:29:28.655 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-18 04:29:28.655 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [TESTENETVIET, TESTIDTEST1, TESTIDTEST2, TESTIDTEST3, TESTIDTEST4, TESTIDTEST6, TESTIDTEST5, OPTESTHB, GIANGTESTASSIGN, OP_TESTCONFIG, TEST3DS2_LT, TEST3DS2_MK, TEST3DS2_TN, OP_VPBMPGS3, TESTTGDD3D2, ONEPAYHB, TEST_BNPL, FDSFDSF, TESTJBAVND, TEST_BNPL2, TESTPCI, tcreate2, thanhdanh, danh1289, TESTTGDD, OP_SAMSUNG, BIDVEXPIRE, AUTOREFUNDUSD, danh5140, HVTT, SIVU1, SIVU, D_TEST, OP_TESTVND, OP_VND01, TESTONEPAY1, TESTHANH3, TESTBIDVV2, TESTVF, TESTIDR, TESTCNY, TESTSCB3D2, TESTTRAGOP2, TESTAPPLEND, TESTTWD, TESTSGD, TESTAPPLE, ONEPAYHM, OPMPAY, OP_TEST3DS2V, TESTTNSUSD, TESTTGPHYTO, TESTINVOICE, OP_MPAYVN1, TESTBIDV1, TESTONEPAY4, danh128950, OP_TESTAUTH, TESTONEPAY2, TESTINDOCHINAUSD, TESTMCD, HOABT, TESTNAMVH, TESTONEPAY20, OP_TESTOP20, OP_MPGSUSD, TESTSACOMCBV, TESTBIDV, TEST3DSMPGS, OP_TEST3DS, MIGS3DS, TESTBIDVCSU2, OP_CYBSVND, OP_CYBSUSD, OP_SACOMCBSV, OP_SACOMCBSU, 9.70436E+15, TESTSACOMCBU, OP_TESTUSD, ONEPAYMT, OPPREPAID, TESTAPPAY, ANHTVTEST20, TESTTEROP, 9.70436E+15, TESTATM, TESTDUONG, OP_SACOMTRVU, TESTGATEWAY, TESTAPP, TESTUSD, TESTPR, TESTVFVND, TCREATE1, TCREATE2, TCREATE3, TCREATE4, OTELBIDV, TESTMISA, TESTEXPIRED, TESTHANH5, TESTHANH6, TESTHANH7, thanhdanh2211, VTL, TESTKRW, TESTJPY, 1.23457E+19, TESTQNM, thanhdanh12, danh12890, danh510, danh3140, AHIHI, OP_SACOMTRVV, OP_MPGSVND, danh12891, TESTSAMSUNGPL, TESTSAMSUNG, DPVD, TESTOP3D2, danh31240, SIVU_DANH, THL, TESTONEPAY3, TESTTHB, TESTONEPAY7, TESTAXA1, TESTHC1, TESTMEGA1, TESTONEPAY9, TESTONEPAY10, TESTMYR, TESTOCB, TESTONEPAY, ACVNC, AOSVNBANK, AOSVNC, TESTAXA, TESTHC, TESTMEGA, TESTVTB3D2, TESTVJU, SHIHANLIFETG, TESTBNPL, TESTONEPAYUSD, FALSE, MPAYVND, OP_TESTSS, OP_SACOM2SCU, INVOICETG, OP_VPBMPGS1, OP_VPBMPGS2, TESTVCB3D2O, TESTTNS, TESTTOKEN, TESTTOKENUSD, OP_VCBVND, OP_VCBUSD, OP_MPAYVND3D, TESTVCB3D2, TESTBIDVCBSV, TESTBIDVCBSU, TESTBIDVU3, TESTBIDVV3, TESTTHSC, TESTLZD, OP_MPAYVND, TESTMAFC, TESTTRAGOP, OP_SACOM2SCV, TESTDMX, TESTSAPO2, TESTSAPO]
2025-07-18 04:29:28.658 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-18 04:29:28.659 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-07-18 04:29:29.623 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call getTransactionHistory-----------
2025-07-18 04:29:29.634 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: originalTransactionId=******** ,paymentMethod=QT ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:29:29.644 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionHistory MA-----------
2025-07-18 04:29:29.666 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/********/history ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:29:29.705 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY API: response code=200 ,response body={"transactions":[{"transaction_id":"********","original_transaction_id":"********","response_code":"0","reference_number":"5864104180072653227050","merchant_transaction_ref":"TEST_1752*********-*********","transaction_type":"Purchase","amount":{"total":123000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-07-17T14:45:50Z","advance_status":"Successful","financial_transaction_id":"5864104180072653227050","note":"","parent_id":"********","dadId":"","subHistories":""}]}
2025-07-18 04:29:29.710 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionHistory MA------------
2025-07-18 04:29:29.717 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY INTERNATIONAL: {"transactions":[{"transaction_id":"********","original_transaction_id":"********","response_code":"0","reference_number":"5864104180072653227050","merchant_transaction_ref":"TEST_1752*********-*********","transaction_type":"Purchase","amount":{"total":123000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-07-17T14:45:50Z","advance_status":"Successful","financial_transaction_id":"5864104180072653227050","note":"","parent_id":"********","dadId":"","subHistories":""}]}
2025-07-18 04:29:29.764 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call getTransactionHistory------------
2025-07-18 04:29:30.138 [reactor-http-epoll-3] INFO  v.o.t.repository.AcquirerRepository - Starting getListAcquirer - calling function ONEDATA.GET_LIST_ACQUIRER
2025-07-18 04:29:56.764 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - Error when checkPermissionByUserId: transactionId=********, error={}
java.lang.IllegalStateException: block()/blockFirst()/blockLast() are blocking, which is not supported in thread reactor-http-epoll-3
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:87)
	at reactor.core.publisher.Mono.block(Mono.java:1779)
	at vn.onepay.transaction.util.CheckPermissionUtil.checkPermission(CheckPermissionUtil.java:56)
	at vn.onepay.transaction.util.CheckPermissionUtil.checkPerActionVoid(CheckPermissionUtil.java:107)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.checkPerAndActionByUserId(TransactionOldServiceImpl.java:2559)
	at vn.onepay.transaction.controller.TransactionController.lambda$2(TransactionController.java:159)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-18 04:29:57.540 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Response: GET http://localhost:8591/ma-service/api/v1/transactions/transaction/detail?transactionId=********&paymentMethod=QT&transactionType=Purchase - Status: 200 OK
2025-07-18 04:30:01.380 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://localhost:8591/ma-service/api/v1/transactions/transaction/detail?transactionId=********&paymentMethod=QT&transactionType=Purchase
2025-07-18 04:30:01.380 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-07-18 04:30:01.380 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-18 04:30:01.380 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-18 04:30:01.380 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-18 04:30:01.380 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-18 04:30:01.380 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-18 04:30:01.380 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-07-18 04:30:01.380 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-18 04:30:01.381 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-18 04:30:01.381 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-18 04:30:01.381 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-18 04:30:01.381 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-18 04:30:01.381 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-18 04:30:01.381 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-18 04:30:01.381 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-07-18 04:30:01.381 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTM3MTE4MTksInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.uFHvM51Hq3zbz_0QNIPAnz5t7fDwjs8LqXvOtvvcZrA
2025-07-18 04:30:01.381 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:30:01.381 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 69adf9d3-1373-4854-813c-2b064e622303
2025-07-18 04:30:01.381 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8591
2025-07-18 04:30:01.381 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-18 04:30:01.381 [reactor-http-epoll-4] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-18 04:30:03.053 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:30:03.054 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-18 04:30:03.055 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:30:03.084 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"********","original_transaction_id":"********","transaction_type":"Purchase","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"4","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1752*********-*********","amount":{"total":123000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"531358****3430","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"26"}},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"831000"},"transaction_time":"2025-07-17T14:45:50Z","transaction_status":"0","transaction_ref_number":"5864104180072653227050","ip_address":"***************","ip_proxy":"N","bin_country":"AUSTRALIA","csc_result_code":"M","enrolled_3ds":"Y","avs":{"address":"","result_code":"Y","province":"","city":"","zip_code":"","country":""},"verification_security_level":"05","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"NATIONAL AUSTRALIA BANK LIMITED","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","acqResponseCode":"","status3ds":"Y","riskOverAllResult":"ACCEPT","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","contractType":"2B"}
2025-07-18 04:30:03.084 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-18 04:30:03.085 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-18 04:30:03.086 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-18 04:30:03.086 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-17T14:45:50, orderAmount=123000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Purchase, paymentMethod=QT, transactionCreatedTime=2025-07-17T14:45:50, transactionCompletedTime=2025-07-17T14:45:50, transactionAmount=123000.0, transactionCurrency=VND, transactionStatus=Successful, responseCode=0, merchantTransRef=TEST_1752*********-*********, paymentChannel=null, installmentStatus=), paymentMethod=PaymentMethod(cardNumber=531358****3430, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, acquirer=4, cardExpiry=1126, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=AUSTRALIA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null), actions=null)
2025-07-18 04:30:03.086 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-18 04:30:03.087 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-18 04:30:03.087 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-18 04:30:03.098 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-18 04:30:03.098 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [TESTENETVIET, TESTIDTEST1, TESTIDTEST2, TESTIDTEST3, TESTIDTEST4, TESTIDTEST6, TESTIDTEST5, OPTESTHB, GIANGTESTASSIGN, OP_TESTCONFIG, TEST3DS2_LT, TEST3DS2_MK, TEST3DS2_TN, OP_VPBMPGS3, TESTTGDD3D2, ONEPAYHB, TEST_BNPL, FDSFDSF, TESTJBAVND, TEST_BNPL2, TESTPCI, tcreate2, thanhdanh, danh1289, TESTTGDD, OP_SAMSUNG, BIDVEXPIRE, AUTOREFUNDUSD, danh5140, HVTT, SIVU1, SIVU, D_TEST, OP_TESTVND, OP_VND01, TESTONEPAY1, TESTHANH3, TESTBIDVV2, TESTVF, TESTIDR, TESTCNY, TESTSCB3D2, TESTTRAGOP2, TESTAPPLEND, TESTTWD, TESTSGD, TESTAPPLE, ONEPAYHM, OPMPAY, OP_TEST3DS2V, TESTTNSUSD, TESTTGPHYTO, TESTINVOICE, OP_MPAYVN1, TESTBIDV1, TESTONEPAY4, danh128950, OP_TESTAUTH, TESTONEPAY2, TESTINDOCHINAUSD, TESTMCD, HOABT, TESTNAMVH, TESTONEPAY20, OP_TESTOP20, OP_MPGSUSD, TESTSACOMCBV, TESTBIDV, TEST3DSMPGS, OP_TEST3DS, MIGS3DS, TESTBIDVCSU2, OP_CYBSVND, OP_CYBSUSD, OP_SACOMCBSV, OP_SACOMCBSU, 9.70436E+15, TESTSACOMCBU, OP_TESTUSD, ONEPAYMT, OPPREPAID, TESTAPPAY, ANHTVTEST20, TESTTEROP, 9.70436E+15, TESTATM, TESTDUONG, OP_SACOMTRVU, TESTGATEWAY, TESTAPP, TESTUSD, TESTPR, TESTVFVND, TCREATE1, TCREATE2, TCREATE3, TCREATE4, OTELBIDV, TESTMISA, TESTEXPIRED, TESTHANH5, TESTHANH6, TESTHANH7, thanhdanh2211, VTL, TESTKRW, TESTJPY, 1.23457E+19, TESTQNM, thanhdanh12, danh12890, danh510, danh3140, AHIHI, OP_SACOMTRVV, OP_MPGSVND, danh12891, TESTSAMSUNGPL, TESTSAMSUNG, DPVD, TESTOP3D2, danh31240, SIVU_DANH, THL, TESTONEPAY3, TESTTHB, TESTONEPAY7, TESTAXA1, TESTHC1, TESTMEGA1, TESTONEPAY9, TESTONEPAY10, TESTMYR, TESTOCB, TESTONEPAY, ACVNC, AOSVNBANK, AOSVNC, TESTAXA, TESTHC, TESTMEGA, TESTVTB3D2, TESTVJU, SHIHANLIFETG, TESTBNPL, TESTONEPAYUSD, FALSE, MPAYVND, OP_TESTSS, OP_SACOM2SCU, INVOICETG, OP_VPBMPGS1, OP_VPBMPGS2, TESTVCB3D2O, TESTTNS, TESTTOKEN, TESTTOKENUSD, OP_VCBVND, OP_VCBUSD, OP_MPAYVND3D, TESTVCB3D2, TESTBIDVCBSV, TESTBIDVCBSU, TESTBIDVU3, TESTBIDVV3, TESTTHSC, TESTLZD, OP_MPAYVND, TESTMAFC, TESTTRAGOP, OP_SACOM2SCV, TESTDMX, TESTSAPO2, TESTSAPO]
2025-07-18 04:30:03.098 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-18 04:30:03.098 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-07-18 04:30:04.231 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call getTransactionHistory-----------
2025-07-18 04:30:04.231 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: originalTransactionId=******** ,paymentMethod=QT ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:30:04.232 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionHistory MA-----------
2025-07-18 04:30:04.232 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/********/history ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:30:04.246 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY API: response code=200 ,response body={"transactions":[{"transaction_id":"********","original_transaction_id":"********","response_code":"0","reference_number":"5864104180072653227050","merchant_transaction_ref":"TEST_1752*********-*********","transaction_type":"Purchase","amount":{"total":123000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-07-17T14:45:50Z","advance_status":"Successful","financial_transaction_id":"5864104180072653227050","note":"","parent_id":"********","dadId":"","subHistories":""}]}
2025-07-18 04:30:04.246 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionHistory MA------------
2025-07-18 04:30:04.246 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY INTERNATIONAL: {"transactions":[{"transaction_id":"********","original_transaction_id":"********","response_code":"0","reference_number":"5864104180072653227050","merchant_transaction_ref":"TEST_1752*********-*********","transaction_type":"Purchase","amount":{"total":123000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-07-17T14:45:50Z","advance_status":"Successful","financial_transaction_id":"5864104180072653227050","note":"","parent_id":"********","dadId":"","subHistories":""}]}
2025-07-18 04:30:04.247 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call getTransactionHistory------------
2025-07-18 04:31:05.624 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - Error when checkPermissionByUserId: transactionId=********, error={}
java.lang.IllegalStateException: block()/blockFirst()/blockLast() are blocking, which is not supported in thread reactor-http-epoll-3
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:87)
	at reactor.core.publisher.Mono.block(Mono.java:1779)
	at vn.onepay.transaction.util.CheckPermissionUtil.checkPermission(CheckPermissionUtil.java:56)
	at vn.onepay.transaction.util.CheckPermissionUtil.checkPerActionVoid(CheckPermissionUtil.java:107)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.checkPerAndActionByUserId(TransactionOldServiceImpl.java:2559)
	at vn.onepay.transaction.controller.TransactionController.lambda$2(TransactionController.java:159)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-18 04:31:08.525 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Response: GET http://localhost:8591/ma-service/api/v1/transactions/transaction/detail?transactionId=********&paymentMethod=QT&transactionType=Purchase - Status: 200 OK
2025-07-18 04:54:33.955 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://localhost:8591/ma-service/api/v1/transactions/transaction/detail?transactionId=********&paymentMethod=QT&transactionType=Purchase
2025-07-18 04:54:33.956 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-07-18 04:54:33.956 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-18 04:54:33.956 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-18 04:54:33.956 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-18 04:54:33.956 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-18 04:54:33.956 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-18 04:54:33.956 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-07-18 04:54:33.956 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-18 04:54:33.956 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-18 04:54:33.956 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-18 04:54:33.956 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-18 04:54:33.956 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-18 04:54:33.956 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-18 04:54:33.956 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-18 04:54:33.957 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-07-18 04:54:33.957 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTM3MTE4MTksInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.uFHvM51Hq3zbz_0QNIPAnz5t7fDwjs8LqXvOtvvcZrA
2025-07-18 04:54:33.957 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:54:33.957 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = a4e53250-62d9-49cb-ab20-5dd325f69da7
2025-07-18 04:54:33.957 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8591
2025-07-18 04:54:33.957 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-18 04:54:33.957 [reactor-http-epoll-5] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-18 04:54:35.880 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:54:35.880 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-18 04:54:35.881 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:54:35.972 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"********","original_transaction_id":"********","transaction_type":"Purchase","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"4","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1752*********-*********","amount":{"total":123000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"531358****3430","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"26"}},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"831000"},"transaction_time":"2025-07-17T14:45:50Z","transaction_status":"0","transaction_ref_number":"5864104180072653227050","ip_address":"***************","ip_proxy":"N","bin_country":"AUSTRALIA","csc_result_code":"M","enrolled_3ds":"Y","avs":{"address":"","result_code":"Y","province":"","city":"","zip_code":"","country":""},"verification_security_level":"05","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"NATIONAL AUSTRALIA BANK LIMITED","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","acqResponseCode":"","status3ds":"Y","riskOverAllResult":"ACCEPT","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","contractType":"2B"}
2025-07-18 04:54:35.972 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-18 04:54:35.973 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-18 04:54:35.974 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-18 04:54:35.974 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-17T14:45:50, orderAmount=123000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Purchase, paymentMethod=QT, transactionCreatedTime=2025-07-17T14:45:50, transactionCompletedTime=2025-07-17T14:45:50, transactionAmount=123000.0, transactionCurrency=VND, transactionStatus=Successful, responseCode=0, merchantTransRef=TEST_1752*********-*********, paymentChannel=null, installmentStatus=), paymentMethod=PaymentMethod(cardNumber=531358****3430, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, acquirer=4, cardExpiry=1126, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=AUSTRALIA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null), actions=null)
2025-07-18 04:54:35.974 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-18 04:54:35.974 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-18 04:54:35.974 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-18 04:54:35.989 [reactor-http-epoll-5] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-18 04:54:35.990 [reactor-http-epoll-5] INFO  v.o.t.service.MerchantService - list merchant from permission: [TESTENETVIET, TESTIDTEST1, TESTIDTEST2, TESTIDTEST3, TESTIDTEST4, TESTIDTEST6, TESTIDTEST5, OPTESTHB, GIANGTESTASSIGN, OP_TESTCONFIG, TEST3DS2_LT, TEST3DS2_MK, TEST3DS2_TN, OP_VPBMPGS3, TESTTGDD3D2, ONEPAYHB, TEST_BNPL, FDSFDSF, TESTJBAVND, TEST_BNPL2, TESTPCI, tcreate2, thanhdanh, danh1289, TESTTGDD, OP_SAMSUNG, BIDVEXPIRE, AUTOREFUNDUSD, danh5140, HVTT, SIVU1, SIVU, D_TEST, OP_TESTVND, OP_VND01, TESTONEPAY1, TESTHANH3, TESTBIDVV2, TESTVF, TESTIDR, TESTCNY, TESTSCB3D2, TESTTRAGOP2, TESTAPPLEND, TESTTWD, TESTSGD, TESTAPPLE, ONEPAYHM, OPMPAY, OP_TEST3DS2V, TESTTNSUSD, TESTTGPHYTO, TESTINVOICE, OP_MPAYVN1, TESTBIDV1, TESTONEPAY4, danh128950, OP_TESTAUTH, TESTONEPAY2, TESTINDOCHINAUSD, TESTMCD, HOABT, TESTNAMVH, TESTONEPAY20, OP_TESTOP20, OP_MPGSUSD, TESTSACOMCBV, TESTBIDV, TEST3DSMPGS, OP_TEST3DS, MIGS3DS, TESTBIDVCSU2, OP_CYBSVND, OP_CYBSUSD, OP_SACOMCBSV, OP_SACOMCBSU, 9.70436E+15, TESTSACOMCBU, OP_TESTUSD, ONEPAYMT, OPPREPAID, TESTAPPAY, ANHTVTEST20, TESTTEROP, 9.70436E+15, TESTATM, TESTDUONG, OP_SACOMTRVU, TESTGATEWAY, TESTAPP, TESTUSD, TESTPR, TESTVFVND, TCREATE1, TCREATE2, TCREATE3, TCREATE4, OTELBIDV, TESTMISA, TESTEXPIRED, TESTHANH5, TESTHANH6, TESTHANH7, thanhdanh2211, VTL, TESTKRW, TESTJPY, 1.23457E+19, TESTQNM, thanhdanh12, danh12890, danh510, danh3140, AHIHI, OP_SACOMTRVV, OP_MPGSVND, danh12891, TESTSAMSUNGPL, TESTSAMSUNG, DPVD, TESTOP3D2, danh31240, SIVU_DANH, THL, TESTONEPAY3, TESTTHB, TESTONEPAY7, TESTAXA1, TESTHC1, TESTMEGA1, TESTONEPAY9, TESTONEPAY10, TESTMYR, TESTOCB, TESTONEPAY, ACVNC, AOSVNBANK, AOSVNC, TESTAXA, TESTHC, TESTMEGA, TESTVTB3D2, TESTVJU, SHIHANLIFETG, TESTBNPL, TESTONEPAYUSD, FALSE, MPAYVND, OP_TESTSS, OP_SACOM2SCU, INVOICETG, OP_VPBMPGS1, OP_VPBMPGS2, TESTVCB3D2O, TESTTNS, TESTTOKEN, TESTTOKENUSD, OP_VCBVND, OP_VCBUSD, OP_MPAYVND3D, TESTVCB3D2, TESTBIDVCBSV, TESTBIDVCBSU, TESTBIDVU3, TESTBIDVV3, TESTTHSC, TESTLZD, OP_MPAYVND, TESTMAFC, TESTTRAGOP, OP_SACOM2SCV, TESTDMX, TESTSAPO2, TESTSAPO]
2025-07-18 04:54:35.990 [reactor-http-epoll-5] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-18 04:54:35.990 [reactor-http-epoll-5] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-07-18 04:54:39.264 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call getTransactionHistory-----------
2025-07-18 04:54:39.264 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: originalTransactionId=******** ,paymentMethod=QT ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:54:39.264 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionHistory MA-----------
2025-07-18 04:54:39.265 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/********/history ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-18 04:54:39.279 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY API: response code=200 ,response body={"transactions":[{"transaction_id":"********","original_transaction_id":"********","response_code":"0","reference_number":"5864104180072653227050","merchant_transaction_ref":"TEST_1752*********-*********","transaction_type":"Purchase","amount":{"total":123000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-07-17T14:45:50Z","advance_status":"Successful","financial_transaction_id":"5864104180072653227050","note":"","parent_id":"********","dadId":"","subHistories":""}]}
2025-07-18 04:54:39.279 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionHistory MA------------
2025-07-18 04:54:39.279 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY INTERNATIONAL: {"transactions":[{"transaction_id":"********","original_transaction_id":"********","response_code":"0","reference_number":"5864104180072653227050","merchant_transaction_ref":"TEST_1752*********-*********","transaction_type":"Purchase","amount":{"total":123000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-07-17T14:45:50Z","advance_status":"Successful","financial_transaction_id":"5864104180072653227050","note":"","parent_id":"********","dadId":"","subHistories":""}]}
2025-07-18 04:54:39.280 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call getTransactionHistory------------
