2025-07-09 02:10:13.554 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 4026473 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-07-09 02:10:13.559 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-07-09 02:10:14.302 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-07-09 02:10:14.502 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 193 ms. Found 2 R2DBC repository interfaces.
2025-07-09 02:10:16.031 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-07-09 02:10:16.329 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@7e31062c
2025-07-09 02:10:16.331 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-07-09 02:10:16.388 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-07-09 02:10:16.503 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@134c38
2025-07-09 02:10:16.503 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-07-09 02:10:16.558 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-07-09 02:10:16.566 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-07-09 02:10:16.568 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-07-09 02:10:16.569 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-07-09 02:10:16.571 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-07-09 02:10:16.572 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-07-09 02:10:17.412 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8592 (http)
2025-07-09 02:10:17.604 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 4.728 seconds (process running for 5.829)
2025-07-09 02:10:41.692 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-09 02:10:41.692 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-09 02:10:41.692 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-09 02:10:41.693 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-09 02:10:41.693 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-09 02:10:41.693 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-09 02:10:41.693 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-09 02:10:41.693 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-09 02:10:41.693 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-09 02:10:41.693 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-09 02:10:41.693 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-09 02:10:41.693 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-09 02:10:41.693 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-09 02:10:41.693 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-09 02:10:41.693 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-09 02:10:41.693 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-09 02:10:41.693 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-09 02:10:41.694 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-09 02:10:41.694 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-09 02:10:41.694 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-09 02:10:41.694 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = a71b598d-fa95-472f-b8cd-05bc23f226f3
2025-07-09 02:10:41.694 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8592
2025-07-09 02:10:41.694 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-09 02:10:41.694 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 345
2025-07-09 02:10:41.698 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-09 02:10:46.212 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-09 02:10:46.212 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-09 02:10:46.298 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=null
2025-07-09 02:10:46.582 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","transaction_id":"********","original_transaction_id":"********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"5","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1751969902989146435","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"512345****0008","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"28"},"commercical_card":"888","commercial_card_indicator":"3"},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"447432"},"transaction_time":"2025-07-08T17:18:37Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"***************","ip_proxy":"N","bin_country":"ANGUILLA","csc_result_code":"","enrolled_3ds":"Y","avs":{"address":"","province":"","city":"","zip_code":"","country":""},"verification_security_level":"","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"VIETINBANK","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"OTHER","status3ds":"Y","riskOverAllResult":"","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00"}
2025-07-09 02:10:46.582 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-09 02:10:46.584 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-09 02:10:46.593 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-09 02:10:46.612 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-08T17:18:37, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-07-08T17:18:37, transactionCompletedTime=2025-07-08T17:18:37, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=0, responseCode=0, merchantTransRef=TEST_1751969902989146435), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, cardExpiry=1128, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=ANGUILLA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null))
2025-07-09 02:10:46.612 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-09 02:10:57.140 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-09 02:10:57.141 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-09 02:10:57.333 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-09 02:10:57.333 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [CGV15, CGV17, CGV2, CGV4, CGV18, CGV8, CGV6, CJSHOW, CGVKIOSK, CGV, CGV13, CGV5, TESTPCI]
2025-07-09 02:10:57.334 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-09 02:10:57.334 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-07-09 02:10:57.334 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api captureTransaction MA-----------
2025-07-09 02:10:57.335 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8183/ma-service/transaction/international/******** ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={
    "id": "********",
    "op": "replace",
    "path": "/capture",
    "value": {
        "merchant_id": "TESTPCI",
        "amount": 1000,
        "currency": "VND",
        "note": "",
        "user_name": "Quản trị viên hệ thống hệ thống hệ thống hệ thống"
    },
    "skipCallSynchronize": false,
    "service": "QT"
}
2025-07-09 02:11:04.928 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api captureTransaction MA------------
2025-07-09 02:11:34.938 [ForkJoinPool.commonPool-worker-1] ERROR v.o.t.e.GlobalExceptionHandler - Unhandled exception: 
java.net.http.HttpTimeoutException: request timed out
	at java.net.http/jdk.internal.net.http.ResponseTimerEvent.handle(ResponseTimerEvent.java:63)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#captureTransaction(ServerWebExchange, String, String) [DispatcherHandler]
Original Stack Trace:
		at java.net.http/jdk.internal.net.http.ResponseTimerEvent.handle(ResponseTimerEvent.java:63)
		at java.net.http/jdk.internal.net.http.HttpClientImpl.purgeTimeoutsAndReturnNextDeadline(HttpClientImpl.java:1780)
		at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.run(HttpClientImpl.java:1378)
2025-07-09 02:11:34.948 [ForkJoinPool.commonPool-worker-1] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
org.springframework.web.server.ResponseStatusException: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Exception handler vn.onepay.transaction.exception.GlobalExceptionHandler#handleException(Exception), error="request timed out" [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x000077e3d05bdea8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
		at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:115)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:67)
		at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
		at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
		at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
		at java.base/java.util.concurrent.CompletableFuture.postFire(CompletableFuture.java:614)
		at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:844)
		at java.base/java.util.concurrent.CompletableFuture$Completion.exec(CompletableFuture.java:483)
		at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
		at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
		at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
		at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
		at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
2025-07-09 02:11:34.962 [ForkJoinPool.commonPool-worker-1] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [8c8b995c-1]  500 Server Error for HTTP PATCH "/transaction/capture/********"
org.springframework.web.server.ResponseStatusException: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Exception handler vn.onepay.transaction.exception.GlobalExceptionHandler#handleException(Exception), error="request timed out" [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x000077e3d05bdea8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP PATCH "/ma-service/api/v1/transactions/transaction/capture/********" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
		at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:115)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:67)
		at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
		at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
		at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
		at java.base/java.util.concurrent.CompletableFuture.postFire(CompletableFuture.java:614)
		at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:844)
		at java.base/java.util.concurrent.CompletableFuture$Completion.exec(CompletableFuture.java:483)
		at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
		at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
		at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
		at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
		at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
2025-07-09 02:12:07.498 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-09 02:12:07.499 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-09 02:12:07.499 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-09 02:12:07.499 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-09 02:12:07.499 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-09 02:12:07.499 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-09 02:12:07.499 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-09 02:12:07.499 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-09 02:12:07.499 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-09 02:12:07.499 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-09 02:12:07.499 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-09 02:12:07.499 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-09 02:12:07.499 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-09 02:12:07.499 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-09 02:12:07.499 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-09 02:12:07.500 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-09 02:12:07.500 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-09 02:12:07.500 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-09 02:12:07.500 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-09 02:12:07.500 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-09 02:12:07.500 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = a6eadd46-4bc2-4244-b7a4-8bac91edc4aa
2025-07-09 02:12:07.500 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8592
2025-07-09 02:12:07.500 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-09 02:12:07.500 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 345
2025-07-09 02:12:07.500 [reactor-http-epoll-4] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-09 02:12:10.555 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-09 02:12:10.555 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-09 02:12:10.557 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=null
2025-07-09 02:12:10.569 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","transaction_id":"********","original_transaction_id":"********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"5","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1751969902989146435","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"512345****0008","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"28"},"commercical_card":"888","commercial_card_indicator":"3"},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"447432"},"transaction_time":"2025-07-08T17:18:37Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"***************","ip_proxy":"N","bin_country":"ANGUILLA","csc_result_code":"","enrolled_3ds":"Y","avs":{"address":"","province":"","city":"","zip_code":"","country":""},"verification_security_level":"","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"VIETINBANK","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"OTHER","status3ds":"Y","riskOverAllResult":"","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00"}
2025-07-09 02:12:10.569 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-09 02:12:10.570 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-09 02:12:10.571 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-09 02:12:10.571 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-08T17:18:37, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-07-08T17:18:37, transactionCompletedTime=2025-07-08T17:18:37, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=0, responseCode=0, merchantTransRef=TEST_1751969902989146435), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, cardExpiry=1128, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=ANGUILLA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null))
2025-07-09 02:12:10.571 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-09 02:12:11.906 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-09 02:12:11.906 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-09 02:12:11.922 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-09 02:12:11.923 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [CGV15, CGV17, CGV2, CGV4, CGV18, CGV8, CGV6, CJSHOW, CGVKIOSK, CGV, CGV13, CGV5, TESTPCI]
2025-07-09 02:12:11.923 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-09 02:12:11.923 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-07-09 02:12:11.923 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api captureTransaction MA-----------
2025-07-09 02:12:11.924 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8183/ma-service/transaction/international/******** ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={
    "id": "********",
    "op": "replace",
    "path": "/capture",
    "value": {
        "merchant_id": "TESTPCI",
        "amount": 1000,
        "currency": "VND",
        "note": "",
        "user_name": "Quản trị viên hệ thống hệ thống hệ thống hệ thống"
    },
    "skipCallSynchronize": false,
    "service": "QT"
}
2025-07-09 02:12:11.925 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api captureTransaction MA------------
2025-07-09 02:12:41.932 [ForkJoinPool.commonPool-worker-2] ERROR v.o.t.e.GlobalExceptionHandler - Unhandled exception: 
java.net.http.HttpTimeoutException: request timed out
	at java.net.http/jdk.internal.net.http.ResponseTimerEvent.handle(ResponseTimerEvent.java:63)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#captureTransaction(ServerWebExchange, String, String) [DispatcherHandler]
Original Stack Trace:
		at java.net.http/jdk.internal.net.http.ResponseTimerEvent.handle(ResponseTimerEvent.java:63)
		at java.net.http/jdk.internal.net.http.HttpClientImpl.purgeTimeoutsAndReturnNextDeadline(HttpClientImpl.java:1780)
		at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.run(HttpClientImpl.java:1378)
2025-07-09 02:12:41.934 [ForkJoinPool.commonPool-worker-2] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
org.springframework.web.server.ResponseStatusException: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Exception handler vn.onepay.transaction.exception.GlobalExceptionHandler#handleException(Exception), error="request timed out" [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x000077e3d05bdea8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
		at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:115)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:67)
		at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
		at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
		at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
		at java.base/java.util.concurrent.CompletableFuture.postFire(CompletableFuture.java:614)
		at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:844)
		at java.base/java.util.concurrent.CompletableFuture$Completion.exec(CompletableFuture.java:483)
		at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
		at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
		at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
		at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
		at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
2025-07-09 02:12:41.936 [ForkJoinPool.commonPool-worker-2] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [0b9c73f7-2]  500 Server Error for HTTP PATCH "/transaction/capture/********"
org.springframework.web.server.ResponseStatusException: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Exception handler vn.onepay.transaction.exception.GlobalExceptionHandler#handleException(Exception), error="request timed out" [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x000077e3d05bdea8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP PATCH "/ma-service/api/v1/transactions/transaction/capture/********" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
		at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:115)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:67)
		at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
		at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
		at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
		at java.base/java.util.concurrent.CompletableFuture.postFire(CompletableFuture.java:614)
		at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:844)
		at java.base/java.util.concurrent.CompletableFuture$Completion.exec(CompletableFuture.java:483)
		at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
		at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
		at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
		at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
		at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
2025-07-09 02:15:45.321 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-09 02:15:45.321 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-09 02:15:45.321 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-09 02:15:45.321 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-09 02:15:45.322 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-09 02:15:45.322 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-09 02:15:45.322 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-09 02:15:45.322 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-09 02:15:45.322 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-09 02:15:45.322 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-09 02:15:45.322 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-09 02:15:45.322 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-09 02:15:45.322 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-09 02:15:45.322 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-09 02:15:45.322 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-09 02:15:45.322 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-09 02:15:45.322 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-09 02:15:45.322 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-09 02:15:45.322 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-09 02:15:45.322 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-09 02:15:45.323 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 838aef00-985c-4d27-b961-2aa1f8164819
2025-07-09 02:15:45.323 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8592
2025-07-09 02:15:45.323 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-09 02:15:45.323 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 345
2025-07-09 02:15:45.323 [reactor-http-epoll-5] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-09 02:15:49.558 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-09 02:15:49.558 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-09 02:15:49.560 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=null
2025-07-09 02:15:49.621 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","transaction_id":"********","original_transaction_id":"********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"5","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1751969902989146435","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"512345****0008","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"28"},"commercical_card":"888","commercial_card_indicator":"3"},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"447432"},"transaction_time":"2025-07-08T17:18:37Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"***************","ip_proxy":"N","bin_country":"ANGUILLA","csc_result_code":"","enrolled_3ds":"Y","avs":{"address":"","province":"","city":"","zip_code":"","country":""},"verification_security_level":"","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"VIETINBANK","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"OTHER","status3ds":"Y","riskOverAllResult":"","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00"}
2025-07-09 02:15:49.621 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-09 02:15:49.622 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-09 02:15:49.623 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-09 02:15:49.623 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-08T17:18:37, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-07-08T17:18:37, transactionCompletedTime=2025-07-08T17:18:37, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=0, responseCode=0, merchantTransRef=TEST_1751969902989146435), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, cardExpiry=1128, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=ANGUILLA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null))
2025-07-09 02:15:49.623 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-09 02:15:50.949 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-09 02:15:50.949 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-09 02:15:50.960 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-09 02:15:50.961 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [CGV15, CGV17, CGV2, CGV4, CGV18, CGV8, CGV6, CJSHOW, CGVKIOSK, CGV, CGV13, CGV5, TESTPCI]
2025-07-09 02:15:50.961 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-09 02:15:50.961 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-07-09 02:15:50.961 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api captureTransaction MA-----------
2025-07-09 02:15:50.961 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8183/ma-service/transaction/international/******** ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={
    "id": "********",
    "op": "replace",
    "path": "/capture",
    "value": {
        "merchant_id": "TESTPCI",
        "amount": 1000,
        "currency": "VND",
        "note": "",
        "user_name": "Quản trị viên hệ thống hệ thống hệ thống hệ thống"
    },
    "skipCallSynchronize": false,
    "service": "QT"
}
2025-07-09 02:15:50.962 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api captureTransaction MA------------
2025-07-09 02:16:20.965 [ForkJoinPool.commonPool-worker-3] ERROR v.o.t.e.GlobalExceptionHandler - Unhandled exception: 
java.net.http.HttpTimeoutException: request timed out
	at java.net.http/jdk.internal.net.http.ResponseTimerEvent.handle(ResponseTimerEvent.java:63)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#captureTransaction(ServerWebExchange, String, String) [DispatcherHandler]
Original Stack Trace:
		at java.net.http/jdk.internal.net.http.ResponseTimerEvent.handle(ResponseTimerEvent.java:63)
		at java.net.http/jdk.internal.net.http.HttpClientImpl.purgeTimeoutsAndReturnNextDeadline(HttpClientImpl.java:1780)
		at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.run(HttpClientImpl.java:1378)
2025-07-09 02:16:20.966 [ForkJoinPool.commonPool-worker-3] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
org.springframework.web.server.ResponseStatusException: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Exception handler vn.onepay.transaction.exception.GlobalExceptionHandler#handleException(Exception), error="request timed out" [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x000077e3d05bdea8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
		at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:115)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:67)
		at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
		at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
		at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
		at java.base/java.util.concurrent.CompletableFuture.postFire(CompletableFuture.java:614)
		at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:844)
		at java.base/java.util.concurrent.CompletableFuture$Completion.exec(CompletableFuture.java:483)
		at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
		at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
		at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
		at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
		at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
2025-07-09 02:16:20.968 [ForkJoinPool.commonPool-worker-3] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [af4ce9eb-3]  500 Server Error for HTTP PATCH "/transaction/capture/********"
org.springframework.web.server.ResponseStatusException: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Exception handler vn.onepay.transaction.exception.GlobalExceptionHandler#handleException(Exception), error="request timed out" [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x000077e3d05bdea8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP PATCH "/ma-service/api/v1/transactions/transaction/capture/********" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
		at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:115)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:67)
		at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
		at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
		at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
		at java.base/java.util.concurrent.CompletableFuture.postFire(CompletableFuture.java:614)
		at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:844)
		at java.base/java.util.concurrent.CompletableFuture$Completion.exec(CompletableFuture.java:483)
		at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
		at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
		at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
		at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
		at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
2025-07-09 02:16:41.779 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-09 02:16:41.779 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-09 02:16:41.779 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-09 02:16:41.779 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-09 02:16:41.779 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-09 02:16:41.779 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-09 02:16:41.780 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-09 02:16:41.780 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-09 02:16:41.780 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-09 02:16:41.780 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-09 02:16:41.780 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-09 02:16:41.780 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-09 02:16:41.780 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-09 02:16:41.780 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-09 02:16:41.780 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-09 02:16:41.780 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-09 02:16:41.780 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-09 02:16:41.780 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-09 02:16:41.780 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-09 02:16:41.780 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-09 02:16:41.780 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 22119233-82a1-43bd-8048-ae54250e7ff0
2025-07-09 02:16:41.780 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8592
2025-07-09 02:16:41.780 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-09 02:16:41.780 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 345
2025-07-09 02:16:41.781 [reactor-http-epoll-5] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-09 02:16:45.090 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-09 02:16:45.090 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-09 02:16:45.091 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=null
2025-07-09 02:16:45.103 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","transaction_id":"********","original_transaction_id":"********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"5","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1751969902989146435","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"512345****0008","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"28"},"commercical_card":"888","commercial_card_indicator":"3"},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"447432"},"transaction_time":"2025-07-08T17:18:37Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"***************","ip_proxy":"N","bin_country":"ANGUILLA","csc_result_code":"","enrolled_3ds":"Y","avs":{"address":"","province":"","city":"","zip_code":"","country":""},"verification_security_level":"","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"VIETINBANK","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"OTHER","status3ds":"Y","riskOverAllResult":"","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00"}
2025-07-09 02:16:45.103 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-09 02:16:45.104 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-09 02:16:45.105 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-09 02:16:45.105 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-08T17:18:37, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-07-08T17:18:37, transactionCompletedTime=2025-07-08T17:18:37, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=0, responseCode=0, merchantTransRef=TEST_1751969902989146435), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, cardExpiry=1128, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=ANGUILLA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null))
2025-07-09 02:16:45.105 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-09 02:16:46.248 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-09 02:16:46.248 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-09 02:16:46.258 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-09 02:16:46.258 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [CGV15, CGV17, CGV2, CGV4, CGV18, CGV8, CGV6, CJSHOW, CGVKIOSK, CGV, CGV13, CGV5, TESTPCI]
2025-07-09 02:16:46.258 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-09 02:16:46.258 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-07-09 02:16:46.258 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api captureTransaction MA-----------
2025-07-09 02:16:46.259 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8183/ma-service/transaction/international/******** ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={
    "id": "********",
    "op": "replace",
    "path": "/capture",
    "value": {
        "merchant_id": "TESTPCI",
        "amount": 1000,
        "currency": "VND",
        "note": "",
        "user_name": "Quản trị viên hệ thống hệ thống hệ thống hệ thống"
    },
    "skipCallSynchronize": false,
    "service": "QT"
}
2025-07-09 02:23:05.575 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api captureTransaction MA------------
2025-07-09 02:23:20.571 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-09 02:23:20.571 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-09 02:23:20.572 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-09 02:23:20.572 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-09 02:23:20.572 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-09 02:23:20.572 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-09 02:23:20.572 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-09 02:23:20.572 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-09 02:23:20.572 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-09 02:23:20.572 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-09 02:23:20.572 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-09 02:23:20.572 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-09 02:23:20.572 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-09 02:23:20.572 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-09 02:23:20.572 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-09 02:23:20.572 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-09 02:23:20.572 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-09 02:23:20.572 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-09 02:23:20.573 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-09 02:23:20.573 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-09 02:23:20.573 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = ddaac5a4-ff5f-4933-9b7d-a07125f0e6e0
2025-07-09 02:23:20.573 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8592
2025-07-09 02:23:20.573 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-09 02:23:20.573 [reactor-http-epoll-6] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 345
2025-07-09 02:23:20.573 [reactor-http-epoll-6] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-09 02:23:23.143 [reactor-http-epoll-6] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-09 02:23:23.144 [reactor-http-epoll-6] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-09 02:23:23.145 [reactor-http-epoll-6] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=null
2025-07-09 02:23:23.157 [reactor-http-epoll-6] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","transaction_id":"********","original_transaction_id":"********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"5","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1751969902989146435","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"512345****0008","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"28"},"commercical_card":"888","commercial_card_indicator":"3"},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"447432"},"transaction_time":"2025-07-08T17:18:37Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"***************","ip_proxy":"N","bin_country":"ANGUILLA","csc_result_code":"","enrolled_3ds":"Y","avs":{"address":"","province":"","city":"","zip_code":"","country":""},"verification_security_level":"","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"VIETINBANK","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"OTHER","status3ds":"Y","riskOverAllResult":"","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00"}
2025-07-09 02:23:23.157 [reactor-http-epoll-6] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-09 02:23:23.158 [reactor-http-epoll-6] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-09 02:23:23.158 [reactor-http-epoll-6] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-09 02:23:23.158 [reactor-http-epoll-6] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-08T17:18:37, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-07-08T17:18:37, transactionCompletedTime=2025-07-08T17:18:37, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=0, responseCode=0, merchantTransRef=TEST_1751969902989146435), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, cardExpiry=1128, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=ANGUILLA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null))
2025-07-09 02:23:23.158 [reactor-http-epoll-6] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-09 02:23:24.563 [reactor-http-epoll-6] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-09 02:23:24.564 [reactor-http-epoll-6] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-09 02:23:24.572 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-09 02:23:24.572 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [CGV15, CGV17, CGV2, CGV4, CGV18, CGV8, CGV6, CJSHOW, CGVKIOSK, CGV, CGV13, CGV5, TESTPCI]
2025-07-09 02:23:24.573 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-09 02:23:24.573 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-07-09 02:23:24.573 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api captureTransaction MA-----------
2025-07-09 02:23:24.573 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8183/ma-service/transaction/international/******** ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={
    "id": "********",
    "op": "replace",
    "path": "/capture",
    "value": {
        "merchant_id": "TESTPCI",
        "amount": 1000,
        "currency": "VND",
        "note": "",
        "user_name": "Quản trị viên hệ thống hệ thống hệ thống hệ thống"
    },
    "skipCallSynchronize": false,
    "service": "QT"
}
2025-07-09 02:23:29.925 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api captureTransaction MA------------
2025-07-09 02:23:35.578 [ForkJoinPool.commonPool-worker-4] ERROR v.o.t.e.GlobalExceptionHandler - Unhandled exception: 
java.net.http.HttpTimeoutException: request timed out
	at java.net.http/jdk.internal.net.http.ResponseTimerEvent.handle(ResponseTimerEvent.java:63)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#captureTransaction(ServerWebExchange, String, String) [DispatcherHandler]
Original Stack Trace:
		at java.net.http/jdk.internal.net.http.ResponseTimerEvent.handle(ResponseTimerEvent.java:63)
		at java.net.http/jdk.internal.net.http.HttpClientImpl.purgeTimeoutsAndReturnNextDeadline(HttpClientImpl.java:1780)
		at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.run(HttpClientImpl.java:1378)
2025-07-09 02:23:35.579 [ForkJoinPool.commonPool-worker-4] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
org.springframework.web.server.ResponseStatusException: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Exception handler vn.onepay.transaction.exception.GlobalExceptionHandler#handleException(Exception), error="request timed out" [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x000077e3d05bdea8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
		at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:115)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:67)
		at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
		at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
		at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
		at java.base/java.util.concurrent.CompletableFuture.postFire(CompletableFuture.java:614)
		at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:844)
		at java.base/java.util.concurrent.CompletableFuture$Completion.exec(CompletableFuture.java:483)
		at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
		at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
		at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
		at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
		at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
2025-07-09 02:23:35.580 [ForkJoinPool.commonPool-worker-4] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [af4ce9eb-4]  500 Server Error for HTTP PATCH "/transaction/capture/********"
org.springframework.web.server.ResponseStatusException: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Exception handler vn.onepay.transaction.exception.GlobalExceptionHandler#handleException(Exception), error="request timed out" [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x000077e3d05bdea8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP PATCH "/ma-service/api/v1/transactions/transaction/capture/********" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
		at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:115)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:67)
		at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
		at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
		at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
		at java.base/java.util.concurrent.CompletableFuture.postFire(CompletableFuture.java:614)
		at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:844)
		at java.base/java.util.concurrent.CompletableFuture$Completion.exec(CompletableFuture.java:483)
		at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
		at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
		at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
		at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
		at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
2025-07-09 02:23:59.928 [ForkJoinPool.commonPool-worker-4] ERROR v.o.t.e.GlobalExceptionHandler - Unhandled exception: 
java.net.http.HttpTimeoutException: request timed out
	at java.net.http/jdk.internal.net.http.ResponseTimerEvent.handle(ResponseTimerEvent.java:63)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#captureTransaction(ServerWebExchange, String, String) [DispatcherHandler]
Original Stack Trace:
		at java.net.http/jdk.internal.net.http.ResponseTimerEvent.handle(ResponseTimerEvent.java:63)
		at java.net.http/jdk.internal.net.http.HttpClientImpl.purgeTimeoutsAndReturnNextDeadline(HttpClientImpl.java:1780)
		at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.run(HttpClientImpl.java:1378)
2025-07-09 02:23:59.929 [ForkJoinPool.commonPool-worker-4] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
org.springframework.web.server.ResponseStatusException: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Exception handler vn.onepay.transaction.exception.GlobalExceptionHandler#handleException(Exception), error="request timed out" [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x000077e3d05bdea8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
		at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:115)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:67)
		at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
		at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
		at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
		at java.base/java.util.concurrent.CompletableFuture.postFire(CompletableFuture.java:614)
		at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:844)
		at java.base/java.util.concurrent.CompletableFuture$Completion.exec(CompletableFuture.java:483)
		at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
		at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
		at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
		at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
		at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
2025-07-09 02:23:59.930 [ForkJoinPool.commonPool-worker-4] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [9fa91fab-5]  500 Server Error for HTTP PATCH "/transaction/capture/********"
org.springframework.web.server.ResponseStatusException: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Exception handler vn.onepay.transaction.exception.GlobalExceptionHandler#handleException(Exception), error="request timed out" [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x000077e3d05bdea8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP PATCH "/ma-service/api/v1/transactions/transaction/capture/********" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
		at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:115)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:67)
		at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
		at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
		at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
		at java.base/java.util.concurrent.CompletableFuture.postFire(CompletableFuture.java:614)
		at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:844)
		at java.base/java.util.concurrent.CompletableFuture$Completion.exec(CompletableFuture.java:483)
		at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
		at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
		at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
		at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
		at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
2025-07-09 02:26:44.352 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-09 02:26:44.353 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-09 02:26:44.353 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-09 02:26:44.353 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-09 02:26:44.353 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-09 02:26:44.353 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-09 02:26:44.353 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-09 02:26:44.353 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-09 02:26:44.353 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-09 02:26:44.353 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-09 02:26:44.353 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-09 02:26:44.353 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-09 02:26:44.353 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-09 02:26:44.353 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-09 02:26:44.353 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-09 02:26:44.353 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-09 02:26:44.354 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-09 02:26:44.354 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-09 02:26:44.354 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-09 02:26:44.354 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-09 02:26:44.354 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 1e4639ed-8bf2-4d2f-92a8-93b309d7c747
2025-07-09 02:26:44.354 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8592
2025-07-09 02:26:44.354 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-09 02:26:44.354 [reactor-http-epoll-7] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 345
2025-07-09 02:26:44.354 [reactor-http-epoll-7] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-09 02:26:46.961 [reactor-http-epoll-7] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-09 02:26:46.961 [reactor-http-epoll-7] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-09 02:26:46.962 [reactor-http-epoll-7] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=null
2025-07-09 02:26:46.975 [reactor-http-epoll-7] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","transaction_id":"********","original_transaction_id":"********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"5","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1751969902989146435","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"512345****0008","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"28"},"commercical_card":"888","commercial_card_indicator":"3"},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"447432"},"transaction_time":"2025-07-08T17:18:37Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"***************","ip_proxy":"N","bin_country":"ANGUILLA","csc_result_code":"","enrolled_3ds":"Y","avs":{"address":"","province":"","city":"","zip_code":"","country":""},"verification_security_level":"","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"VIETINBANK","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"OTHER","status3ds":"Y","riskOverAllResult":"","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00"}
2025-07-09 02:26:46.975 [reactor-http-epoll-7] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-09 02:26:46.976 [reactor-http-epoll-7] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-09 02:26:46.976 [reactor-http-epoll-7] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-09 02:26:46.977 [reactor-http-epoll-7] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-08T17:18:37, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-07-08T17:18:37, transactionCompletedTime=2025-07-08T17:18:37, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=0, responseCode=0, merchantTransRef=TEST_1751969902989146435), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, cardExpiry=1128, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=ANGUILLA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null))
2025-07-09 02:26:46.977 [reactor-http-epoll-7] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-09 02:26:48.396 [reactor-http-epoll-7] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-09 02:26:48.396 [reactor-http-epoll-7] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-09 02:26:48.405 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-09 02:26:48.406 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [CGV15, CGV17, CGV2, CGV4, CGV18, CGV8, CGV6, CJSHOW, CGVKIOSK, CGV, CGV13, CGV5, TESTPCI]
2025-07-09 02:26:48.406 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-09 02:26:48.406 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-07-09 02:26:48.406 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api captureTransaction MA-----------
2025-07-09 02:26:48.406 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8183/ma-service/transaction/international/******** ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={
    "id": "********",
    "op": "replace",
    "path": "/capture",
    "value": {
        "merchant_id": "TESTPCI",
        "amount": 1000,
        "currency": "VND",
        "note": "",
        "user_name": "Quản trị viên hệ thống hệ thống hệ thống hệ thống"
    },
    "skipCallSynchronize": false,
    "service": "QT"
}
2025-07-09 02:26:52.806 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api captureTransaction MA------------
2025-07-09 02:27:10.213 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-09 02:27:10.213 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-09 02:27:10.213 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-09 02:27:10.213 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-09 02:27:10.213 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-09 02:27:10.214 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-09 02:27:10.214 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-09 02:27:10.214 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-09 02:27:10.214 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-09 02:27:10.214 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-09 02:27:10.214 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-09 02:27:10.214 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-09 02:27:10.214 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-09 02:27:10.214 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-09 02:27:10.214 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-09 02:27:10.214 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-09 02:27:10.214 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-09 02:27:10.214 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-09 02:27:10.214 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-09 02:27:10.214 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-09 02:27:10.214 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = e35aefae-dc12-4057-87fc-b98b5a299295
2025-07-09 02:27:10.214 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8592
2025-07-09 02:27:10.214 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-09 02:27:10.214 [reactor-http-epoll-8] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 345
2025-07-09 02:27:10.214 [reactor-http-epoll-8] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-09 02:27:12.761 [reactor-http-epoll-8] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-09 02:27:12.761 [reactor-http-epoll-8] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-09 02:27:12.762 [reactor-http-epoll-8] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=null
2025-07-09 02:27:12.773 [reactor-http-epoll-8] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","transaction_id":"********","original_transaction_id":"********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"5","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1751969902989146435","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"512345****0008","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"28"},"commercical_card":"888","commercial_card_indicator":"3"},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"447432"},"transaction_time":"2025-07-08T17:18:37Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"***************","ip_proxy":"N","bin_country":"ANGUILLA","csc_result_code":"","enrolled_3ds":"Y","avs":{"address":"","province":"","city":"","zip_code":"","country":""},"verification_security_level":"","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"VIETINBANK","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"OTHER","status3ds":"Y","riskOverAllResult":"","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00"}
2025-07-09 02:27:12.774 [reactor-http-epoll-8] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-09 02:27:12.774 [reactor-http-epoll-8] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-09 02:27:12.775 [reactor-http-epoll-8] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-09 02:27:12.775 [reactor-http-epoll-8] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-08T17:18:37, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-07-08T17:18:37, transactionCompletedTime=2025-07-08T17:18:37, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=0, responseCode=0, merchantTransRef=TEST_1751969902989146435), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, cardExpiry=1128, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=ANGUILLA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null))
2025-07-09 02:27:12.775 [reactor-http-epoll-8] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-09 02:27:17.918 [reactor-http-epoll-8] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-09 02:27:17.918 [reactor-http-epoll-8] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-09 02:27:17.928 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-09 02:27:17.928 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [CGV15, CGV17, CGV2, CGV4, CGV18, CGV8, CGV6, CJSHOW, CGVKIOSK, CGV, CGV13, CGV5, TESTPCI]
2025-07-09 02:27:17.928 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-09 02:27:17.928 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-07-09 02:27:17.928 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api captureTransaction MA-----------
2025-07-09 02:27:17.929 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8183/ma-service/transaction/international/******** ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={
    "id": "********",
    "op": "replace",
    "path": "/capture",
    "value": {
        "merchant_id": "TESTPCI",
        "amount": 1000,
        "currency": "VND",
        "note": "",
        "user_name": "Quản trị viên hệ thống hệ thống hệ thống hệ thống"
    },
    "skipCallSynchronize": false,
    "service": "QT"
}
2025-07-09 02:27:17.929 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api captureTransaction MA------------
2025-07-09 02:27:22.809 [ForkJoinPool.commonPool-worker-5] ERROR v.o.t.e.GlobalExceptionHandler - Unhandled exception: 
java.net.http.HttpTimeoutException: request timed out
	at java.net.http/jdk.internal.net.http.ResponseTimerEvent.handle(ResponseTimerEvent.java:63)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#captureTransaction(ServerWebExchange, String, String) [DispatcherHandler]
Original Stack Trace:
		at java.net.http/jdk.internal.net.http.ResponseTimerEvent.handle(ResponseTimerEvent.java:63)
		at java.net.http/jdk.internal.net.http.HttpClientImpl.purgeTimeoutsAndReturnNextDeadline(HttpClientImpl.java:1780)
		at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.run(HttpClientImpl.java:1378)
2025-07-09 02:27:22.811 [ForkJoinPool.commonPool-worker-5] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
org.springframework.web.server.ResponseStatusException: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Exception handler vn.onepay.transaction.exception.GlobalExceptionHandler#handleException(Exception), error="request timed out" [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x000077e3d05bdea8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
		at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:115)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:67)
		at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
		at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
		at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
		at java.base/java.util.concurrent.CompletableFuture.postFire(CompletableFuture.java:614)
		at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:844)
		at java.base/java.util.concurrent.CompletableFuture$Completion.exec(CompletableFuture.java:483)
		at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
		at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
		at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
		at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
		at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
2025-07-09 02:27:22.811 [ForkJoinPool.commonPool-worker-5] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [0c04e58f-6]  500 Server Error for HTTP PATCH "/transaction/capture/********"
org.springframework.web.server.ResponseStatusException: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Exception handler vn.onepay.transaction.exception.GlobalExceptionHandler#handleException(Exception), error="request timed out" [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x000077e3d05bdea8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP PATCH "/ma-service/api/v1/transactions/transaction/capture/********" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
		at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:115)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:67)
		at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
		at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
		at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
		at java.base/java.util.concurrent.CompletableFuture.postFire(CompletableFuture.java:614)
		at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:844)
		at java.base/java.util.concurrent.CompletableFuture$Completion.exec(CompletableFuture.java:483)
		at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
		at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
		at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
		at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
		at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
2025-07-09 02:27:32.836 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-09 02:27:32.837 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-09 02:27:32.837 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-09 02:27:32.837 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-09 02:27:32.837 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-09 02:27:32.837 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-09 02:27:32.837 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-09 02:27:32.837 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-09 02:27:32.837 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-09 02:27:32.837 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-09 02:27:32.837 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-09 02:27:32.837 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-09 02:27:32.837 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-09 02:27:32.837 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-09 02:27:32.837 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-09 02:27:32.837 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-09 02:27:32.837 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-09 02:27:32.837 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-09 02:27:32.837 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-09 02:27:32.837 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-09 02:27:32.838 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 07feb28f-6b04-43bb-b6ec-8dfb263ae7ff
2025-07-09 02:27:32.838 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8592
2025-07-09 02:27:32.838 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-09 02:27:32.838 [reactor-http-epoll-9] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 345
2025-07-09 02:27:32.838 [reactor-http-epoll-9] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-09 02:27:37.573 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-09 02:27:37.574 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-09 02:27:37.574 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=null
2025-07-09 02:27:37.585 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","transaction_id":"********","original_transaction_id":"********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"5","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1751969902989146435","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"512345****0008","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"28"},"commercical_card":"888","commercial_card_indicator":"3"},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"447432"},"transaction_time":"2025-07-08T17:18:37Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"***************","ip_proxy":"N","bin_country":"ANGUILLA","csc_result_code":"","enrolled_3ds":"Y","avs":{"address":"","province":"","city":"","zip_code":"","country":""},"verification_security_level":"","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"VIETINBANK","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"OTHER","status3ds":"Y","riskOverAllResult":"","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00"}
2025-07-09 02:27:37.585 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-09 02:27:37.586 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-09 02:27:37.586 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-09 02:27:37.587 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-08T17:18:37, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-07-08T17:18:37, transactionCompletedTime=2025-07-08T17:18:37, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=0, responseCode=0, merchantTransRef=TEST_1751969902989146435), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, cardExpiry=1128, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=ANGUILLA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null))
2025-07-09 02:27:37.587 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-09 02:27:38.828 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-09 02:27:38.828 [reactor-http-epoll-9] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-09 02:27:38.838 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-09 02:27:38.838 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [CGV15, CGV17, CGV2, CGV4, CGV18, CGV8, CGV6, CJSHOW, CGVKIOSK, CGV, CGV13, CGV5, TESTPCI]
2025-07-09 02:27:38.838 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-09 02:27:38.838 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-07-09 02:27:38.838 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api captureTransaction MA-----------
2025-07-09 02:27:38.839 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8183/ma-service/transaction/international/******** ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={
    "id": "********",
    "op": "replace",
    "path": "/capture",
    "value": {
        "merchant_id": "TESTPCI",
        "amount": 1000,
        "currency": "VND",
        "note": "",
        "user_name": "Quản trị viên hệ thống hệ thống hệ thống hệ thống"
    },
    "skipCallSynchronize": false,
    "service": "QT"
}
2025-07-09 02:27:47.932 [ForkJoinPool.commonPool-worker-5] ERROR v.o.t.e.GlobalExceptionHandler - Unhandled exception: 
java.net.http.HttpTimeoutException: request timed out
	at java.net.http/jdk.internal.net.http.ResponseTimerEvent.handle(ResponseTimerEvent.java:63)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#captureTransaction(ServerWebExchange, String, String) [DispatcherHandler]
Original Stack Trace:
		at java.net.http/jdk.internal.net.http.ResponseTimerEvent.handle(ResponseTimerEvent.java:63)
		at java.net.http/jdk.internal.net.http.HttpClientImpl.purgeTimeoutsAndReturnNextDeadline(HttpClientImpl.java:1780)
		at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.run(HttpClientImpl.java:1378)
2025-07-09 02:27:47.934 [ForkJoinPool.commonPool-worker-5] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
org.springframework.web.server.ResponseStatusException: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Exception handler vn.onepay.transaction.exception.GlobalExceptionHandler#handleException(Exception), error="request timed out" [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x000077e3d05bdea8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
		at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:115)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:67)
		at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
		at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
		at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
		at java.base/java.util.concurrent.CompletableFuture.postFire(CompletableFuture.java:614)
		at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:844)
		at java.base/java.util.concurrent.CompletableFuture$Completion.exec(CompletableFuture.java:483)
		at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
		at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
		at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
		at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
		at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
2025-07-09 02:27:47.935 [ForkJoinPool.commonPool-worker-5] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [4f8d8f5c-7]  500 Server Error for HTTP PATCH "/transaction/capture/********"
org.springframework.web.server.ResponseStatusException: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Exception handler vn.onepay.transaction.exception.GlobalExceptionHandler#handleException(Exception), error="request timed out" [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x000077e3d05bdea8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP PATCH "/ma-service/api/v1/transactions/transaction/capture/********" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
		at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:115)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:67)
		at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
		at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
		at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
		at java.base/java.util.concurrent.CompletableFuture.postFire(CompletableFuture.java:614)
		at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:844)
		at java.base/java.util.concurrent.CompletableFuture$Completion.exec(CompletableFuture.java:483)
		at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
		at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
		at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
		at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
		at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
2025-07-09 02:29:07.624 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api captureTransaction MA------------
2025-07-09 02:29:37.628 [ForkJoinPool.commonPool-worker-6] ERROR v.o.t.e.GlobalExceptionHandler - Unhandled exception: 
java.net.http.HttpTimeoutException: request timed out
	at java.net.http/jdk.internal.net.http.ResponseTimerEvent.handle(ResponseTimerEvent.java:63)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#captureTransaction(ServerWebExchange, String, String) [DispatcherHandler]
Original Stack Trace:
		at java.net.http/jdk.internal.net.http.ResponseTimerEvent.handle(ResponseTimerEvent.java:63)
		at java.net.http/jdk.internal.net.http.HttpClientImpl.purgeTimeoutsAndReturnNextDeadline(HttpClientImpl.java:1780)
		at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.run(HttpClientImpl.java:1378)
2025-07-09 02:29:37.629 [ForkJoinPool.commonPool-worker-6] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
org.springframework.web.server.ResponseStatusException: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Exception handler vn.onepay.transaction.exception.GlobalExceptionHandler#handleException(Exception), error="request timed out" [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x000077e3d05bdea8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
		at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:115)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:67)
		at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
		at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
		at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
		at java.base/java.util.concurrent.CompletableFuture.postFire(CompletableFuture.java:614)
		at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:844)
		at java.base/java.util.concurrent.CompletableFuture$Completion.exec(CompletableFuture.java:483)
		at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
		at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
		at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
		at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
		at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
2025-07-09 02:29:37.630 [ForkJoinPool.commonPool-worker-6] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [fcf2b96b-8]  500 Server Error for HTTP PATCH "/transaction/capture/********"
org.springframework.web.server.ResponseStatusException: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Exception handler vn.onepay.transaction.exception.GlobalExceptionHandler#handleException(Exception), error="request timed out" [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x000077e3d05bdea8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP PATCH "/ma-service/api/v1/transactions/transaction/capture/********" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
		at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:115)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:67)
		at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
		at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
		at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
		at java.base/java.util.concurrent.CompletableFuture.postFire(CompletableFuture.java:614)
		at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:844)
		at java.base/java.util.concurrent.CompletableFuture$Completion.exec(CompletableFuture.java:483)
		at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
		at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
		at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
		at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
		at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
2025-07-09 02:31:13.183 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 4034190 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-07-09 02:31:13.185 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-07-09 02:31:14.011 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-07-09 02:31:14.259 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 238 ms. Found 2 R2DBC repository interfaces.
2025-07-09 02:31:15.769 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-07-09 02:31:16.056 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@ff03361
2025-07-09 02:31:16.058 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-07-09 02:31:16.128 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-07-09 02:31:16.260 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@5dd6a4c8
2025-07-09 02:31:16.260 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-07-09 02:31:16.326 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-07-09 02:31:16.336 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-07-09 02:31:16.340 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-07-09 02:31:16.341 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-07-09 02:31:16.343 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-07-09 02:31:16.344 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-07-09 02:31:17.299 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8592 (http)
2025-07-09 02:31:17.531 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 5.018 seconds (process running for 6.241)
2025-07-09 02:31:20.315 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-09 02:31:20.317 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-09 02:31:20.317 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-09 02:31:20.317 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-09 02:31:20.317 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-09 02:31:20.317 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-09 02:31:20.317 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-09 02:31:20.317 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-09 02:31:20.317 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-09 02:31:20.317 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-09 02:31:20.318 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-09 02:31:20.318 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-09 02:31:20.318 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-09 02:31:20.318 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-09 02:31:20.318 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-09 02:31:20.318 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-09 02:31:20.318 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-09 02:31:20.318 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-09 02:31:20.319 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-09 02:31:20.319 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-09 02:31:20.319 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = d11bbdfa-04ed-4b9a-94a2-83fa8f24905f
2025-07-09 02:31:20.319 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8592
2025-07-09 02:31:20.320 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-09 02:31:20.320 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 345
2025-07-09 02:31:20.329 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-09 02:31:24.067 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-09 02:31:24.068 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-09 02:31:24.139 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=null
2025-07-09 02:31:24.225 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","transaction_id":"********","original_transaction_id":"********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"5","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1751969902989146435","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"512345****0008","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"28"},"commercical_card":"888","commercial_card_indicator":"3"},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"447432"},"transaction_time":"2025-07-08T17:18:37Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"***************","ip_proxy":"N","bin_country":"ANGUILLA","csc_result_code":"","enrolled_3ds":"Y","avs":{"address":"","province":"","city":"","zip_code":"","country":""},"verification_security_level":"","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"VIETINBANK","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"OTHER","status3ds":"Y","riskOverAllResult":"","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00"}
2025-07-09 02:31:24.226 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-09 02:31:24.228 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-09 02:31:24.235 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-09 02:31:24.253 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-08T17:18:37, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-07-08T17:18:37, transactionCompletedTime=2025-07-08T17:18:37, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=0, responseCode=0, merchantTransRef=TEST_1751969902989146435), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, cardExpiry=1128, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=ANGUILLA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null))
2025-07-09 02:31:24.253 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-09 02:31:25.946 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-09 02:31:25.946 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-09 02:31:26.178 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-09 02:31:26.178 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [CGV15, CGV17, CGV2, CGV4, CGV18, CGV8, CGV6, CJSHOW, CGVKIOSK, CGV, CGV13, CGV5, TESTPCI]
2025-07-09 02:31:26.178 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-09 02:31:26.179 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-07-09 02:31:26.179 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api captureTransaction MA-----------
2025-07-09 02:31:26.180 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8183/ma-service/transaction/international/******** ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={
    "id": "********",
    "op": "replace",
    "path": "/capture",
    "value": {
        "merchant_id": "TESTPCI",
        "amount": 1000,
        "currency": "VND",
        "note": "",
        "user_name": "Quản trị viên hệ thống hệ thống hệ thống hệ thống"
    },
    "skipCallSynchronize": false,
    "service": "QT"
}
2025-07-09 02:31:28.020 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api captureTransaction MA------------
2025-07-09 02:31:28.058 [ForkJoinPool.commonPool-worker-1] ERROR v.o.t.e.GlobalExceptionHandler - Unhandled exception: 
java.io.IOException: /127.0.0.1:44612: GOAWAY received
	at java.net.http/jdk.internal.net.http.Http2Connection.handleGoAway(Http2Connection.java:1362)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#captureTransaction(ServerWebExchange, String, String) [DispatcherHandler]
Original Stack Trace:
		at java.net.http/jdk.internal.net.http.Http2Connection.handleGoAway(Http2Connection.java:1362)
		at java.net.http/jdk.internal.net.http.Http2Connection.handleConnectionFrame(Http2Connection.java:1170)
		at java.net.http/jdk.internal.net.http.Http2Connection.processFrame(Http2Connection.java:932)
		at java.net.http/jdk.internal.net.http.frame.FramesDecoder.decode(FramesDecoder.java:155)
		at java.net.http/jdk.internal.net.http.Http2Connection$FramesController.processReceivedData(Http2Connection.java:310)
		at java.net.http/jdk.internal.net.http.Http2Connection.asyncReceive(Http2Connection.java:859)
		at java.net.http/jdk.internal.net.http.Http2Connection$Http2TubeSubscriber.processQueue(Http2Connection.java:1756)
		at java.net.http/jdk.internal.net.http.common.SequentialScheduler$LockingRestartableTask.run(SequentialScheduler.java:182)
		at java.net.http/jdk.internal.net.http.common.SequentialScheduler$CompleteRestartableTask.run(SequentialScheduler.java:149)
		at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:207)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-09 02:31:28.069 [ForkJoinPool.commonPool-worker-1] ERROR v.o.t.exception.RequestLoggingFilter - Request error: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/******** - Exception: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
org.springframework.web.server.ResponseStatusException: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Exception handler vn.onepay.transaction.exception.GlobalExceptionHandler#handleException(Exception), error="/127.0.0.1:44612: GOAWAY received" [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x000075cdec5bd6b0 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
		at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:115)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:67)
		at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
		at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
		at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
		at java.base/java.util.concurrent.CompletableFuture.postFire(CompletableFuture.java:614)
		at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:844)
		at java.base/java.util.concurrent.CompletableFuture$Completion.exec(CompletableFuture.java:483)
		at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
		at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
		at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
		at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
		at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
2025-07-09 02:31:28.085 [ForkJoinPool.commonPool-worker-1] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [8dee42db-1]  500 Server Error for HTTP PATCH "/transaction/capture/********"
org.springframework.web.server.ResponseStatusException: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Exception handler vn.onepay.transaction.exception.GlobalExceptionHandler#handleException(Exception), error="/127.0.0.1:44612: GOAWAY received" [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x000075cdec5bd6b0 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP PATCH "/ma-service/api/v1/transactions/transaction/capture/********" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at vn.onepay.transaction.exception.GlobalExceptionHandler.handleException(GlobalExceptionHandler.java:35)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
		at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondError(MonoFlatMap.java:241)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onError(MonoFlatMap.java:315)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:115)
		at reactor.core.publisher.MonoCompletionStage$MonoCompletionStageSubscription.apply(MonoCompletionStage.java:67)
		at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
		at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
		at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
		at java.base/java.util.concurrent.CompletableFuture.postFire(CompletableFuture.java:614)
		at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:844)
		at java.base/java.util.concurrent.CompletableFuture$Completion.exec(CompletableFuture.java:483)
		at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
		at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
		at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
		at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
		at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
2025-07-09 02:34:20.287 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-09 02:34:20.287 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-09 02:34:20.287 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-09 02:34:20.287 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-09 02:34:20.288 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-09 02:34:20.288 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-09 02:34:20.288 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-09 02:34:20.288 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-09 02:34:20.288 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-09 02:34:20.288 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-09 02:34:20.288 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-09 02:34:20.288 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-09 02:34:20.288 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-09 02:34:20.288 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-09 02:34:20.288 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-09 02:34:20.288 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-09 02:34:20.288 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-09 02:34:20.288 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-09 02:34:20.288 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-09 02:34:20.289 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-09 02:34:20.289 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 9c16edd4-2ed6-40cf-9aa3-2241b282ddc8
2025-07-09 02:34:20.289 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8592
2025-07-09 02:34:20.289 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-09 02:34:20.289 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 345
2025-07-09 02:34:20.289 [reactor-http-epoll-4] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-09 02:34:24.449 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-09 02:34:24.449 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-09 02:34:51.837 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=null
2025-07-09 02:36:20.146 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 4036282 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-07-09 02:36:20.148 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-07-09 02:36:20.925 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-07-09 02:36:21.142 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 210 ms. Found 2 R2DBC repository interfaces.
2025-07-09 02:36:22.606 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-07-09 02:36:22.886 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@176333ee
2025-07-09 02:36:22.888 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-07-09 02:36:22.947 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-07-09 02:36:23.071 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@5fa0141f
2025-07-09 02:36:23.071 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-07-09 02:36:23.123 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-07-09 02:36:23.133 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-07-09 02:36:23.135 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-07-09 02:36:23.136 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-07-09 02:36:23.139 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-07-09 02:36:23.140 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-07-09 02:36:24.079 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8592 (http)
2025-07-09 02:36:24.344 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 4.886 seconds (process running for 6.018)
2025-07-09 02:36:27.481 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-09 02:36:27.482 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-09 02:36:27.482 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-09 02:36:27.483 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-09 02:36:27.483 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-09 02:36:27.483 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-09 02:36:27.483 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-09 02:36:27.483 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-09 02:36:27.483 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-09 02:36:27.483 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-09 02:36:27.483 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-09 02:36:27.483 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-09 02:36:27.483 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-09 02:36:27.483 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-09 02:36:27.483 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-09 02:36:27.483 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-09 02:36:27.483 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-09 02:36:27.483 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-09 02:36:27.484 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-09 02:36:27.484 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-09 02:36:27.484 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 5f1462cc-a234-43f1-8d75-08758b37c2fe
2025-07-09 02:36:27.484 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8592
2025-07-09 02:36:27.484 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-09 02:36:27.484 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 345
2025-07-09 02:36:27.487 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-09 02:36:30.071 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-09 02:36:30.071 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-09 02:36:30.923 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=null
2025-07-09 02:36:31.022 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","transaction_id":"********","original_transaction_id":"********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"5","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1751969902989146435","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"512345****0008","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"28"},"commercical_card":"888","commercial_card_indicator":"3"},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"447432"},"transaction_time":"2025-07-08T17:18:37Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"***************","ip_proxy":"N","bin_country":"ANGUILLA","csc_result_code":"","enrolled_3ds":"Y","avs":{"address":"","province":"","city":"","zip_code":"","country":""},"verification_security_level":"","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"VIETINBANK","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"OTHER","status3ds":"Y","riskOverAllResult":"","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00"}
2025-07-09 02:36:31.022 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-09 02:36:31.025 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-09 02:36:31.037 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-09 02:36:31.061 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-08T17:18:37, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-07-08T17:18:37, transactionCompletedTime=2025-07-08T17:18:37, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=0, responseCode=0, merchantTransRef=TEST_1751969902989146435), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, cardExpiry=1128, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=ANGUILLA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null))
2025-07-09 02:36:31.062 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-09 02:36:32.866 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-09 02:36:32.867 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-09 02:36:33.067 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-09 02:36:33.067 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [CGV15, CGV17, CGV2, CGV4, CGV18, CGV8, CGV6, CJSHOW, CGVKIOSK, CGV, CGV13, CGV5, TESTPCI]
2025-07-09 02:36:33.068 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-09 02:36:33.068 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-07-09 02:36:33.068 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api captureTransaction MA-----------
2025-07-09 02:36:33.069 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8183/ma-service/transaction/international/******** ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={
    "id": "********",
    "op": "replace",
    "path": "/capture",
    "value": {
        "merchant_id": "TESTPCI",
        "amount": 1000,
        "currency": "VND",
        "note": "",
        "user_name": "Quản trị viên hệ thống hệ thống hệ thống hệ thống"
    },
    "skipCallSynchronize": false,
    "service": "QT"
}
2025-07-09 02:36:34.334 [reactor-http-epoll-3] ERROR v.o.t.s.i.TransactionOldServiceImpl - Error call api capture: 
java.io.IOException: /127.0.0.1:32806: GOAWAY received
	at java.net.http/jdk.internal.net.http.HttpClientImpl.send(HttpClientImpl.java:966)
	at java.net.http/jdk.internal.net.http.HttpClientFacade.send(HttpClientFacade.java:133)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.captureTransaction(TransactionOldServiceImpl.java:1822)
	at vn.onepay.transaction.controller.TransactionController.lambda$5(TransactionController.java:214)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.io.IOException: /127.0.0.1:32806: GOAWAY received
	at java.net.http/jdk.internal.net.http.Http2Connection.handleGoAway(Http2Connection.java:1362)
	at java.net.http/jdk.internal.net.http.Http2Connection.handleConnectionFrame(Http2Connection.java:1170)
	at java.net.http/jdk.internal.net.http.Http2Connection.processFrame(Http2Connection.java:932)
	at java.net.http/jdk.internal.net.http.frame.FramesDecoder.decode(FramesDecoder.java:155)
	at java.net.http/jdk.internal.net.http.Http2Connection$FramesController.processReceivedData(Http2Connection.java:310)
	at java.net.http/jdk.internal.net.http.Http2Connection.asyncReceive(Http2Connection.java:859)
	at java.net.http/jdk.internal.net.http.Http2Connection$Http2TubeSubscriber.processQueue(Http2Connection.java:1756)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$LockingRestartableTask.run(SequentialScheduler.java:182)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$CompleteRestartableTask.run(SequentialScheduler.java:149)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:207)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	... 1 common frames omitted
2025-07-09 02:36:34.336 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api captureTransaction MA------------
2025-07-09 02:37:05.406 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 4036823 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-07-09 02:37:05.408 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-07-09 02:37:06.143 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-07-09 02:37:06.332 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 178 ms. Found 2 R2DBC repository interfaces.
2025-07-09 02:37:07.806 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-07-09 02:37:08.168 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@176333ee
2025-07-09 02:37:08.170 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-07-09 02:37:08.243 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-07-09 02:37:08.370 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@5fa0141f
2025-07-09 02:37:08.371 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-07-09 02:37:08.437 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-07-09 02:37:08.447 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-07-09 02:37:08.451 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-07-09 02:37:08.452 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-07-09 02:37:08.455 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-07-09 02:37:08.456 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-07-09 02:37:09.324 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8592 (http)
2025-07-09 02:37:09.518 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 4.787 seconds (process running for 5.96)
2025-07-09 02:37:14.954 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-09 02:37:14.955 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-09 02:37:14.955 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-09 02:37:14.955 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-09 02:37:14.955 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-09 02:37:14.955 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-09 02:37:14.955 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-09 02:37:14.956 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-09 02:37:14.956 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-09 02:37:14.956 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-09 02:37:14.956 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-09 02:37:14.956 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-09 02:37:14.956 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-09 02:37:14.956 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-09 02:37:14.956 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-09 02:37:14.956 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-09 02:37:14.956 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-09 02:37:14.956 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-09 02:37:14.956 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-09 02:37:14.956 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-09 02:37:14.956 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = a91967c4-4f5c-46cc-aed3-0e516e2a449b
2025-07-09 02:37:14.956 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8592
2025-07-09 02:37:14.956 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-09 02:37:14.956 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 345
2025-07-09 02:37:14.960 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-09 02:37:18.000 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-09 02:37:18.001 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-09 02:37:18.063 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=null
2025-07-09 02:37:18.265 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","transaction_id":"********","original_transaction_id":"********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"5","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1751969902989146435","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"512345****0008","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"28"},"commercical_card":"888","commercial_card_indicator":"3"},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"447432"},"transaction_time":"2025-07-08T17:18:37Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"***************","ip_proxy":"N","bin_country":"ANGUILLA","csc_result_code":"","enrolled_3ds":"Y","avs":{"address":"","province":"","city":"","zip_code":"","country":""},"verification_security_level":"","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"VIETINBANK","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"OTHER","status3ds":"Y","riskOverAllResult":"","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00"}
2025-07-09 02:37:18.265 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-09 02:37:18.267 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-09 02:37:18.275 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-09 02:37:18.292 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-08T17:18:37, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-07-08T17:18:37, transactionCompletedTime=2025-07-08T17:18:37, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=0, responseCode=0, merchantTransRef=TEST_1751969902989146435), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, cardExpiry=1128, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=ANGUILLA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null))
2025-07-09 02:37:18.292 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-09 02:37:19.615 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-09 02:37:19.616 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-09 02:37:19.801 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-09 02:37:19.801 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [CGV15, CGV17, CGV2, CGV4, CGV18, CGV8, CGV6, CJSHOW, CGVKIOSK, CGV, CGV13, CGV5, TESTPCI]
2025-07-09 02:37:19.802 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-09 02:37:19.802 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-07-09 02:37:19.802 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api captureTransaction MA-----------
2025-07-09 02:37:19.803 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8183/ma-service/transaction/international/******** ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={
    "id": "********",
    "op": "replace",
    "path": "/capture",
    "value": {
        "merchant_id": "TESTPCI",
        "amount": 1000,
        "currency": "VND",
        "note": "",
        "user_name": "Quản trị viên hệ thống hệ thống hệ thống hệ thống"
    },
    "skipCallSynchronize": false,
    "service": "QT"
}
2025-07-09 02:37:20.425 [reactor-http-epoll-3] ERROR v.o.t.s.i.TransactionOldServiceImpl - Error call api capture: 
java.io.IOException: /127.0.0.1:33776: GOAWAY received
	at java.net.http/jdk.internal.net.http.HttpClientImpl.send(HttpClientImpl.java:966)
	at java.net.http/jdk.internal.net.http.HttpClientFacade.send(HttpClientFacade.java:133)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.captureTransaction(TransactionOldServiceImpl.java:1822)
	at vn.onepay.transaction.controller.TransactionController.lambda$5(TransactionController.java:214)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.io.IOException: /127.0.0.1:33776: GOAWAY received
	at java.net.http/jdk.internal.net.http.Http2Connection.handleGoAway(Http2Connection.java:1362)
	at java.net.http/jdk.internal.net.http.Http2Connection.handleConnectionFrame(Http2Connection.java:1170)
	at java.net.http/jdk.internal.net.http.Http2Connection.processFrame(Http2Connection.java:932)
	at java.net.http/jdk.internal.net.http.frame.FramesDecoder.decode(FramesDecoder.java:155)
	at java.net.http/jdk.internal.net.http.Http2Connection$FramesController.processReceivedData(Http2Connection.java:310)
	at java.net.http/jdk.internal.net.http.Http2Connection.asyncReceive(Http2Connection.java:859)
	at java.net.http/jdk.internal.net.http.Http2Connection$Http2TubeSubscriber.processQueue(Http2Connection.java:1756)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$LockingRestartableTask.run(SequentialScheduler.java:182)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$CompleteRestartableTask.run(SequentialScheduler.java:149)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:207)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	... 1 common frames omitted
2025-07-09 02:37:20.427 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api captureTransaction MA------------
2025-07-09 02:37:23.911 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Response: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/******** - Status: 500 INTERNAL_SERVER_ERROR
2025-07-09 02:37:27.169 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8592/ma-service/api/v1/transactions/transaction/capture/********
2025-07-09 02:37:27.169 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json, text/plain, */*
2025-07-09 02:37:27.169 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-09 02:37:27.169 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-09 02:37:27.169 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-09 02:37:27.170 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-09 02:37:27.170 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev5-ma.onepay.vn
2025-07-09 02:37:27.170 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-09 02:37:27.170 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/
2025-07-09 02:37:27.170 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-09 02:37:27.170 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-09 02:37:27.170 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-09 02:37:27.170 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-09 02:37:27.170 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Request-Id = 3F9900BFF721A94338009AC70FA91C1D
2025-07-09 02:37:27.170 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-09 02:37:27.170 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-09 02:37:27.170 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-09 02:37:27.170 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzUxOTcwMTIxLCJleHAiOjE3NTE5NzczMjEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.srMGz6_U-5C8Oi67cUtqu1S49GFPV5MDlq5rYQ34EpI
2025-07-09 02:37:27.170 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 72
2025-07-09 02:37:27.170 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-09 02:37:27.171 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = c4c96c26-f574-41ac-900f-40e285f2c908
2025-07-09 02:37:27.171 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8592
2025-07-09 02:37:27.171 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-09 02:37:27.171 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 345
2025-07-09 02:37:27.171 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-09 02:37:29.899 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=******** ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-09 02:37:29.899 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-07-09 02:37:29.900 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/******** ,xUserId=null
2025-07-09 02:37:29.924 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","transaction_id":"********","original_transaction_id":"********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"5","acquirer_name":"VietcomBank","acquirer_short_name":"VietcomBank"},"transaction_reference":"TEST_1751969902989146435","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"512345****0008","card_type":"Mastercard","name_on_card":"","card_date":{"month":"11","year":"28"},"commercical_card":"888","commercial_card_indicator":"3"},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authorization_code":"447432"},"transaction_time":"2025-07-08T17:18:37Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"***************","ip_proxy":"N","bin_country":"ANGUILLA","csc_result_code":"","enrolled_3ds":"Y","avs":{"address":"","province":"","city":"","zip_code":"","country":""},"verification_security_level":"","eci":"02","operator":"","response_code":"0","can_void":true,"is_required_avs":true,"mid_no":"","risk_assesment":"Not Assessed","bank_id":"VIETINBANK","wait_for_approval_amount":"0.00","advance_status":"Successful","ticket_number":"***************","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"OTHER","status3ds":"Y","riskOverAllResult":"","xid":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","epp":"","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00"}
2025-07-09 02:37:29.924 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-07-09 02:37:29.925 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-07-09 02:37:29.926 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-07-09 02:37:29.926 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-07-08T17:18:37, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=********, parentTransactionId=********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-07-08T17:18:37, transactionCompletedTime=2025-07-08T17:18:37, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=0, responseCode=0, merchantTransRef=TEST_1751969902989146435), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=VietcomBank, cardExpiry=1128, nameOnCard=, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=N, ipCountry=***************, binCountry=ANGUILLA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null))
2025-07-09 02:37:29.926 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-07-09 02:37:31.468 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-07-09 02:37:31.469 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-07-09 02:37:31.483 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-07-09 02:37:31.483 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [CGV15, CGV17, CGV2, CGV4, CGV18, CGV8, CGV6, CJSHOW, CGVKIOSK, CGV, CGV13, CGV5, TESTPCI]
2025-07-09 02:37:31.483 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-07-09 02:37:31.483 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-07-09 02:37:31.483 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api captureTransaction MA-----------
2025-07-09 02:37:31.484 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8183/ma-service/transaction/international/******** ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={
    "id": "********",
    "op": "replace",
    "path": "/capture",
    "value": {
        "merchant_id": "TESTPCI",
        "amount": 1000,
        "currency": "VND",
        "note": "",
        "user_name": "Quản trị viên hệ thống hệ thống hệ thống hệ thống"
    },
    "skipCallSynchronize": false,
    "service": "QT"
}
