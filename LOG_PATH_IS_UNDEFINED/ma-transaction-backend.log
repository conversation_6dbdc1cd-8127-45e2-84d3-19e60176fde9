2025-07-14 07:24:30.239 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 2220037 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-07-14 07:24:30.243 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-07-14 07:24:31.076 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-07-14 07:24:31.286 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 201 ms. Found 2 R2DBC repository interfaces.
2025-07-14 07:24:33.007 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-07-14 07:24:33.309 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@39a7eca5
2025-07-14 07:24:33.311 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-07-14 07:24:33.370 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-07-14 07:24:33.476 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@16391278
2025-07-14 07:24:33.477 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-07-14 07:24:33.555 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-07-14 07:24:33.564 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-07-14 07:24:33.566 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-07-14 07:24:33.567 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-07-14 07:24:33.569 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-07-14 07:24:33.570 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-07-14 07:24:34.502 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8593 (http)
2025-07-14 07:24:34.731 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 5.214 seconds (process running for 7.429)
2025-07-14 07:25:55.778 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://:8593/ma-service/api/v1/transactions/upos/detail/PAY-a5h9Zwu5SYqtxPLzuVoLjw;ORD-O8LjzSbUQaKUhDGZ2aOfGA;Purchase?paychannel=INSTALLMENT
2025-07-14 07:25:55.779 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-07-14 07:25:55.779 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-14 07:25:55.779 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-14 07:25:55.779 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-14 07:25:55.779 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-14 07:25:55.779 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-14 07:25:55.779 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-07-14 07:25:55.779 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-14 07:25:55.779 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-14 07:25:55.779 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-14 07:25:55.779 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-14 07:25:55.779 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Partner-ID = 417604
2025-07-14 07:25:55.780 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-14 07:25:55.780 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-14 07:25:55.780 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-14 07:25:55.780 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTMzNzc2NjYsInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiIsImlkIjoiNTFERDdDOUM3REE2QTI3ODhGNEFCNTNCNTZCRTY4MTEiLCJqb2JfdGl0bGUiOiJBZG1pbiBhbMO0MjIyIiwicGhvbmUiOiIwOTAxMjM0NTYxIn19.gBnxm6R3iSLKrzIwIRNzVBHB1ECyeeo5E2kOWb7FbQ4
2025-07-14 07:25:55.780 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 5ba0b73c-517c-4c25-a38b-6ff84ff243d7
2025-07-14 07:25:55.780 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8593
2025-07-14 07:25:55.780 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-14 07:25:55.783 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-14 07:25:55.784 [reactor-http-epoll-3] WARN  v.o.t.exception.PartnerIdFilter - Missing x-user-id header
2025-07-14 07:25:55.801 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Response: GET http://:8593/ma-service/api/v1/transactions/upos/detail/PAY-a5h9Zwu5SYqtxPLzuVoLjw;ORD-O8LjzSbUQaKUhDGZ2aOfGA;Purchase?paychannel=INSTALLMENT - Status: 400 BAD_REQUEST
2025-07-14 07:26:15.699 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://:8593/ma-service/api/v1/transactions/upos/detail/PAY-a5h9Zwu5SYqtxPLzuVoLjw;ORD-O8LjzSbUQaKUhDGZ2aOfGA;Purchase?paychannel=INSTALLMENT
2025-07-14 07:26:15.700 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-07-14 07:26:15.700 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-14 07:26:15.700 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-14 07:26:15.700 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-14 07:26:15.700 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-14 07:26:15.700 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-14 07:26:15.700 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-07-14 07:26:15.700 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-14 07:26:15.701 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-14 07:26:15.701 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-14 07:26:15.701 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-14 07:26:15.701 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Partner-ID = 417604
2025-07-14 07:26:15.701 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-14 07:26:15.701 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-14 07:26:15.701 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-14 07:26:15.701 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTMzNzc2NjYsInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiIsImlkIjoiNTFERDdDOUM3REE2QTI3ODhGNEFCNTNCNTZCRTY4MTEiLCJqb2JfdGl0bGUiOiJBZG1pbiBhbMO0MjIyIiwicGhvbmUiOiIwOTAxMjM0NTYxIn19.gBnxm6R3iSLKrzIwIRNzVBHB1ECyeeo5E2kOWb7FbQ4
2025-07-14 07:26:15.701 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-14 07:26:15.701 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 468ede81-0192-4068-827e-71ebe25affc4
2025-07-14 07:26:15.701 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8593
2025-07-14 07:26:15.701 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-14 07:26:15.702 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-14 07:26:15.745 [reactor-http-epoll-3] INFO  v.o.t.service.impl.UposServiceImpl - Start service getTransactionDetail with docId: PAY-a5h9Zwu5SYqtxPLzuVoLjw
2025-07-14 07:26:15.993 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: null
2025-07-14 07:26:15.994 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [TESTENETVIET, TESTIDTEST1, TESTIDTEST2, TESTIDTEST3, TESTIDTEST4, TESTIDTEST6, TESTIDTEST5, OPTESTHB, GIANGTESTASSIGN, OP_TESTCONFIG, TEST3DS2_LT, TEST3DS2_MK, TEST3DS2_TN, OP_VPBMPGS3, TESTTGDD3D2, ONEPAYHB, TEST_BNPL, FDSFDSF, TESTJBAVND, TEST_BNPL2, TESTPCI, tcreate2, thanhdanh, danh1289, TESTTGDD, OP_SAMSUNG, BIDVEXPIRE, AUTOREFUNDUSD, danh5140, HVTT, SIVU1, SIVU, D_TEST, OP_TESTVND, OP_VND01, TESTONEPAY1, TESTHANH3, TESTBIDVV2, TESTVF, TESTIDR, TESTCNY, TESTSCB3D2, TESTTRAGOP2, TESTAPPLEND, TESTTWD, TESTSGD, TESTAPPLE, ONEPAYHM, OPMPAY, OP_TEST3DS2V, TESTTNSUSD, TESTTGPHYTO, TESTINVOICE, OP_MPAYVN1, TESTBIDV1, TESTONEPAY4, danh128950, OP_TESTAUTH, TESTONEPAY2, TESTINDOCHINAUSD, TESTMCD, HOABT, TESTNAMVH, TESTONEPAY20, OP_TESTOP20, OP_MPGSUSD, TESTSACOMCBV, TESTBIDV, TEST3DSMPGS, OP_TEST3DS, MIGS3DS, TESTBIDVCSU2, OP_CYBSVND, OP_CYBSUSD, OP_SACOMCBSV, OP_SACOMCBSU, 9.70436E+15, TESTSACOMCBU, OP_TESTUSD, ONEPAYMT, OPPREPAID, TESTAPPAY, ANHTVTEST20, TESTTEROP, 9.70436E+15, TESTATM, TESTDUONG, OP_SACOMTRVU, TESTGATEWAY, TESTAPP, TESTUSD, TESTPR, TESTVFVND, TCREATE1, TCREATE2, TCREATE3, TCREATE4, OTELBIDV, TESTMISA, TESTEXPIRED, TESTHANH5, TESTHANH6, TESTHANH7, thanhdanh2211, VTL, TESTKRW, TESTJPY, 1.23457E+19, TESTQNM, thanhdanh12, danh12890, danh510, danh3140, AHIHI, OP_SACOMTRVV, OP_MPGSVND, danh12891, TESTSAMSUNGPL, TESTSAMSUNG, DPVD, TESTOP3D2, danh31240, SIVU_DANH, THL, TESTONEPAY3, TESTTHB, TESTONEPAY7, TESTAXA1, TESTHC1, TESTMEGA1, TESTONEPAY9, TESTONEPAY10, TESTMYR, TESTOCB, TESTONEPAY, ACVNC, AOSVNBANK, AOSVNC, TESTAXA, TESTHC, TESTMEGA, TESTVTB3D2, TESTVJU, SHIHANLIFETG, TESTBNPL, TESTONEPAYUSD, FALSE, MPAYVND, OP_TESTSS, OP_SACOM2SCU, INVOICETG, OP_VPBMPGS1, OP_VPBMPGS2, TESTVCB3D2O, TESTTNS, TESTTOKEN, TESTTOKENUSD, OP_VCBVND, OP_VCBUSD, OP_MPAYVND3D, TESTVCB3D2, TESTBIDVCBSV, TESTBIDVCBSU, TESTBIDVU3, TESTBIDVV3, TESTTHSC, TESTLZD, OP_MPAYVND, TESTMAFC, TESTTRAGOP, OP_SACOM2SCV, TESTDMX, TESTSAPO2, TESTSAPO]
2025-07-14 07:26:16.001 [boundedElastic-1] INFO  v.o.t.client.OpenSearchClient - OPENSEARCH EXECUTE QUERY:null
2025-07-14 07:26:16.068 [boundedElastic-1] WARN  v.o.t.client.OpenSearchClient - OPENSEARCH EXECUTE QUERY - ResponseException (status: HTTP/1.1 404 Not Found): {"_index":"tb_0606","_id":"PAY-a5h9Zwu5SYqtxPLzuVoLjw","found":false}
2025-07-14 07:26:16.097 [boundedElastic-1] INFO  v.o.t.exception.RequestLoggingFilter - Response: GET http://:8593/ma-service/api/v1/transactions/upos/detail/PAY-a5h9Zwu5SYqtxPLzuVoLjw;ORD-O8LjzSbUQaKUhDGZ2aOfGA;Purchase?paychannel=INSTALLMENT - Status: 404 NOT_FOUND
2025-07-14 07:27:02.289 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://:8593/ma-service/api/v1/transactions/upos/detail/PAY-a5h9Zwu5SYqtxPLzuVoLjw;ORD-O8LjzSbUQaKUhDGZ2aOfGA;Purchase?paychannel=INSTALLMENT
2025-07-14 07:27:02.289 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-07-14 07:27:02.289 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-14 07:27:02.289 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-14 07:27:02.289 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-14 07:27:02.289 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-14 07:27:02.289 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-14 07:27:02.289 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-07-14 07:27:02.290 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-14 07:27:02.290 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-14 07:27:02.290 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-14 07:27:02.290 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-14 07:27:02.290 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Partner-ID = 417604
2025-07-14 07:27:02.290 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-14 07:27:02.290 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-14 07:27:02.290 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-14 07:27:02.290 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTMzNzc2NjYsInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiIsImlkIjoiNTFERDdDOUM3REE2QTI3ODhGNEFCNTNCNTZCRTY4MTEiLCJqb2JfdGl0bGUiOiJBZG1pbiBhbMO0MjIyIiwicGhvbmUiOiIwOTAxMjM0NTYxIn19.gBnxm6R3iSLKrzIwIRNzVBHB1ECyeeo5E2kOWb7FbQ4
2025-07-14 07:27:02.290 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-14 07:27:02.290 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = f193bf49-b83d-49fe-93a7-cb3cdedc4797
2025-07-14 07:27:02.291 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8593
2025-07-14 07:27:02.291 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-14 07:27:02.291 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-14 07:27:02.293 [reactor-http-epoll-3] INFO  v.o.t.service.impl.UposServiceImpl - Start service getTransactionDetail with docId: PAY-a5h9Zwu5SYqtxPLzuVoLjw
2025-07-14 07:27:02.307 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: null
2025-07-14 07:27:02.307 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [TESTENETVIET, TESTIDTEST1, TESTIDTEST2, TESTIDTEST3, TESTIDTEST4, TESTIDTEST6, TESTIDTEST5, OPTESTHB, GIANGTESTASSIGN, OP_TESTCONFIG, TEST3DS2_LT, TEST3DS2_MK, TEST3DS2_TN, OP_VPBMPGS3, TESTTGDD3D2, ONEPAYHB, TEST_BNPL, FDSFDSF, TESTJBAVND, TEST_BNPL2, TESTPCI, tcreate2, thanhdanh, danh1289, TESTTGDD, OP_SAMSUNG, BIDVEXPIRE, AUTOREFUNDUSD, danh5140, HVTT, SIVU1, SIVU, D_TEST, OP_TESTVND, OP_VND01, TESTONEPAY1, TESTHANH3, TESTBIDVV2, TESTVF, TESTIDR, TESTCNY, TESTSCB3D2, TESTTRAGOP2, TESTAPPLEND, TESTTWD, TESTSGD, TESTAPPLE, ONEPAYHM, OPMPAY, OP_TEST3DS2V, TESTTNSUSD, TESTTGPHYTO, TESTINVOICE, OP_MPAYVN1, TESTBIDV1, TESTONEPAY4, danh128950, OP_TESTAUTH, TESTONEPAY2, TESTINDOCHINAUSD, TESTMCD, HOABT, TESTNAMVH, TESTONEPAY20, OP_TESTOP20, OP_MPGSUSD, TESTSACOMCBV, TESTBIDV, TEST3DSMPGS, OP_TEST3DS, MIGS3DS, TESTBIDVCSU2, OP_CYBSVND, OP_CYBSUSD, OP_SACOMCBSV, OP_SACOMCBSU, 9.70436E+15, TESTSACOMCBU, OP_TESTUSD, ONEPAYMT, OPPREPAID, TESTAPPAY, ANHTVTEST20, TESTTEROP, 9.70436E+15, TESTATM, TESTDUONG, OP_SACOMTRVU, TESTGATEWAY, TESTAPP, TESTUSD, TESTPR, TESTVFVND, TCREATE1, TCREATE2, TCREATE3, TCREATE4, OTELBIDV, TESTMISA, TESTEXPIRED, TESTHANH5, TESTHANH6, TESTHANH7, thanhdanh2211, VTL, TESTKRW, TESTJPY, 1.23457E+19, TESTQNM, thanhdanh12, danh12890, danh510, danh3140, AHIHI, OP_SACOMTRVV, OP_MPGSVND, danh12891, TESTSAMSUNGPL, TESTSAMSUNG, DPVD, TESTOP3D2, danh31240, SIVU_DANH, THL, TESTONEPAY3, TESTTHB, TESTONEPAY7, TESTAXA1, TESTHC1, TESTMEGA1, TESTONEPAY9, TESTONEPAY10, TESTMYR, TESTOCB, TESTONEPAY, ACVNC, AOSVNBANK, AOSVNC, TESTAXA, TESTHC, TESTMEGA, TESTVTB3D2, TESTVJU, SHIHANLIFETG, TESTBNPL, TESTONEPAYUSD, FALSE, MPAYVND, OP_TESTSS, OP_SACOM2SCU, INVOICETG, OP_VPBMPGS1, OP_VPBMPGS2, TESTVCB3D2O, TESTTNS, TESTTOKEN, TESTTOKENUSD, OP_VCBVND, OP_VCBUSD, OP_MPAYVND3D, TESTVCB3D2, TESTBIDVCBSV, TESTBIDVCBSU, TESTBIDVU3, TESTBIDVV3, TESTTHSC, TESTLZD, OP_MPAYVND, TESTMAFC, TESTTRAGOP, OP_SACOM2SCV, TESTDMX, TESTSAPO2, TESTSAPO]
2025-07-14 07:27:25.421 [boundedElastic-1] INFO  v.o.t.client.OpenSearchClient - OPENSEARCH EXECUTE QUERY:null
2025-07-14 07:27:25.427 [boundedElastic-1] WARN  v.o.t.client.OpenSearchClient - OPENSEARCH EXECUTE QUERY - ResponseException (status: HTTP/1.1 404 Not Found): {"_index":"tb_0606","_id":"PAY-a5h9Zwu5SYqtxPLzuVoLjw","found":false}
2025-07-14 07:27:25.430 [boundedElastic-1] INFO  v.o.t.exception.RequestLoggingFilter - Response: GET http://:8593/ma-service/api/v1/transactions/upos/detail/PAY-a5h9Zwu5SYqtxPLzuVoLjw;ORD-O8LjzSbUQaKUhDGZ2aOfGA;Purchase?paychannel=INSTALLMENT - Status: 404 NOT_FOUND
2025-07-14 07:27:33.984 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://:8593/ma-service/api/v1/transactions/upos/detail/PAY-a5h9Zwu5SYqtxPLzuVoLjw;ORD-O8LjzSbUQaKUhDGZ2aOfGA;Purchase?paychannel=INSTALLMENT
2025-07-14 07:27:33.985 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-07-14 07:27:33.985 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-07-14 07:27:33.985 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-07-14 07:27:33.985 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-07-14 07:27:33.985 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-07-14 07:27:33.985 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-07-14 07:27:33.985 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-07-14 07:27:33.985 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-07-14 07:27:33.986 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-07-14 07:27:33.986 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-07-14 07:27:33.986 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-07-14 07:27:33.986 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: X-Partner-ID = 417604
2025-07-14 07:27:33.986 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-07-14 07:27:33.986 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-07-14 07:27:33.986 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-07-14 07:27:33.986 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTMzNzc2NjYsInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiIsImlkIjoiNTFERDdDOUM3REE2QTI3ODhGNEFCNTNCNTZCRTY4MTEiLCJqb2JfdGl0bGUiOiJBZG1pbiBhbMO0MjIyIiwicGhvbmUiOiIwOTAxMjM0NTYxIn19.gBnxm6R3iSLKrzIwIRNzVBHB1ECyeeo5E2kOWb7FbQ4
2025-07-14 07:27:33.986 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-07-14 07:27:33.986 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 1151e892-6bca-4cbc-99a6-9f0961d54f48
2025-07-14 07:27:33.987 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = :8593
2025-07-14 07:27:33.987 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-07-14 07:27:33.987 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-07-14 07:27:33.988 [reactor-http-epoll-3] INFO  v.o.t.service.impl.UposServiceImpl - Start service getTransactionDetail with docId: PAY-a5h9Zwu5SYqtxPLzuVoLjw
2025-07-14 07:27:34.001 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: null
2025-07-14 07:27:34.001 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [TESTENETVIET, TESTIDTEST1, TESTIDTEST2, TESTIDTEST3, TESTIDTEST4, TESTIDTEST6, TESTIDTEST5, OPTESTHB, GIANGTESTASSIGN, OP_TESTCONFIG, TEST3DS2_LT, TEST3DS2_MK, TEST3DS2_TN, OP_VPBMPGS3, TESTTGDD3D2, ONEPAYHB, TEST_BNPL, FDSFDSF, TESTJBAVND, TEST_BNPL2, TESTPCI, tcreate2, thanhdanh, danh1289, TESTTGDD, OP_SAMSUNG, BIDVEXPIRE, AUTOREFUNDUSD, danh5140, HVTT, SIVU1, SIVU, D_TEST, OP_TESTVND, OP_VND01, TESTONEPAY1, TESTHANH3, TESTBIDVV2, TESTVF, TESTIDR, TESTCNY, TESTSCB3D2, TESTTRAGOP2, TESTAPPLEND, TESTTWD, TESTSGD, TESTAPPLE, ONEPAYHM, OPMPAY, OP_TEST3DS2V, TESTTNSUSD, TESTTGPHYTO, TESTINVOICE, OP_MPAYVN1, TESTBIDV1, TESTONEPAY4, danh128950, OP_TESTAUTH, TESTONEPAY2, TESTINDOCHINAUSD, TESTMCD, HOABT, TESTNAMVH, TESTONEPAY20, OP_TESTOP20, OP_MPGSUSD, TESTSACOMCBV, TESTBIDV, TEST3DSMPGS, OP_TEST3DS, MIGS3DS, TESTBIDVCSU2, OP_CYBSVND, OP_CYBSUSD, OP_SACOMCBSV, OP_SACOMCBSU, 9.70436E+15, TESTSACOMCBU, OP_TESTUSD, ONEPAYMT, OPPREPAID, TESTAPPAY, ANHTVTEST20, TESTTEROP, 9.70436E+15, TESTATM, TESTDUONG, OP_SACOMTRVU, TESTGATEWAY, TESTAPP, TESTUSD, TESTPR, TESTVFVND, TCREATE1, TCREATE2, TCREATE3, TCREATE4, OTELBIDV, TESTMISA, TESTEXPIRED, TESTHANH5, TESTHANH6, TESTHANH7, thanhdanh2211, VTL, TESTKRW, TESTJPY, 1.23457E+19, TESTQNM, thanhdanh12, danh12890, danh510, danh3140, AHIHI, OP_SACOMTRVV, OP_MPGSVND, danh12891, TESTSAMSUNGPL, TESTSAMSUNG, DPVD, TESTOP3D2, danh31240, SIVU_DANH, THL, TESTONEPAY3, TESTTHB, TESTONEPAY7, TESTAXA1, TESTHC1, TESTMEGA1, TESTONEPAY9, TESTONEPAY10, TESTMYR, TESTOCB, TESTONEPAY, ACVNC, AOSVNBANK, AOSVNC, TESTAXA, TESTHC, TESTMEGA, TESTVTB3D2, TESTVJU, SHIHANLIFETG, TESTBNPL, TESTONEPAYUSD, FALSE, MPAYVND, OP_TESTSS, OP_SACOM2SCU, INVOICETG, OP_VPBMPGS1, OP_VPBMPGS2, TESTVCB3D2O, TESTTNS, TESTTOKEN, TESTTOKENUSD, OP_VCBVND, OP_VCBUSD, OP_MPAYVND3D, TESTVCB3D2, TESTBIDVCBSV, TESTBIDVCBSU, TESTBIDVU3, TESTBIDVV3, TESTTHSC, TESTLZD, OP_MPAYVND, TESTMAFC, TESTTRAGOP, OP_SACOM2SCV, TESTDMX, TESTSAPO2, TESTSAPO]
2025-07-14 07:27:47.534 [boundedElastic-1] INFO  v.o.t.client.OpenSearchClient - OPENSEARCH EXECUTE QUERY:null
2025-07-14 07:27:47.539 [boundedElastic-1] WARN  v.o.t.client.OpenSearchClient - OPENSEARCH EXECUTE QUERY - ResponseException (status: HTTP/1.1 404 Not Found): {"_index":"tb_0606","_id":"PAY-a5h9Zwu5SYqtxPLzuVoLjw","found":false}
2025-07-14 07:27:49.088 [boundedElastic-1] INFO  v.o.t.exception.RequestLoggingFilter - Response: GET http://:8593/ma-service/api/v1/transactions/upos/detail/PAY-a5h9Zwu5SYqtxPLzuVoLjw;ORD-O8LjzSbUQaKUhDGZ2aOfGA;Purchase?paychannel=INSTALLMENT - Status: 404 NOT_FOUND
2025-07-14 09:00:00.000 [transaction-report-job-1] INFO  v.o.t.job.TransactionReportJob - ⚙️ Running TransactionReportJob...
2025-07-14 09:00:00.004 [transaction-report-job-1] INFO  v.o.t.s.impl.TransactionReportImpl - Starting insertDailyTransactionReport from 2025-07-13T00:00 to 2025-07-13T23:59:59.999999999
2025-07-14 09:00:00.011 [boundedElastic-2] INFO  v.o.t.s.impl.TransactionReportImpl - Sending OpenSearch request
2025-07-14 09:00:00.013 [boundedElastic-2] INFO  v.o.t.client.OpenSearchClient - OPENSEARCH EXECUTE QUERY:{
    "size": 0,
    "query": {
        "bool": {
            "filter": [
                {
                    "range": {
                        "enrich_txn.d_create": {
                            "gte": "2025-07-13T00:00:00",
                            "lt": "2025-07-13T23:59:59.999999999",
                            "time_zone": "+07:00"
                        }
                    }
                },
                {
                    "wildcard": {
                        "enrich_txn.s_state.keyword": "Successful"
                    }
                }
            ],
            "must_not": [
                {
                    "term": {
                        "b_has_payment": true
                    }
                },
                {
                    "term": {
                        "enrich_txn.s_base_txn_type.keyword": "invoice"
                    }
                },
                {
                    "terms": {
                        "enrich_txn.s_txn_type.keyword": [
                            "Refund Request",
                            "Request Refund",
                            ""
                        ]
                    }
                }
            ]
        }
    },
    "aggs": {
        "byComposite": {
            "composite": {
                "size": 10000,
                "sources": [
                    {
                        "transactionDay": {
                            "date_histogram": {
                                "field": "enrich_txn.d_create",
                                "calendar_interval": "day",
                                "time_zone": "+07:00"
                            }
                        }
                    },
                    {
                        "merchantId": {
                            "terms": {
                                "field": "msp_merchant.s_id.keyword"
                            }
                        }
                    },
                    {
                        "currency": {
                            "terms": {
                                "field": "msp_payment.s_currency.keyword"
                            }
                        }
                    },
                    {
                        "txnType": {
                            "terms": {
                                "field": "enrich_txn.s_txn_type.keyword"
                            }
                        }
                    },
                    {
                        "paymentMethod": {
                            "terms": {
                                "field": "msp_payment.s_e_pay_method.keyword"
                            }
                        }
                    },
                    {
                        "orderSource": {
                            "terms": {
                                "field": "msp_invoice.s_e_order_source.keyword"
                            }
                        }
                    },
                    {
                        "isPromotion": {
                            "terms": {
                                "script": {
                                    "source": "def channel = doc['msp_payment.s_e_channel.keyword'].size() == 0 ? null : doc['msp_payment.s_e_channel.keyword'].value;\nreturn channel == 'Promotion';",
                                    "lang": "painless"
                                }
                            }
                        }
                    }
                ]
            },
            "aggs": {
                "totalVolume": {
                    "sum": {
                        "field": "enrich_txn.n_amount"
                    }
                }
            }
        }
    }
}
2025-07-14 09:00:00.029 [boundedElastic-2] INFO  v.o.t.s.impl.TransactionReportImpl - Received response from OpenSearch
2025-07-14 09:00:00.031 [boundedElastic-2] INFO  v.o.t.s.impl.TransactionReportImpl - Converted OpenSearch response into 0 report entities
2025-07-14 09:00:00.031 [boundedElastic-2] INFO  v.o.t.s.impl.TransactionReportImpl - Fetched 0 reports from the first OpenSearch request
2025-07-14 09:00:00.031 [boundedElastic-2] INFO  v.o.t.s.impl.TransactionReportImpl - No reports to insert into database
2025-07-14 09:00:00.031 [boundedElastic-2] INFO  v.o.t.s.impl.TransactionReportImpl - Less than 10000 reports fetched (0). Assuming last page, stopping recursion.
2025-07-14 09:05:00.000 [Promotion-report-job-1] INFO  v.o.t.job.PromotionReportJob - ⚙️ Running PromotionReportJob...
2025-07-14 09:05:00.000 [Promotion-report-job-1] INFO  v.o.t.s.impl.PromotionServiceImpl - Starting insertDailyPromotionReport from 2025-07-13T00:00 to 2025-07-13T23:59:59.999999999
2025-07-14 09:05:00.003 [boundedElastic-3] INFO  v.o.t.s.impl.PromotionServiceImpl - Sending OpenSearch request
2025-07-14 09:05:00.003 [boundedElastic-3] INFO  v.o.t.client.OpenSearchClient - OPENSEARCH EXECUTE QUERY:{
    "size": 0,
    "query": {
        "bool": {
            "filter": [
                {
                    "range": {
                        "enrich_txn.d_create": {
                            "gte": "2025-07-13T00:00:00",
                            "lt": "2025-07-13T23:59:59.999999999",
                            "time_zone": "+07:00"
                        }
                    }
                },
                {
                    "terms": {
                        "msp_payment.s_e_channel.keyword": [
                            "Promotion"
                        ]
                    }
                },
                {
                    "wildcard": {
                        "enrich_txn.s_state.keyword": "Successful"
                    }
                }
            ],
            "must_not": [
                {
                    "term": {
                        "b_has_payment": true
                    }
                },
                {
                    "term": {
                        "enrich_txn.s_base_txn_type.keyword": "invoice"
                    }
                },
                {
                    "terms": {
                        "enrich_txn.s_txn_type.keyword": [
                            "Refund Request",
                            "Request Refund",
                            ""
                        ]
                    }
                }
            ]
        }
    },
    "aggs": {
        "byComposite": {
            "composite": {
                "size": 10000,
                "sources": [
                    {
                        "transactionDay": {
                            "date_histogram": {
                                "field": "enrich_txn.d_create",
                                "calendar_interval": "day",
                                "time_zone": "+07:00"
                            }
                        }
                    },
                    {
                        "merchantId": {
                            "terms": {
                                "field": "msp_merchant.s_id.keyword"
                            }
                        }
                    },
                    {
                        "currency": {
                            "terms": {
                                "field": "msp_payment.s_currency.keyword"
                            }
                        }
                    },
                    {
                        "txnType": {
                            "terms": {
                                "field": "enrich_txn.s_txn_type.keyword"
                            }
                        }
                    },
                    {
                        "pr_id": {
                            "terms": {
                                "field": "onepr_pr.s_id.keyword"
                            }
                        }
                    },
                    {
                        "paymentMethod": {
                            "terms": {
                                "field": "msp_payment.s_e_pay_method.keyword"
                            }
                        }
                    },
                    {
                        "payGate": {
                            "terms": {
                                "field": "msp_payment.s_e_gate_label.keyword"
                            }
                        }
                    },
                    {
                        "cardType": {
                            "terms": {
                                "field": "msp_payment.s_e_card.keyword"
                            }
                        }
                    }
                ]
            },
            "aggs": {
                "totalVolume": {
                    "sum": {
                        "field": "msp_invoice.n_amount"
                    }
                },
                "totalPayment": {
                    "sum": {
                        "field": "msp_payment.n_amount"
                    }
                }
            }
        }
    }
}
2025-07-14 09:05:00.017 [boundedElastic-3] INFO  v.o.t.s.impl.PromotionServiceImpl - Received response from OpenSearch
2025-07-14 09:05:00.017 [boundedElastic-3] INFO  v.o.t.s.impl.PromotionServiceImpl - Converted OpenSearch response into 0 report entities
2025-07-14 09:05:00.017 [boundedElastic-3] INFO  v.o.t.s.impl.PromotionServiceImpl - Fetched 0 reports from the first OpenSearch request
2025-07-14 09:05:00.017 [boundedElastic-3] INFO  v.o.t.s.impl.PromotionServiceImpl - No reports to insert into database
2025-07-14 09:05:00.018 [boundedElastic-3] INFO  v.o.t.s.impl.PromotionServiceImpl - No afterKey found. All pages fetched.
2025-07-14 09:10:00.000 [Upos-report-job-1] INFO  v.o.transaction.job.UposReportJob - ⚙️ Running UposReportJob...
2025-07-14 09:10:00.001 [Upos-report-job-1] INFO  v.o.t.service.impl.UposServiceImpl - Starting insertDailyUposReport from 2025-07-13T00:00 to 2025-07-13T23:59:59.999999999
2025-07-14 09:10:00.010 [boundedElastic-4] INFO  v.o.t.service.impl.UposServiceImpl - Sending OpenSearch request
2025-07-14 09:10:00.011 [boundedElastic-4] INFO  v.o.t.client.OpenSearchClient - OPENSEARCH EXECUTE QUERY:{
    "size": 0,
    "query": {
        "bool": {
            "filter": [
                {
                    "range": {
                        "enrich_txn.d_create": {
                            "gte": "2025-07-13T00:00:00",
                            "lt": "2025-07-13T23:59:59.999999999",
                            "time_zone": "+07:00"
                        }
                    }
                },
                {
                    "terms": {
                        "msp_payment.s_e_merchant_channel.keyword": [
                            "UPOS"
                        ]
                    }
                },
                {
                    "wildcard": {
                        "enrich_txn.s_state.keyword": "Successful"
                    }
                }
            ],
            "must_not": [
                {
                    "term": {
                        "b_has_payment": true
                    }
                },
                {
                    "term": {
                        "enrich_txn.s_base_txn_type.keyword": "invoice"
                    }
                },
                {
                    "terms": {
                        "enrich_txn.s_txn_type.keyword": [
                            "Refund Request",
                            "Request Refund",
                            ""
                        ]
                    }
                }
            ]
        }
    },
    "aggs": {
        "byComposite": {
            "composite": {
                "size": 10000,
                "sources": [
                    {
                        "transactionDay": {
                            "date_histogram": {
                                "field": "enrich_txn.d_create",
                                "calendar_interval": "day",
                                "time_zone": "+07:00"
                            }
                        }
                    },
                    {
                        "merchantId": {
                            "terms": {
                                "field": "msp_merchant.s_id.keyword"
                            }
                        }
                    },
                    {
                        "currency": {
                            "terms": {
                                "field": "msp_payment.s_currency.keyword"
                            }
                        }
                    },
                    {
                        "txnType": {
                            "terms": {
                                "field": "enrich_txn.s_txn_type.keyword"
                            }
                        }
                    },
                    {
                        "paymentChannel": {
                            "terms": {
                                "script": {
                                    "source": "def method = doc['msp_payment.s_e_pay_method.keyword'].size() > 0 ? doc['msp_payment.s_e_pay_method.keyword'].value : null; if (method != 'CARD') return method; if (!doc.containsKey('msp_payment.s_ita_id.keyword') || doc['msp_payment.s_ita_id.keyword'].size() == 0) return 'CARD'; def ita = doc['msp_payment.s_ita_id.keyword'].value; return (ita == '') ? 'CARD' : 'Installment';",
                                    "lang": "painless"
                                }
                            }
                        }
                    },
                    {
                        "cardType": {
                            "terms": {
                                "field": "msp_payment.s_e_card.keyword"
                            }
                        }
                    },
                    {
                        "tid": {
                            "terms": {
                                "script": {
                                    "source": "def v = doc['msp_payment.s_data.tid.keyword'].size() == 0 ? '' : doc['msp_payment.s_data.tid.keyword'].value;\nreturn v == '' ? '' : v;",
                                    "lang": "painless"
                                }
                            }
                        }
                    }
                ]
            },
            "aggs": {
                "amount": {
                    "sum": {
                        "field": "msp_payment.n_amount"
                    }
                }
            }
        }
    }
}
2025-07-14 09:10:00.018 [boundedElastic-4] INFO  v.o.t.service.impl.UposServiceImpl - Received response from OpenSearch
2025-07-14 09:10:00.018 [boundedElastic-4] INFO  v.o.t.service.impl.UposServiceImpl - Converted OpenSearch response into 0 report entities
2025-07-14 09:10:00.019 [boundedElastic-4] INFO  v.o.t.service.impl.UposServiceImpl - Fetched 0 reports from the first OpenSearch request
2025-07-14 09:10:00.019 [boundedElastic-4] INFO  v.o.t.service.impl.UposServiceImpl - No reports to insert into database
2025-07-14 09:10:00.019 [boundedElastic-4] INFO  v.o.t.service.impl.UposServiceImpl - No afterKey found. All pages fetched.
