spring.application.name=MaTransactionBackEnd
server.address=0.0.0.0
server.port=8592
spring.webflux.base-path=/ma-service/api/v1/transactions


# spring.profiles.active=dev  

LOG_PATH=/var/log/ma-transaction-backend
#opensearch-config
opensearch.host=************
opensearch.port=9200
opensearch.protocol=http
opensearch.index=/tb_transactions_prod/_search
opensearch.index.searchDocId=/tb_transactions_prod/_doc/

# R2DBC configuration for PostgreSQL
spring.r2dbc.url=r2dbc:postgresql://db1514.onepay.vn:5432/ma?currentSchema=ma
spring.r2dbc.username=postgres
spring.r2dbc.password=4n
spring.r2dbc.pool.enabled=true
spring.r2dbc.pool.initial-size=5
spring.r2dbc.pool.max-size=20

# Disable standard JPA configuration, since you're using R2DBC
spring.jpa.hibernate.ddl-auto=none
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect

# Cron job sync transaction-report daily configuration
transaction-report.job.enabled=true
transaction-report.job.cron=0 00 16 * * *

#kafka
spring.kafka.bootstrap-servers=************:9092

# DB2 - extra
db2.r2dbc.url=r2dbc:postgresql://db1514.onepay.vn:5432/postgres?currentSchema=merchant_monitor_mm
db2.r2dbc.username=postgres
db2.r2dbc.password=4n