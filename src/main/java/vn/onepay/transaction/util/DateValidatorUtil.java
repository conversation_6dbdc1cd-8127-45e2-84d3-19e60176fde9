package vn.onepay.transaction.util;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

import vn.onepay.transaction.exception.DateRangeInvalidException;

public class DateValidatorUtil {
    public static void validateDateISORange(String fromDateStr, String toDateStr) {
        OffsetDateTime from = null;
        OffsetDateTime to = null;
        fromDateStr = normalizeOffsetZ(fromDateStr);
        toDateStr = normalizeOffsetZ(toDateStr);
        try {
            if (fromDateStr != null) {
                from = OffsetDateTime.parse(fromDateStr, DateTimeFormatter.ISO_OFFSET_DATE_TIME);
            }
            if (toDateStr != null) {
                to = OffsetDateTime.parse(toDateStr, DateTimeFormatter.ISO_OFFSET_DATE_TIME);
            }

            if (from != null && to != null && from.isAfter(to)) {
                throw new DateRangeInvalidException("fromDate must be before toDate");
            }
        } catch (DateTimeParseException e) {
            if (fromDateStr != null) {
                throw new DateRangeInvalidException("fromDate is invalid");
            }
            if (toDateStr != null) {
                throw new DateRangeInvalidException("toDate is invalid");
            }
            throw new DateRangeInvalidException("Invalid datetime format");
        }
    }

    private static String normalizeOffsetZ(String dateStr) {
        if (dateStr != null && dateStr.endsWith("z")) {
            return dateStr.substring(0, dateStr.length() - 1) + "Z";
        }
        return dateStr;
    }

    public static void validateLocalDateRange(String fromDateStr, String toDateStr) {
        LocalDate from = null;
        LocalDate to = null;
        try {
            if (fromDateStr != null) {
                from = LocalDate.parse(fromDateStr, DateTimeFormatter.ISO_LOCAL_DATE);
            }
            if (toDateStr != null) {
                to = LocalDate.parse(toDateStr, DateTimeFormatter.ISO_LOCAL_DATE);
            }
    
            if (from != null && to != null && from.isAfter(to)) {
                throw new DateRangeInvalidException("fromDate must be before toDate");
            }
        } catch (DateTimeParseException e) {
            if (fromDateStr != null) {
                throw new DateRangeInvalidException("fromDate is invalid");
            }
            if (toDateStr != null) {
                throw new DateRangeInvalidException("toDate is invalid");
            }
            throw new DateRangeInvalidException("Invalid date format");
        }
    }
}
