package vn.onepay.transaction.util;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import vn.onepay.transaction.exception.DateRangeInvalidException;

public class DateValidatorUtil {
    private static final Logger logger = LoggerFactory.getLogger(DateValidatorUtil.class);
    private static final DateTimeFormatter INPUT_FORMATTER = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
    private static final DateTimeFormatter OUTPUT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");

    public static void validateDateISORange(String fromDateStr, String toDateStr) {
        OffsetDateTime from = null;
        OffsetDateTime to = null;
        fromDateStr = normalizeOffsetZ(fromDateStr);
        toDateStr = normalizeOffsetZ(toDateStr);
        try {
            if (fromDateStr != null) {
                from = OffsetDateTime.parse(fromDateStr, DateTimeFormatter.ISO_OFFSET_DATE_TIME);
            }
            if (toDateStr != null) {
                to = OffsetDateTime.parse(toDateStr, DateTimeFormatter.ISO_OFFSET_DATE_TIME);
            }

            if (from != null && to != null && from.isAfter(to)) {
                throw new DateRangeInvalidException("fromDate must be before toDate");
            }
        } catch (DateTimeParseException e) {
            if (fromDateStr != null) {
                throw new DateRangeInvalidException("fromDate is invalid");
            }
            if (toDateStr != null) {
                throw new DateRangeInvalidException("toDate is invalid");
            }
            throw new DateRangeInvalidException("Invalid datetime format");
        }
    }

    private static String normalizeOffsetZ(String dateStr) {
        if (dateStr != null && dateStr.endsWith("z")) {
            return dateStr.substring(0, dateStr.length() - 1) + "Z";
        }
        return dateStr;
    }

    public static void validateLocalDateRange(String fromDateStr, String toDateStr) {
        LocalDate from = null;
        LocalDate to = null;
        try {
            if (fromDateStr != null) {
                from = LocalDate.parse(fromDateStr, DateTimeFormatter.ISO_LOCAL_DATE);
            }
            if (toDateStr != null) {
                to = LocalDate.parse(toDateStr, DateTimeFormatter.ISO_LOCAL_DATE);
            }

            if (from != null && to != null && from.isAfter(to)) {
                throw new DateRangeInvalidException("fromDate must be before toDate");
            }
        } catch (DateTimeParseException e) {
            if (fromDateStr != null) {
                throw new DateRangeInvalidException("fromDate is invalid");
            }
            if (toDateStr != null) {
                throw new DateRangeInvalidException("toDate is invalid");
            }
            throw new DateRangeInvalidException("Invalid date format");
        }
    }

    public static String convertDate(String datetimeStr) {
        try {
            OffsetDateTime odt = OffsetDateTime.parse(datetimeStr, INPUT_FORMATTER);
            return odt.toLocalDateTime().format(OUTPUT_FORMATTER);
        } catch (DateTimeParseException e) {
            logger.warn("Invalid datetime format: {}", datetimeStr);
            return null;
        }
    }

    public static String mapJsonForService(String jsonData) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(jsonData);
           return  jsonNode.get("service").asText();
        } catch (Exception e) {
            logger.warn("service not found: {}", jsonData);
            return null;
        }
    }

    public static BigDecimal mapJsonForAmount(String jsonData) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(jsonData);
            JsonNode valueNode = jsonNode.path("value");

            if (jsonNode.get("amount").decimalValue()!= null && jsonNode.get("amount").decimalValue().compareTo(BigDecimal.ZERO) > 0) {
                return jsonNode.get("amount").decimalValue();
            }

            return valueNode.get("amount").decimalValue();
        } catch (Exception e) {
            logger.warn("service not found: {}", jsonData);
            return BigDecimal.ZERO;
        }
    }
}
