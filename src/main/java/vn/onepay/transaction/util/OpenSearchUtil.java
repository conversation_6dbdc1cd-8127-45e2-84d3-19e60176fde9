package vn.onepay.transaction.util;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

public class OpenSearchUtil {
    public static String convertDateToISO(String dateStr) {
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
        LocalDateTime localDateTime = LocalDateTime.parse(dateStr, inputFormatter);
        ZonedDateTime zonedDateTime = localDateTime.atZone(ZoneId.of("Asia/Ho_Chi_Minh"));
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX");

        return zonedDateTime.format(outputFormatter);
    }

    public static String convertUtcToOffset(String utcDateTime) {
        // Parse chuỗi UTC (có 'Z' đuôi tức là UTC)
        OffsetDateTime utc = OffsetDateTime.parse(utcDateTime);

        // Chuyển sang offset +07:00
        OffsetDateTime offsetDateTime = utc.withOffsetSameInstant(ZoneOffset.ofHours(7));

        return offsetDateTime.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME);
    }
}
