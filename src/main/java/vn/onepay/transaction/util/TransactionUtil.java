package vn.onepay.transaction.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TransactionUtil {
    private static final Logger logger = LoggerFactory.getLogger(TransactionUtil.class);

    public static String prepareRefundRequestBNPL(String jsonData, String provider) {
        try {
            switch (provider) {
                case "":
                    return jsonData.replace("Refund", "Request Refund");
                default:
                    return jsonData;
            }
            return jsonData;
        } catch (Exception e) {
            logger.error("Error preparing refund request: {}", e.getMessage(), e);
            return jsonData;
        }
    }
}
