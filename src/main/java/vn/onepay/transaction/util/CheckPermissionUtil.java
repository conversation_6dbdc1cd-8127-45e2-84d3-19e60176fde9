package vn.onepay.transaction.util;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import oracle.net.aso.f;
import reactor.core.publisher.Mono;
import vn.onepay.transaction.constant.IConstants;
import vn.onepay.transaction.dto.RequestRefundDetail;
import vn.onepay.transaction.dto.TransactionDetailResponse;
import vn.onepay.transaction.dto.TransactionHistoryDTO;

public class CheckPermissionUtil {
    private static final Logger logger = LoggerFactory.getLogger(CheckPermissionUtil.class);
    @Value("${transaction.capture.before.day:7}")
    private static int captureBeforeDay;

    public static boolean checkPermission(Mono<List<String>> userPermissions, String requiredPermission) {
        return userPermissions.map(permissions -> permissions.contains(requiredPermission)).block();
    }

    public static boolean checkConditionShowButtonRefund(String transactionid, BigDecimal amountRefund, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail) {
        if (detail == null || detail.getTransactionInformation() == null || 
            detail.getOrderInformation() == null ||
            lstTransactionHistory == null || lstTransactionHistory.isEmpty()) {
            logger.info("checkConditionShowButtonRefund params invalid, transactionid={}", transactionid);
            return false;
        }

        // transactionType different purchase && capture => return false
        if (!detail.getTransactionInformation().getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_PURCHASE) &&
            !detail.getTransactionInformation().getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_CAPTURE)) {
                logger.info("checkConditionShowButtonRefund transactionType invalid, transactionid={}, transactionType={}", transactionid, detail.getTransactionInformation().getTransactionType());
                return false;
        }

        BigDecimal totalAmount = detail.getOrderInformation().getOrderAmount();
        BigDecimal totalRefund = BigDecimal.ZERO;
        for (TransactionHistoryDTO transactionHistoryDTO : lstTransactionHistory) {
            switch (detail.getTransactionInformation().getTransactionType()) {
                case IConstants.TRANSACTION_TYPE_PURCHASE:
                    if (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_VOID_PURCHASE)) {
                        return false;
                    }
                case IConstants.TRANSACTION_TYPE_CAPTURE:
                    if (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_VOID_CAPTURE)) {
                        return false;
                    }
                default:
                    break;
            }
             

            if (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_REQUEST_REFUND)) {
                totalRefund = totalRefund.add(transactionHistoryDTO.getAmount());
            }
        }

        totalRefund = totalRefund.add(amountRefund);
        return totalAmount.compareTo(totalRefund) > 0;
    }

    public static boolean checkConditionShowButonApproveOrRejectRefund(String transactionid, RequestRefundDetail detail) {
        if (detail == null || detail.getRefundInfomation() == null || 
            detail.getOrderInformation() == null ) {
            logger.info("checkConditionShowButonApproveOrRejectRefund params invalid, transactionid={}", transactionid);
            return false;
        }

        if (!detail.getRefundInfomation().getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_REQUEST_REFUND)) {
            logger.info("checkConditionShowButonApproveOrRejectRefund transactionType invalid, transactionid={}, transactionType={}", transactionid, detail.getRefundInfomation().getTransactionType());
            return false;
        }

        if (!detail.getRefundInfomation().getRequestStatus().equalsIgnoreCase("Wait for Merchant's Approval")) {
            return false;
        }
        
        return true;
    }

    public static boolean checkConditionShowButtonCapture(String transactionid, BigDecimal amountCapture, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail) {
        if (detail == null || detail.getTransactionInformation() == null || 
            detail.getOrderInformation() == null ||
            lstTransactionHistory == null || lstTransactionHistory.isEmpty()) {
            logger.info("checkConditionShowButtonCapture params invalid, transactionid={}", transactionid);
            return false;
        }

        // transactionType different authorize => return false
        if (!detail.getTransactionInformation().getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_AUTHORIZE)) {
            return false;
        }

        if (!detail.getTransactionInformation().getTransactionStatus().equalsIgnoreCase("Successful")) {
            logger.info("checkConditionShowButtonCapture transactionStatus invalid, transactionid={}, transactionStatus={}", transactionid, detail.getTransactionInformation().getTransactionStatus());
            return false;
        }

        // transactionDate - captureBeforeDay > now => return false
        String transactionDate = detail.getTransactionInformation().getTransactionCreatedTime();
        if (isDateMinusDaysAfterNow(transactionDate, captureBeforeDay)) {
            return false;
        }

        BigDecimal totalAmount = detail.getOrderInformation().getOrderAmount();
        BigDecimal totalCapture = BigDecimal.ZERO;
        for (TransactionHistoryDTO transactionHistoryDTO : lstTransactionHistory) {
            // has transaction type is void authorize => return false
            if (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_VOID_AUTHORIZE)) {
                return false;
            }

            if (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_AUTHORIZE)) {
                totalCapture = totalCapture.add(transactionHistoryDTO.getAmount());
            }
        }

        totalCapture = totalCapture.add(amountCapture);
        return totalAmount.compareTo(totalCapture) > 0;
    }

    public static boolean checkConditionShowButtonVoid(String transactionid, String paymentMethod, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail) {
        if (detail == null || detail.getTransactionInformation() == null || 
            detail.getOrderInformation() == null ||
            lstTransactionHistory == null || lstTransactionHistory.isEmpty()) {
            logger.info("checkConditionShowButtonRefund params invalid, transactionid={}", transactionid);
            return false;
        }

        if (!paymentMethod.equalsIgnoreCase(IConstants.PAYMENT_METHOD_INTERNATIONAL) && 
            !paymentMethod.equalsIgnoreCase(IConstants.PAYMENT_METHOD_UPOS) &&
            !paymentMethod.equalsIgnoreCase(IConstants.PAYMENT_METHOD_QT) &&
            !paymentMethod.equalsIgnoreCase(IConstants.PAYMENT_METHOD_INSTALLMENT) &&
            !paymentMethod.equalsIgnoreCase(IConstants.PAYMENT_METHOD_SS_PAY) &&
            !paymentMethod.equalsIgnoreCase(IConstants.PAYMENT_METHOD_GG_PAY) &&
            !paymentMethod.equalsIgnoreCase(IConstants.PAYMENT_METHOD_AP_PAY)) {
            return false;
        }

        if (paymentMethod.equalsIgnoreCase(IConstants.PAYMENT_METHOD_UPOS) && 
        (!detail.getTransactionInformation().getPaymentChannel().equalsIgnoreCase(IConstants.PAYMENT_CHANNEL_CARD)) &&
        (!detail.getTransactionInformation().getPaymentChannel().equalsIgnoreCase(IConstants.PAYMENT_CHANNEL_INSTALLMENT))) {
            return false;
        }

        if (!detail.getTransactionInformation().getTransactionStatus().equalsIgnoreCase("Successful")) {
            logger.info("checkConditionShowButtonVoid transactionStatus invalid, transactionid={}, transactionStatus={}", transactionid, detail.getTransactionInformation().getTransactionStatus());
            return false;
        }

        for (TransactionHistoryDTO transactionHistoryDTO : lstTransactionHistory) {
            // has transaction type is void authorize => return false
            switch (detail.getTransactionInformation().getTransactionType()) {
                case IConstants.TRANSACTION_TYPE_PURCHASE:
                    if (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_VOID_PURCHASE)) {
                        return false;
                    }
                case IConstants.TRANSACTION_TYPE_AUTHORIZE:
                    if (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_VOID_AUTHORIZE) ||
                        transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_CAPTURE)) {
                        return false;
                    }
                case IConstants.TRANSACTION_TYPE_CAPTURE:
                    if (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_VOID_CAPTURE)) {
                        return false;
                    }
                default:
                    break;
            }

        }

        return true;
    }

    /**
     * Check if (dateString - daysToSubtract) is after now
     *
     * @param dateString Date string (e.g. "2025-07-10 15:30:00")
     * @param daysToSubtract Number of days to subtract
     * @return true if (date - days) > now, false otherwise
     */
    public static boolean isDateMinusDaysAfterNow(String dateString, int daysToSubtract) {
        try {
            LocalDateTime dateTime = LocalDateTime.parse(dateString, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            LocalDateTime dateMinusDays = dateTime.minusDays(daysToSubtract);
            LocalDateTime now = LocalDateTime.now();

            return dateMinusDays.isAfter(now);
        } catch (Exception e) {
            logger.error("Error parsing date: {}", dateString, e);
            return false;
        }
    }
}
