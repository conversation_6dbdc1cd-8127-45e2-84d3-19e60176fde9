package vn.onepay.transaction.util;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import oracle.net.aso.f;
import reactor.core.publisher.Mono;
import vn.onepay.transaction.constant.IConstants;
import vn.onepay.transaction.dto.Acquirer;
import vn.onepay.transaction.dto.AcquirerDTO;
import vn.onepay.transaction.dto.RequestRefundDetail;
import vn.onepay.transaction.dto.TransactionDetailResponse;
import vn.onepay.transaction.dto.TransactionHistoryDTO;

public class CheckPermissionUtil {
    private static final Logger logger = LoggerFactory.getLogger(CheckPermissionUtil.class);
    @Value("${transaction.capture.before.day:7}")
    private static int captureBeforeDay;

    public static boolean checkPermission(Mono<List<String>> userPermissions, String requiredPermission) {
        return userPermissions.map(permissions -> permissions.contains(requiredPermission)).block();
    }

    public static boolean checkConditionShowButtonRefund(String transactionId, BigDecimal amountRefund, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail) {
        if (detail == null || detail.getTransactionInformation() == null || 
            detail.getOrderInformation() == null ||
            lstTransactionHistory == null || lstTransactionHistory.isEmpty()) {
            logger.info("checkConditionShowButtonRefund params invalid, transactionId={}", transactionId);
            return false;
        }

        // transactionType different purchase && capture => return false
        if (!detail.getTransactionInformation().getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_PURCHASE) &&
            !detail.getTransactionInformation().getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_CAPTURE)) {
                logger.info("checkConditionShowButtonRefund transactionType invalid, transactionId={}, transactionType={}", transactionId, detail.getTransactionInformation().getTransactionType());
                return false;
        }

        BigDecimal totalAmount = detail.getOrderInformation().getOrderAmount();
        BigDecimal totalRefund = BigDecimal.ZERO;
        for (TransactionHistoryDTO transactionHistoryDTO : lstTransactionHistory) {
            switch (detail.getTransactionInformation().getTransactionType()) {
                case IConstants.TRANSACTION_TYPE_PURCHASE:
                    if (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_VOID_PURCHASE)) {
                        return false;
                    }
                case IConstants.TRANSACTION_TYPE_CAPTURE:
                    if (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_VOID_CAPTURE)) {
                        return false;
                    }
                default:
                    break;
            }
             

            if (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_REQUEST_REFUND)) {
                totalRefund = totalRefund.add(transactionHistoryDTO.getAmount());
            }
        }

        totalRefund = totalRefund.add(amountRefund);
        return totalAmount.compareTo(totalRefund) > 0;
    }

    public static boolean checkConditionShowButonApproveOrRejectRefund(String transactionId, RequestRefundDetail detail) {
        if (detail == null || detail.getRefundInfomation() == null || 
            detail.getOrderInformation() == null ) {
            logger.info("checkConditionShowButonApproveOrRejectRefund params invalid, transactionId={}", transactionId);
            return false;
        }

        if (!detail.getRefundInfomation().getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_REQUEST_REFUND)) {
            logger.info("checkConditionShowButonApproveOrRejectRefund transactionType invalid, transactionId={}, transactionType={}", transactionId, detail.getRefundInfomation().getTransactionType());
            return false;
        }

        if (!detail.getRefundInfomation().getRequestStatus().equalsIgnoreCase("Wait for Merchant's Approval")) {
            return false;
        }
        
        return true;
    }

    public static boolean checkConditionShowButtonCapture(String transactionId, BigDecimal amountCapture, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail) {
        if (detail == null || detail.getTransactionInformation() == null || 
            detail.getOrderInformation() == null ||
            lstTransactionHistory == null || lstTransactionHistory.isEmpty()) {
            logger.info("checkConditionShowButtonCapture params invalid, transactionId={}", transactionId);
            return false;
        }

        // transactionType different authorize => return false
        if (!detail.getTransactionInformation().getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_AUTHORIZE)) {
            return false;
        }

        if (!detail.getTransactionInformation().getTransactionStatus().equalsIgnoreCase("Successful")) {
            logger.info("checkConditionShowButtonCapture transactionStatus invalid, transactionId={}, transactionStatus={}", transactionId, detail.getTransactionInformation().getTransactionStatus());
            return false;
        }

        // transactionDate - captureBeforeDay > now => return false
        String transactionDate = detail.getTransactionInformation().getTransactionCreatedTime();
        if (isDateMinusDaysAfterNow(transactionDate, captureBeforeDay)) {
            return false;
        }

        BigDecimal totalAmount = detail.getOrderInformation().getOrderAmount();
        BigDecimal totalCapture = BigDecimal.ZERO;
        for (TransactionHistoryDTO transactionHistoryDTO : lstTransactionHistory) {
            // has transaction type is void authorize => return false
            if (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_VOID_AUTHORIZE)) {
                return false;
            }

            if (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_AUTHORIZE)) {
                totalCapture = totalCapture.add(transactionHistoryDTO.getAmount());
            }
        }

        totalCapture = totalCapture.add(amountCapture);
        return totalAmount.compareTo(totalCapture) > 0;
    }

    public static boolean checkConditionShowButtonVoid(String transactionId, String paymentMethod, AcquirerDTO acquirers, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail) {
        if (detail == null || detail.getTransactionInformation() == null || 
            detail.getOrderInformation() == null ||
            lstTransactionHistory == null || lstTransactionHistory.isEmpty()) {
            logger.info("checkConditionShowButtonRefund params invalid, transactionId={}", transactionId);
            return false;
        }

        if (!paymentMethod.equalsIgnoreCase(IConstants.PAYMENT_METHOD_INTERNATIONAL) && 
            !paymentMethod.equalsIgnoreCase(IConstants.PAYMENT_METHOD_QT) &&
            !paymentMethod.equalsIgnoreCase(IConstants.PAYMENT_METHOD_INSTALLMENT) &&
            !paymentMethod.equalsIgnoreCase(IConstants.PAYMENT_METHOD_SS_PAY) &&
            !paymentMethod.equalsIgnoreCase(IConstants.PAYMENT_METHOD_GG_PAY) &&
            !paymentMethod.equalsIgnoreCase(IConstants.PAYMENT_METHOD_AP_PAY)) {
            return false;
        }

        if (!detail.getTransactionInformation().getTransactionStatus().equalsIgnoreCase("Successful")) {
            logger.info("checkConditionShowButtonVoid transactionStatus invalid, transactionId={}, transactionStatus={}", transactionId, detail.getTransactionInformation().getTransactionStatus());
            return false;
        }

        if (acquirers.getList() == null || acquirers.getList().isEmpty()) {
            logger.info("checkConditionShowButtonVoid acquirers invalid, transactionId={}", transactionId);
            return false;
        }

        if (detail.getPaymentMethod() == null || detail.getPaymentMethod().getAcquirer() == null) {
            logger.info("checkConditionShowButtonVoid paymentMethod invalid, transactionId={}", transactionId);
            return false;
        }

        Acquirer acquirer = acquirers.getList().stream()
            .filter(a -> a.getId() == Integer.parseInt(detail.getPaymentMethod().getAcquirer()))
            .findFirst()
            .orElse(null);

        if (acquirer == null) {
            logger.info("checkConditionShowButtonVoid acquirer invalid, transactionId={}", transactionId);
            return false;
        }

        for (TransactionHistoryDTO transactionHistoryDTO : lstTransactionHistory) {
            // has transaction type is void authorize => return false
            switch (detail.getTransactionInformation().getTransactionType()) {
                case IConstants.TRANSACTION_TYPE_PURCHASE:
                    if (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_VOID_PURCHASE)) {
                        return false;
                    }
                case IConstants.TRANSACTION_TYPE_AUTHORIZE:
                    if (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_VOID_AUTHORIZE) ||
                        transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_CAPTURE)) {
                        return false;
                    }
                case IConstants.TRANSACTION_TYPE_CAPTURE:
                    if (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_VOID_CAPTURE)) {
                        return false;
                    }
                case IConstants.TRANSACTION_TYPE_REFUND:
                    if (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_VOID_REFUND)) {
                        return false;
                    }
                default:
                    break;
            }

        }

        return true;
    }

    /**
     * Check if (dateString - daysToSubtract) is after now
     *
     * @param dateString Date string (e.g. "2025-07-10 15:30:00")
     * @param daysToSubtract Number of days to subtract
     * @return true if (date - days) > now, false otherwise
     */
    public static boolean isDateMinusDaysAfterNow(String dateString, int daysToSubtract) {
        try {
            LocalDateTime dateTime = LocalDateTime.parse(dateString, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            LocalDateTime dateMinusDays = dateTime.minusDays(daysToSubtract);
            LocalDateTime now = LocalDateTime.now();

            return dateMinusDays.isAfter(now);
        } catch (Exception e) {
            logger.error("Error parsing date: {}", dateString, e);
            return false;
        }
    }

    /**
     * Check if transaction is still valid based on cutoff logic and 24h limit
     *
     * Logic:
     * - Giao dịch trước cutoff: có thể thao tác đến cutoff cùng ngày
     * - Giao dịch sau cutoff: có thể thao tác đến cutoff ngày hôm sau
     * - Tối đa 24h từ thời điểm giao dịch
     *
     * @param dateString The transaction date string (e.g. "2025-07-10 15:30:00")
     * @param cutoffHour The cutoff hour (e.g. 17 for 17:00)
     * @return true if transaction is still valid for operations
     */
    public static boolean isCutoffValid(String dateString, int cutoffHour) {
        try {
            if (dateString == null || dateString.trim().isEmpty()) {
                logger.warn("Transaction date string is null or empty");
                return false;
            }

            if (cutoffHour < 0 || cutoffHour > 23) {
                logger.warn("Invalid cutoff hour: {}. Must be between 0-23", cutoffHour);
                return false;
            }

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime transactionTime = LocalDateTime.parse(dateString.trim(), formatter);
            LocalDateTime now = LocalDateTime.now();

            logger.debug("Checking cutoff validity - Transaction: {}, Now: {}, Cutoff hour: {}",
                        transactionTime, now, cutoffHour);

            // Tính cutoff deadline cho giao dịch này
            LocalDateTime cutoffDeadline = calculateCutoffDeadline(transactionTime, cutoffHour);

            // Kiểm tra 24h limit
            Duration duration = Duration.between(transactionTime, now);
            boolean within24Hours = duration.toHours() <= 24;

            // Kiểm tra cutoff deadline
            boolean beforeCutoff = now.isBefore(cutoffDeadline) || now.isEqual(cutoffDeadline);

            boolean isValid = within24Hours && beforeCutoff;

            logger.debug("Cutoff validation result - Within 24h: {}, Before cutoff: {}, Valid: {}, Deadline: {}",
                        within24Hours, beforeCutoff, isValid, cutoffDeadline);

            return isValid;

        } catch (Exception e) {
            logger.error("Error validating cutoff for date: {}, cutoff hour: {}", dateString, cutoffHour, e);
            return false;
        }
    }

    /**
     * Calculate cutoff deadline for a transaction
     *
     * @param transactionTime The transaction time
     * @param cutoffHour The cutoff hour (0-23)
     * @return The cutoff deadline
     */
    private static LocalDateTime calculateCutoffDeadline(LocalDateTime transactionTime, int cutoffHour) {
        LocalDate transactionDate = transactionTime.toLocalDate();
        LocalDateTime sameDayCutoff = transactionDate.atTime(cutoffHour, 0, 0);

        if (transactionTime.isBefore(sameDayCutoff) || transactionTime.isEqual(sameDayCutoff)) {
            // Giao dịch trước/đúng cutoff → deadline là cutoff cùng ngày
            return sameDayCutoff;
        } else {
            // Giao dịch sau cutoff → deadline là cutoff ngày hôm sau
            return sameDayCutoff.plusDays(1);
        }
    }
}
