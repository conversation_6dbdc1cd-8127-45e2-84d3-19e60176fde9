package vn.onepay.transaction.util;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class NotificationSender {
    private static final Logger logger = LoggerFactory.getLogger(NotificationSender.class);
    private final WebClient webClient;

    @Value("${ma-enventbus.url}")
    private String baseNotificationUrl;

    public Mono<Void> send(String userId, String fileHashName, String fileName, boolean success, long fileSize) {
        Map<String, Object> notiData = new HashMap<>();
        notiData.put("notification_id", "notification." + userId);

        Map<String, Object> detail = new HashMap<>();
        detail.put("file_hash_name", fileHashName);
        detail.put("file_name", fileName);
        detail.put("code", success ? "200" : "500");

        notiData.put("notification_detail", detail);
        logger.info("Start send noti with body: {}",notiData);
        return webClient.post()
                .uri(baseNotificationUrl + "/push")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(notiData)
                .retrieve()
                .toBodilessEntity()
                .doOnSuccess(res -> logger.info("Notification sent."))
                .doOnError(err -> logger.error("Notification failed: {}", err.getMessage()))
                .then();
    }

}