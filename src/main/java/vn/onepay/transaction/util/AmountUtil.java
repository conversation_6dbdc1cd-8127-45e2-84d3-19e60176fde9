package vn.onepay.transaction.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.Locale;

public class AmountUtil {
    private static final Logger logger = LoggerFactory.getLogger(CheckPermissionUtil.class);
    
    public static String formatAmount(BigDecimal amount, String currencyCode) {
        NumberFormat nf;
        try {
            if ("VND".equalsIgnoreCase(currencyCode)) {
                nf = NumberFormat.getInstance(Locale.of("vi", "VN")); 
                nf.setMaximumFractionDigits(0);
            } else if ("USD".equalsIgnoreCase(currencyCode)) {
                nf = NumberFormat.getInstance(Locale.US);
                nf.setMinimumFractionDigits(2);
                nf.setMaximumFractionDigits(2);
            } else {
                nf = NumberFormat.getInstance();
            }

            return nf.format(amount);
        } catch (Exception e) {
            logger.error("Error formatting amount: {}", e.getMessage(), e);
           return amount.toPlainString();
        }
    }
    
}
