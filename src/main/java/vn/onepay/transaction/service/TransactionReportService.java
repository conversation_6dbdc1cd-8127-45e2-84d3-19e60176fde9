package vn.onepay.transaction.service;

import org.springframework.web.server.ServerWebExchange;

import reactor.core.publisher.Mono;
import vn.onepay.transaction.dto.FileDownloadDto;
import vn.onepay.transaction.dto.ReportResponse;
import vn.onepay.transaction.dto.TransactionReportDTO;
import vn.onepay.transaction.entity.DownloadTaskEntity;

public interface TransactionReportService {
    Mono<ReportResponse<TransactionReportDTO>> getTransactionReports(String fromDate,String toDate,String interval,String merchantId,String currency,int page,int size,Boolean isPromotion,String paymentMethod,String orderSource);
    Mono<Void> insertDailyTransactionReport(String fromDateStr, String toDateStr);
    FileDownloadDto createExcel(DownloadTaskEntity task);
}
