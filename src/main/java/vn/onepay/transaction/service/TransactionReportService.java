package vn.onepay.transaction.service;

import java.util.Map;

import org.springframework.web.server.ServerWebExchange;

import reactor.core.publisher.Mono;
import vn.onepay.transaction.dto.PagedResponse;
import vn.onepay.transaction.dto.TransactionReportDTO;
import vn.onepay.transaction.dto.TransactionReportResponse;
import vn.onepay.transaction.entity.DownloadTaskEntity;

public interface TransactionReportService {
    Mono<TransactionReportResponse> getTransactionReports(ServerWebExchange exchange,String fromDate,String toDate,String interval,String merchantId,String currency,int page,int size);
    Mono<Void> insertDailyTransactionReport();
    String createExcel(DownloadTaskEntity task);
}
