package vn.onepay.transaction.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.r2dbc.core.DatabaseClient;
import org.springframework.stereotype.Service;

import reactor.core.publisher.Mono;
import vn.onepay.transaction.dto.ApiResponse;
import vn.onepay.transaction.dto.RawPartnerDTO;

@Service
public class PartnerService {
    private final DatabaseClient db2Client;

    public PartnerService(@Qualifier("db2DatabaseClient") DatabaseClient db2Client) {
        this.db2Client = db2Client;
    }

    public Mono<ResponseEntity<ApiResponse<List<RawPartnerDTO>>>> getRawPartners() {
        return db2Client.sql("""
                    SELECT DISTINCT partner_id, short_name, name
                    FROM raw_partners
                    WHERE partner_id IN (
                        SELECT DISTINCT partner_id FROM raw_merchants
                    )
                    ORDER BY partner_id DESC
                    LIMIT 100
                """)
                .map((row, meta) -> new RawPartnerDTO(
                        row.get("partner_id", String.class),
                        row.get("short_name", String.class),
                        row.get("name", String.class)))
                .all()
                .collectList()
                .map(data -> ResponseEntity.ok(
                        new ApiResponse<>("SUCCESS", data.size(), data)));
    }
}
