package vn.onepay.transaction.service;

import reactor.core.publisher.Mono;
import vn.onepay.transaction.dto.ApiResponse;
import vn.onepay.transaction.dto.FileDownloadDto;
import vn.onepay.transaction.dto.PagedResponse;
import vn.onepay.transaction.dto.ReportResponse;
import vn.onepay.transaction.dto.UposDetailResponse;
import vn.onepay.transaction.dto.UposListResponse;
import vn.onepay.transaction.dto.UposReportResponse;
import vn.onepay.transaction.entity.DownloadTaskEntity;

public interface UposService {
    Mono<ApiResponse<PagedResponse<UposListResponse>>> getUposTransactions(String fromDate,
            String toDate, String merchantId, String tid, String transactionId, String orderReference,
            String merchantTransRef,
            String cardNumber, String approvalCode, String paymentChannel, String cardType, String transactionType,
            String transactionState,
            String installmentStatus, int page, int size);

    Mono<UposDetailResponse> getUposDetail(String docId);
    Mono<Void> insertDailyUposReport(String fromDateStr, String toDateStr);
    FileDownloadDto createExcel(DownloadTaskEntity task);
    FileDownloadDto createReportExcel(DownloadTaskEntity task);

    Mono<ApiResponse<ReportResponse<UposReportResponse>>> getUposReport(
            String fromDate, String toDate,
            String merchantId, String interval,String currency, String tid, int page, int size,String paymentChannel);
}