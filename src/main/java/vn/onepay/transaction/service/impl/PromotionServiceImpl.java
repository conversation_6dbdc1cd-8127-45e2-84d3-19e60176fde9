package vn.onepay.transaction.service.impl;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.r2dbc.core.DatabaseClient;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import vn.onepay.transaction.config.ExportFileProperties;
import vn.onepay.transaction.constant.IConstants;
import vn.onepay.transaction.dto.ApiResponse;
import vn.onepay.transaction.dto.FileDownloadDto;
import vn.onepay.transaction.dto.PagedResponse;
import vn.onepay.transaction.dto.PromotionReportResponse;
import vn.onepay.transaction.dto.ReportResponse;
import vn.onepay.transaction.dto.TransactionPromotionDTO;
import vn.onepay.transaction.entity.DownloadTaskEntity;
import vn.onepay.transaction.entity.PromotionReportEntity;
import vn.onepay.transaction.opensearch.OpenSearchQueryBuilder;
import vn.onepay.transaction.repository.TransactionOpenSearchRepository;
import vn.onepay.transaction.service.MerchantService;
import vn.onepay.transaction.service.PromotionService;
import vn.onepay.transaction.util.DateValidatorUtil;

@Service
public class PromotionServiceImpl implements PromotionService {
    private static final Logger logger = LoggerFactory.getLogger(PromotionServiceImpl.class);
    @Autowired
    private DatabaseClient databaseClient;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private TransactionOpenSearchRepository repository;
    @Autowired
    private ExportFileProperties exportFileProperties;

    @Override
    public Mono<ApiResponse<ReportResponse<PromotionReportResponse>>> getPromotionReport(
            String from, String to, String promotionCode, String paymentMethod, String merchantId,
            String interval, String currency, int page, int size) {
        String fromDate = normalizeDate(from);
        String toDate = normalizeDate(to);
        String groupBy = switch (interval.toUpperCase()) {
            case "DAILY" -> "d_transaction_day";
            case "WEEKLY" -> "TO_CHAR(d_transaction_day, 'IYYY-\"W\"IW')";
            case "MONTHLY" -> "TO_CHAR(d_transaction_day, 'YYYY-MM')";
            default -> throw new IllegalArgumentException("Invalid interval: " + interval);
        };
        return merchantService.resolveMerchantIdFilter(merchantId)
                .flatMap(merchantIds -> {
                    if (merchantIds.isEmpty()) {
                        logger.error("No merchantId matched -> returning empty response");
                        return Mono.just(ApiResponse.success(0, null));
                    }
                    StringBuilder sqlBuilder = buildQuerySelectReport(promotionCode, paymentMethod, groupBy);
                    sqlBuilder.append("   LIMIT :limit OFFSET :offset"); // paging

                    DatabaseClient.GenericExecuteSpec spec = databaseClient.sql(sqlBuilder.toString())
                            .bind("fromDate", LocalDate.parse(fromDate))
                            .bind("toDate", LocalDate.parse(toDate))
                            .bind("currency", currency)
                            .bind("merchantIds", merchantIds)
                            .bind("limit", size)
                            .bind("offset", page * size);
                    if (promotionCode != null && !promotionCode.isBlank()) {
                        spec = spec.bind("promotionCode", promotionCode);
                    }
                    if (paymentMethod != null && !paymentMethod.isBlank()) {
                        spec = spec.bind("paymentMethod", paymentMethod);
                    }

                    Mono<List<PromotionReportResponse>> resultList = spec
                            .map((row, meta) -> {
                                return new PromotionReportResponse(
                                        row.get("merchant_id", String.class),
                                        row.get("date", String.class),
                                        row.get("cardType", String.class),
                                        row.get("currency", String.class),
                                        row.get("num_purchase_trans", Long.class),
                                        row.get("num_refund_void_trans", Long.class),
                                        row.get("total_amount", BigDecimal.class),
                                        row.get("total_original_purchase", BigDecimal.class),
                                        row.get("total_refund", BigDecimal.class));
                            })
                            .all()
                            .collectList();

                    Mono<Long> countMono = buildPromotionReportCountQuery(
                            fromDate, toDate, merchantIds, promotionCode, paymentMethod, currency, groupBy);

                    return Mono.zip(resultList, countMono)
                            .map(tuple -> {
                                List<PromotionReportResponse> items = tuple.getT1();
                                long total = tuple.getT2();
                                int totalPages = (int) Math.ceil((double) total / size);

                                // Tính tổng các trường
                                long totalPurchase = 0;
                                long totalRefundVoid = 0;
                                BigDecimal totalAmount = BigDecimal.ZERO;
                                BigDecimal totalOriginalPurchase = BigDecimal.ZERO;
                                BigDecimal totalRefund = BigDecimal.ZERO;
                                for (PromotionReportResponse dto : items) {
                                    totalPurchase += Optional.ofNullable(dto.getNumPurchaseTrans()).orElse(0L);
                                    totalRefundVoid += Optional.ofNullable(dto.getNumRefundVoidTrans()).orElse(0L);
                                    totalAmount = totalAmount
                                            .add(Optional.ofNullable(dto.getTotalAmount()).orElse(BigDecimal.ZERO));
                                    totalOriginalPurchase = totalOriginalPurchase
                                            .add(Optional.ofNullable(dto.getTotalOriginalPurchase())
                                                    .orElse(BigDecimal.ZERO));
                                    totalRefund = totalRefund
                                            .add(Optional.ofNullable(dto.getTotalRefund()).orElse(BigDecimal.ZERO));
                                }
                                // Tạo bản ghi tổng

                                String summaryDate = fromDate + " - " + toDate;
                                PromotionReportResponse totalDto = new PromotionReportResponse(
                                        null, summaryDate, null, null,
                                        totalPurchase, totalRefundVoid, totalAmount, totalOriginalPurchase,
                                        totalRefund);

                                PagedResponse<PromotionReportResponse> pagedResponse = PagedResponse
                                        .<PromotionReportResponse>builder()
                                        .currentPage(page)
                                        .pageSize(size)
                                        .totalPages(totalPages)
                                        .total(total)
                                        .items(items)
                                        .build();

                                return ApiResponse.success(total, new ReportResponse<PromotionReportResponse>(pagedResponse, totalDto));
                            })
                            .onErrorResume(e -> {
                                logger.error("Error getPromotionReports: ", e);
                                return Mono.just(ApiResponse.error(""));
                            });
                });
    }

    private static StringBuilder buildQuerySelectReport(String promotionCode, String paymentMethod, String groupBy) {
        StringBuilder sqlBuilder = new StringBuilder(
                """
                            SELECT
                                %s AS date,
                                merchant_id,
                                cardType,
                                currency,
                                SUM(CASE WHEN transaction_type = 'Purchase' THEN total_trans ELSE 0 END) AS num_purchase_trans,
                                SUM(CASE WHEN transaction_type IN (
                                        'Refund',
                                        'Refund Capture',
                                        'Refund Dispute',
                                        'Void Purchase',
                                        'Void Authorize',
                                        'Void Capture',
                                        'Void Refund',
                                        'Void Refund Capture'
                                    ) THEN total_trans
                                    ELSE 0 END) AS num_refund_void_trans,
                                SUM(amountInvoice) AS total_amount,
                                SUM(amountPayment) AS total_original_purchase,
                                SUM(CASE WHEN transaction_type IN (
                                        'Refund',
                                        'Refund Capture',
                                        'Refund Dispute',
                                        'Void Purchase',
                                        'Void Authorize',
                                        'Void Capture',
                                        'Void Refund',
                                        'Void Refund Capture'
                                    ) THEN amountPayment ELSE 0 END) AS total_refund
                            FROM promotion_report
                            WHERE d_transaction_day BETWEEN :fromDate AND :toDate
                                AND merchant_id IN (:merchantIds)
                        """
                        .formatted(groupBy));

        if (promotionCode != null && !promotionCode.isBlank()) {
            sqlBuilder.append(" AND promotionCode = :promotionCode");
        }
        if (paymentMethod != null && !paymentMethod.isBlank()) {
            sqlBuilder.append(" AND paymentMethod = :paymentMethod");
        }

        sqlBuilder.append("""
                    AND (:currency IS NULL OR currency = :currency)
                    GROUP BY %s, merchant_id, cardType, currency
                    ORDER BY %s DESC
                """.formatted(groupBy, groupBy));
        return sqlBuilder;
    }

    private Mono<Long> buildPromotionReportCountQuery(
            String fromDate,
            String toDate,
            List<String> merchantIds,
            String promotionCode,
            String paymentMethod,
            String currency,
            String groupBy) {

        StringBuilder sqlBuilder = new StringBuilder("""
                    SELECT COUNT(*) AS cnt FROM (
                        SELECT 1 FROM promotion_report
                        WHERE d_transaction_day BETWEEN :fromDate AND :toDate
                """);

        if (!merchantIds.isEmpty()) {
            sqlBuilder.append(" AND merchant_id IN (");
            for (int i = 0; i < merchantIds.size(); i++) {
                sqlBuilder.append(":merchant").append(i);
                if (i < merchantIds.size() - 1)
                    sqlBuilder.append(", ");
            }
            sqlBuilder.append(")");
        }

        if (promotionCode != null && !promotionCode.isBlank()) {
            sqlBuilder.append(" AND promotionCode = :promotionCode");
        }
        if (paymentMethod != null && !paymentMethod.isBlank()) {
            sqlBuilder.append(" AND paymentMethod = :paymentMethod");
        }

        sqlBuilder.append("""
                    AND (:currency IS NULL OR currency = :currency)
                    GROUP BY %s, merchant_id, cardType, currency
                ) sub
                """.formatted(groupBy));

        DatabaseClient.GenericExecuteSpec spec = databaseClient.sql(sqlBuilder.toString())
                .bind("fromDate", LocalDate.parse(fromDate))
                .bind("toDate", LocalDate.parse(toDate))
                .bind("currency", currency);

        for (int i = 0; i < merchantIds.size(); i++) {
            spec = spec.bind("merchant" + i, merchantIds.get(i));
        }

        if (promotionCode != null && !promotionCode.isBlank()) {
            spec = spec.bind("promotionCode", promotionCode);
        }
        if (paymentMethod != null && !paymentMethod.isBlank()) {
            spec = spec.bind("paymentMethod", paymentMethod);
        }

        return spec.map((row, meta) -> row.get("cnt", Long.class)).one().defaultIfEmpty(0L);
    }

    public static String normalizeDate(String fromDate) {
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        try {
            LocalDate date = LocalDate.parse(fromDate, inputFormatter);
            return outputFormatter.format(date);
        } catch (DateTimeParseException e) {
            // Không đúng định dạng dd-MM-yyyy => giữ nguyên
            return fromDate;
        }
    }

    @Override
    public Mono<Void> insertDailyPromotionReport(String fromDateStr, String toDateStr) {
        LocalDateTime fromDate = LocalDate.parse(fromDateStr).atStartOfDay();
        LocalDateTime toDate = LocalDate.parse(toDateStr).atTime(LocalTime.MAX);
        logger.info("Starting insertDailyPromotionReport from {} to {}", fromDate, toDate);

        DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME;
        String from = fromDate.format(formatter);
        String to = toDate.format(formatter);
        String baseQuery = IConstants.basePromotionConditionJson.formatted(from, to);

        return fetchPromotionReportFromOpenSearch(baseQuery, null)
                .flatMap(reports -> {
                    logger.info("Fetched {} reports from the first OpenSearch request", reports.size());
                    return insertReportsIntoDatabase(reports)
                            .then(fetchNextPageReports(baseQuery, reports));
                })
                .doOnError(e -> logger.error("Error occurred during insertDailyPromotionReport", e))
                .then();
    }

    private Mono<Void> fetchNextPageReports(String baseQuery, List<PromotionReportEntity> previousReports) {
        String afterKeyJson = getAfterKeyFromReports(previousReports);
        if (afterKeyJson != null) {
            try {
                JsonNode afterKeyNode = new ObjectMapper().readTree(afterKeyJson);
                String queryWithAfterKey = OpenSearchQueryBuilder.buildQueryWithAfterKey(baseQuery, afterKeyNode);
                logger.info("Built query with afterKey: {}", afterKeyJson);

                return fetchPromotionReportFromOpenSearch(queryWithAfterKey, afterKeyJson)
                        .flatMap(nextReports -> {
                            logger.info("Fetched {} reports in next page", nextReports.size());
                            return insertReportsIntoDatabase(nextReports)
                                    .then(fetchNextPageReports(baseQuery, nextReports));
                        });
            } catch (Exception e) {
                logger.error("Failed to parse afterKey JSON: {}", afterKeyJson, e);
                throw new RuntimeException("Failed to parse afterKey JSON: " + afterKeyJson, e);
            }
        } else {
            logger.info("No afterKey found. All pages fetched.");
            return Mono.empty();
        }
    }

    private Mono<List<PromotionReportEntity>> fetchPromotionReportFromOpenSearch(String query, String afterKey) {
        return Mono.fromCallable(() -> {
            try {
                logger.info("Sending OpenSearch request{}", afterKey != null ? " with afterKey: " + afterKey : "");
                String response = repository.getTransactionReport(query);
                logger.info("Received response from OpenSearch");
                return convertToDTO(response);
            } catch (Exception e) {
                logger.error("Failed to fetch promotion report from OpenSearch", e);
                throw new RuntimeException("Failed to fetch promotion report from OpenSearch", e);
            }
        });
    }

    private List<PromotionReportEntity> convertToDTO(String response) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            JsonNode rootNode = mapper.readTree(response);
            JsonNode buckets = rootNode.path("aggregations").path("byComposite").path("buckets");
            List<PromotionReportEntity> reports = new ArrayList<>();

            for (JsonNode bucket : buckets) {
                PromotionReportEntity entity = new PromotionReportEntity();
                entity.setMerchantId(bucket.path("key").path("merchantId").asText());

                long epochMillis = bucket.path("key").path("transactionDay").asLong();
                LocalDate transactionDay = Instant.ofEpochMilli(epochMillis)
                        .atZone(ZoneId.of("Asia/Ho_Chi_Minh")).toLocalDate();
                entity.setDTransactionDay(transactionDay);
                entity.setTotalTrans(bucket.path("doc_count").asLong());
                entity.setCurrency(bucket.path("key").path("currency").asText());
                entity.setTransactionType(bucket.path("key").path("txnType").asText());
                entity.setPaymentMethod(bucket.path("key").path("paymentMethod").asText());
                entity.setPayGate(bucket.path("key").path("payGate").asText());
                entity.setCardType(bucket.path("key").path("cardType").asText());
                entity.setPromotionCode(bucket.path("key").path("pr_id").asText());
                JsonNode totalVolumeNode = bucket.path("totalVolume").path("value");
                Long amountInvoice = totalVolumeNode.isNull() ? 0L : totalVolumeNode.asLong();
                entity.setAmountInvoice(BigDecimal.valueOf(amountInvoice));
                JsonNode totalPaymentNode = bucket.path("totalPayment").path("value");
                Long amountPayment = totalPaymentNode.isNull() ? 0L : totalPaymentNode.asLong();
                entity.setAmountPayment(BigDecimal.valueOf(amountPayment));
                reports.add(entity);
            }

            logger.info("Converted OpenSearch response into {} report entities", reports.size());
            return reports;
        } catch (Exception e) {
            logger.error("Error converting OpenSearch response to DTO", e);
            throw new RuntimeException("Error converting OpenSearch response to DTO", e);
        }
    }

    public Mono<Void> insertReportsIntoDatabase(List<PromotionReportEntity> reports) {
        if (reports.isEmpty()) {
            logger.info("No reports to insert into database");
            return Mono.empty();
        }

        logger.info("Inserting {} reports into the database", reports.size());
        // insert song song nếu có nhiều connection
        // return Mono.when(
        // reports.stream()
        // .map(this::upsertReport)
        // .toArray(Mono[]::new))
        // .doOnError(e -> logger.error("Error occurred during database insertion", e))
        // .then();
        return Flux.fromIterable(reports)
                .flatMap(this::upsertReport, 10) // chạy tối đa 10 connections
                .then()
                .doOnError(e -> logger.error("Error occurred during database insertion", e));

    }

    private Mono<Void> upsertReport(PromotionReportEntity report) {
        logger.debug("Upserting report: {}", report);

        String upsertSql = "INSERT INTO promotion_report (d_transaction_day, merchant_id, transaction_type, payGate, cardType, promotionCode, paymentMethod, currency,amountInvoice,amountPayment,created_date,updated_date,total_trans) "
                + "VALUES (:d_transaction_day, :merchant_id, :transaction_type, :payGate, :cardType, :promotionCode, :paymentMethod, :currency,:amountInvoice,:amountPayment,:created_date,:updated_date,:total_trans) "
                + "ON CONFLICT (d_transaction_day, merchant_id, transaction_type, currency,payGate,cardType,promotionCode,paymentMethod) "
                + "DO UPDATE SET amountPayment = :amountPayment,amountInvoice =:amountInvoice, updated_date = :updated_date, total_trans = :total_trans";

        return databaseClient.sql(upsertSql)
                .bind("d_transaction_day", report.getDTransactionDay())
                .bind("merchant_id", report.getMerchantId())
                .bind("transaction_type", report.getTransactionType())
                .bind("currency", report.getCurrency())
                .bind("cardType", report.getCardType())
                .bind("payGate", report.getPayGate())
                .bind("paymentMethod", report.getPaymentMethod())
                .bind("promotionCode", report.getPromotionCode())
                .bind("amountInvoice", report.getAmountInvoice())
                .bind("amountPayment", report.getAmountPayment())
                .bind("created_date", LocalDateTime.now())
                .bind("updated_date", LocalDateTime.now())
                .bind("total_trans", report.getTotalTrans())
                .then()
                .doOnError(e -> logger.error("Error upserting report: {}", report, e)); // now valid
    }

    private String getAfterKeyFromReports(List<PromotionReportEntity> reports) {
        if (reports.isEmpty())
            return null;

        PromotionReportEntity last = reports.get(reports.size() - 1);
        Map<String, Object> afterKey = new LinkedHashMap<>();
        afterKey.put("transactionDay",
                last.getDTransactionDay().atStartOfDay(ZoneId.of("Asia/Ho_Chi_Minh")).toInstant().toEpochMilli());
        afterKey.put("merchantId", last.getMerchantId());
        afterKey.put("currency", last.getCurrency());
        afterKey.put("txnType", last.getTransactionType());
        afterKey.put("pr_id", last.getPromotionCode());
        afterKey.put("paymentMethod", last.getPaymentMethod());
        afterKey.put("payGate", last.getPayGate());
        afterKey.put("cardType", last.getCardType());

        try {
            String afterKeyStr = new ObjectMapper().writeValueAsString(afterKey);
            logger.info("Generated afterKey from last report: {}", afterKeyStr);
            return afterKeyStr;
        } catch (JsonProcessingException e) {
            logger.error("Failed to serialize afterKey", e);
            throw new RuntimeException("Failed to serialize afterKey", e);
        }
    }

    @Override
    public FileDownloadDto createExcel(DownloadTaskEntity task) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            logger.info("Start exporting Excel for taskId={}, fileName={}", task.getId(), task.getFileName());

            // Tạo thư mục lưu file nếu chưa có
            String baseDir = exportFileProperties.getBaseDir();
            File dir = new File(baseDir);
            if (!dir.exists())
                dir.mkdirs();

            String outputPath = baseDir + task.getFileHashName();
            // Parse requestParams từ task
            Map<String, Object> params = objectMapper.readValue(task.getRequestParams(), new TypeReference<>() {
            });
            String fromDate = (String) params.get("fromDate");
            String toDate = (String) params.get("toDate");
            String merchantId = (String) params.get("merchantId");
            String transId = (String) params.get("transId");
            String paymentMethod = (String) params.get("paymentMethod");
            String sortOrder = (String) params.getOrDefault("sortOrder", "desc");
            String transactionStatus = (String) params.get("transactionStatus");
            String transactionType = (String) params.get("transactionType");
            String authCode = (String) params.get("authCode");
            String cardNumber = (String) params.get("cardNumber");
            String merchantTransRef = (String) params.get("merchantTransRef");
            String promoName = (String) params.get("promoName");
            String promoCode = (String) params.get("promoCode");
            int page = 0;
            int size = 10000;

            List<TransactionPromotionDTO> allTransactions = new ArrayList<>();
            if (merchantId != null && !merchantId.isEmpty()) {
                while (true) {
                    Map<String, Object> conditions = new HashMap<>();
                    conditions.put("fromDate", fromDate);
                    conditions.put("toDate", toDate);
                    conditions.put("merchantId", merchantId);
                    conditions.put("transactionStatus", transactionStatus);
                    conditions.put("transactionType", transactionType);
                    conditions.put("page", page);
                    conditions.put("pageSize", size);
                    conditions.put("sortOrder", sortOrder);
                    conditions.put("transId", transId);
                    conditions.put("paymentMethod", paymentMethod);
                    conditions.put("merchantTransRef", merchantTransRef);
                    conditions.put("cardNumber", cardNumber);
                    conditions.put("authCode", authCode);
                    conditions.put("promoCode", promoCode);
                    conditions.put("promoName", promoName);
                    conditions.put("Promotion", "Promotion");
                    conditions.put("searchPromotion", "1");
                    conditions.put("type", IConstants.TYPE_PROMOTION);
                    String opsQuery = OpenSearchQueryBuilder.buildQuery(conditions);
                    String opsStrData = repository.getTransactionList(opsQuery);

                    JsonNode root = objectMapper.readTree(opsStrData);
                    JsonNode hits = root.path("hits").path("hits");

                    if (!hits.isArray() || hits.size() == 0)
                        break;

                    for (JsonNode hit : hits) {
                        TransactionPromotionDTO dto = mapJsonToTransactionPromotionDTO(hit);
                        if (dto != null) {
                            allTransactions.add(dto);
                        }
                    }

                    if (hits.size() < size)
                        break;
                    page++;
                }
                logger.info("Fetched total {} transactions", allTransactions.size());

            }
            try (
                    InputStream is = getClass().getClassLoader()
                            .getResourceAsStream("template/PromotionTemplate.xlsx");
                    Workbook workbook = new XSSFWorkbook(is);
                    FileOutputStream fos = new FileOutputStream(outputPath)) {
                if (is == null)
                    throw new FileNotFoundException("Template not found in resources/template/");

                Sheet sheet = workbook.getSheetAt(0);
                int startRow = 4;

                ZonedDateTime zFrom = Instant.parse(fromDate).atZone(ZoneId.systemDefault());
                ZonedDateTime zTo = Instant.parse(toDate).atZone(ZoneId.systemDefault());
                // Định dạng theo "yyyy-MM-dd HH:mm:ss"
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

                String fromDateTime = zFrom.format(formatter);
                String toDateTime = zTo.format(formatter);
                Row rowDate = sheet.createRow(1);
                rowDate.createCell(7).setCellValue("From: " + fromDateTime);
                rowDate.createCell(9).setCellValue("To: " + toDateTime);
                logger.info("Writing {} rows to Excel file", allTransactions.size());

                for (int i = 0; i < allTransactions.size(); i++) {
                    TransactionPromotionDTO dto = allTransactions.get(i);
                    Row row = sheet.createRow(startRow + i);
                    row.createCell(0).setCellValue(i + 1);
                    row.createCell(1).setCellValue(dto.getMerchantId());
                    row.createCell(2).setCellValue(dto.getTransactionId());
                    row.createCell(3).setCellValue(dto.getOrderReference());
                    row.createCell(4).setCellValue(dto.getOrgAmount().toString());
                    row.createCell(5).setCellValue(dto.getPaymentAmount().toString());
                    row.createCell(6).setCellValue(dto.getCurrency());
                    row.createCell(7).setCellValue(dto.getPromotionCode());
                    row.createCell(8).setCellValue(dto.getPromotionName());
                    row.createCell(9).setCellValue(dto.getPaymentMethod());
                    row.createCell(10).setCellValue(dto.getCardNumber());
                    row.createCell(11).setCellValue(dto.getCreatedDate().replaceAll("T", " "));
                    row.createCell(12).setCellValue(dto.getTransactionType());
                    row.createCell(13).setCellValue(dto.getTransactionStatus());
                    row.createCell(14).setCellValue(dto.getResponseDescription());
                }

                workbook.write(fos);
            }

            // Tạo FileDownloadDto trả về
            File file = new File(outputPath);
            FileDownloadDto dto = new FileDownloadDto();
            dto.setFile_hash_name(task.getFileHashName());
            dto.setFile_name(task.getFileName());
            dto.setFile_path(outputPath);
            dto.setFile_size(file.length());
            dto.setUser(task.getUserId());
            logger.info("Exported {} transactions to {}", allTransactions.size(), outputPath);
            return dto;
        } catch (Exception e) {
            logger.error("Error while exporting Excel for task {}: {}", task.getId(), e.getMessage(), e);
            throw new RuntimeException("Export Excel failed", e);
        }
    }

    private TransactionPromotionDTO mapJsonToTransactionPromotionDTO(JsonNode hit) {
        try {
            JsonNode source = hit.path("_source");
            if (source.isMissingNode())
                return null;

            JsonNode merchantNode = source.path("msp_merchant");
            JsonNode enrichTxnNode = source.path("enrich_txn");
            JsonNode paymentNode = source.path("msp_payment");
            JsonNode invoiceNode = source.path("msp_invoice");
            JsonNode promotion = source.path("onepr_pr");
            String docId = safeText(hit, "_id");
            if (docId == null || docId.isEmpty())
                return null;

            TransactionPromotionDTO dto = TransactionPromotionDTO.builder()
                    .docId(docId)
                    .merchantId(safeText(merchantNode, "s_id"))
                    .baseId(safeText(enrichTxnNode, "s_base_id"))
                    .transactionId(safeText(enrichTxnNode, "s_id"))
                    .orderReference(safeText(invoiceNode, "s_info"))
                    .cardNumber(safeText(paymentNode, "s_e_card_number"))
                    .transactionType(safeText(enrichTxnNode, "s_txn_type"))
                    .responseCode(safeText(enrichTxnNode, "s_response_code"))
                    .responseDescription(safeText(enrichTxnNode, "s_e_response_code"))
                    .transactionStatus(safeText(enrichTxnNode, "s_state"))
                    .currency(safeText(invoiceNode, "s_currencies"))
                    .promotionCode(safeText(promotion, "s_id"))
                    .promotionName(safeText(promotion, "s_name"))
                    .paymentMethod(safeText(paymentNode, "s_e_pay_method"))
                    .build();

            // amount
            String amountStr = safeText(enrichTxnNode, "n_original_amount");
            if (amountStr != null)
                dto.setOrgAmount(new BigDecimal(amountStr));

            String paymentAmount = safeText(paymentNode, "n_amount");
            if (paymentAmount != null)
                dto.setPaymentAmount(new BigDecimal(paymentAmount));

            // datetime
            dto.setCreatedDate(DateValidatorUtil.convertDate((safeText(enrichTxnNode, "d_create"))));

            if (dto.getPromotionCode() == null)
                dto.setPromotionCode(safeText(paymentNode, "s_pr_id"));
            return dto;
        } catch (Exception e) {
            logger.warn("Error mapping TransactionPromotionDTO: ", e);
            return null;
        }
    }

    private String safeText(JsonNode node, String... path) {
        for (String p : path) {
            if (node == null)
                return null;
            node = node.path(p);
        }
        return node.isMissingNode() ? null : node.asText(null);
    }

    private List<String> parseCsvToList(String csv) {
        if (!StringUtils.hasText(csv))
            return List.of();
        return Arrays.stream(csv.split(","))
                .map(String::trim)
                .filter(StringUtils::hasText)
                .toList();
    }

    @Override
    public FileDownloadDto createReportExcel(DownloadTaskEntity task) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            logger.info("Start exporting Excel for taskId={}, fileName={}", task.getId(), task.getFileName());

            // Tạo thư mục lưu file nếu chưa có
            String baseDir = exportFileProperties.getBaseDir();
            File dir = new File(baseDir);
            if (!dir.exists())
                dir.mkdirs();

            String outputPath = baseDir + task.getFileHashName();
            // Parse requestParams từ task
            Map<String, Object> params = objectMapper.readValue(task.getRequestParams(), new TypeReference<>() {
            });
            String fromDate = normalizeDate((String) params.get("fromDate"));
            String toDate = normalizeDate((String) params.get("toDate"));
            String merchantId = (String) params.get("merchantId");
            String paymentMethod = (String) params.get("paymentMethod");
            String interval = (String) params.get("interval");
            String currency = (String) params.get("currency");
            String promotionCode = (String) params.get("promotionCode");
            String groupBy = switch (interval.toUpperCase()) {
                case "DAILY" -> "d_transaction_day";
                case "WEEKLY" -> "TO_CHAR(d_transaction_day, 'IYYY-\"W\"IW')";
                case "MONTHLY" -> "TO_CHAR(d_transaction_day, 'YYYY-MM')";
                default -> throw new IllegalArgumentException("Invalid interval: " + interval);
            };
            List<String> merchantIds = parseCsvToList((String) params.get("merchantId"));
            List<PromotionReportResponse> allTransactions = new ArrayList<>();
            if (merchantId != null && !merchantId.isEmpty()) {
                StringBuilder sqlBuilder = buildQuerySelectReport(promotionCode, paymentMethod, groupBy);
                DatabaseClient.GenericExecuteSpec spec = databaseClient.sql(sqlBuilder.toString())
                        .bind("fromDate", LocalDate.parse(fromDate))
                        .bind("toDate", LocalDate.parse(toDate))
                        .bind("currency", currency)
                        .bind("merchantIds", merchantIds);
                if (promotionCode != null && !promotionCode.isBlank()) {
                    spec = spec.bind("promotionCode", promotionCode);
                }
                if (paymentMethod != null && !paymentMethod.isBlank()) {
                    spec = spec.bind("paymentMethod", paymentMethod);
                }
                allTransactions = spec
                        .map((row, meta) -> {
                            return new PromotionReportResponse(
                                    row.get("merchant_id", String.class),
                                    row.get("date", String.class),
                                    row.get("cardType", String.class),
                                    row.get("currency", String.class),
                                    row.get("num_purchase_trans", Long.class),
                                    row.get("num_refund_void_trans", Long.class),
                                    row.get("total_amount", BigDecimal.class),
                                    row.get("total_original_purchase", BigDecimal.class),
                                    row.get("total_refund", BigDecimal.class));
                        })
                        .all()
                        .collectList().block();
                try (InputStream is = getClass().getClassLoader()
                        .getResourceAsStream("template/PromotionReportTemplate.xlsx");
                        Workbook workbook = new XSSFWorkbook(is);
                        FileOutputStream fos = new FileOutputStream(outputPath)) {
                    if (is == null)
                        throw new FileNotFoundException("Template not found in resources/template/");
                    Sheet sheet = workbook.getSheetAt(0);
                    int startRow = 4;

                    Row rowDate = sheet.createRow(1);
                    rowDate.createCell(3).setCellValue("From: " + fromDate);
                    rowDate.createCell(5).setCellValue("To: " + toDate);

                    for (int i = 0; i < allTransactions.size(); i++) {
                        PromotionReportResponse dto = allTransactions.get(i);
                        Row row = sheet.getRow(startRow + i);
                        if (row == null) {
                            row = sheet.createRow(startRow + i);
                        }
                        row.getCell(0).setCellValue(i + 1);
                        row.getCell(1).setCellValue(dto.getDate());
                        row.getCell(2).setCellValue(dto.getMerchantId());
                        row.getCell(3).setCellValue(dto.getCardType());
                        row.getCell(4).setCellValue(dto.getNumPurchaseTrans());
                        row.getCell(5).setCellValue(dto.getNumRefundVoidTrans());
                        row.getCell(6).setCellValue(dto.getTotalAmount().toString());
                        row.getCell(7).setCellValue(dto.getTotalOriginalPurchase().toString());
                        row.getCell(8).setCellValue(dto.getTotalRefund().toString());
                        row.getCell(9).setCellValue(currency);
                    }
                    workbook.write(fos);
                }
            }
           // Tạo FileDownloadDto trả về
           File file = new File(outputPath);
           FileDownloadDto dto = new FileDownloadDto();
           dto.setFile_hash_name(task.getFileHashName());
           dto.setFile_name(task.getFileName());
           dto.setFile_path(outputPath);
           dto.setFile_size(file.length());
           logger.info("Exported {} transactions to {}", allTransactions.size(), outputPath);
           return dto;

        } catch (Exception e) {
            logger.error("Error while exporting Excel for task {}: {}", task.getId(), e.getMessage(), e);
            throw new RuntimeException("Export Excel failed", e);
        }
    }
}
