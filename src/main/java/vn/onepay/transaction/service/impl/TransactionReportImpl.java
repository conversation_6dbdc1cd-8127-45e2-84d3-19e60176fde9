package vn.onepay.transaction.service.impl;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.r2dbc.core.DatabaseClient;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ServerWebExchange;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import reactor.core.publisher.Mono;
import vn.onepay.transaction.constant.IConstants;
import vn.onepay.transaction.dto.PagedResponse;
import vn.onepay.transaction.dto.TransactionReportDTO;
import vn.onepay.transaction.dto.TransactionReportResponse;
import vn.onepay.transaction.entity.DownloadTaskEntity;
import vn.onepay.transaction.entity.TransactionReportEntity;
import vn.onepay.transaction.opensearch.OpenSearchQueryBuilder;
import vn.onepay.transaction.service.MerchantService;
import vn.onepay.transaction.service.TransactionReportService;
import vn.onepay.transaction.repository.TransactionOpenSearchRepository;

@Service
public class TransactionReportImpl implements TransactionReportService {
    private static final Logger logger = LoggerFactory.getLogger(TransactionReportImpl.class);
    @Autowired
    private DatabaseClient databaseClient;

    @Autowired
    private TransactionOpenSearchRepository repository;

    @Autowired
    private MerchantService merchantService;

    @Override
    public Mono<TransactionReportResponse> getTransactionReports(ServerWebExchange exchange, String from, String to,
            String interval, String merchantId, String currency, int page, int size) {
        String fromDate = normalizeDate(from);
        String toDate = normalizeDate(to);
        String groupBy = switch (interval.toUpperCase()) {
            case "DAILY" -> "d_transaction_day";
            case "WEEKLY" -> "TO_CHAR(d_transaction_day, 'IYYY-\"W\"IW')";
            case "MONTHLY" -> "TO_CHAR(d_transaction_day, 'YYYY-MM')";
            default -> throw new IllegalArgumentException("Invalid interval: " + interval);
        };
        return merchantService.resolveMerchantIdFilter(exchange, merchantId)
                .flatMap(merchantIds -> {

                    // Build SQL with dynamic IN clause if needed
                    StringBuilder sqlBuilder = new StringBuilder(
                            """
                                        SELECT
                                          %s AS date,
                                          merchant_id,
                                          SUM(CASE WHEN transaction_type = 'Purchase' THEN total_trans ELSE 0 END) AS num_purchase_trans,
                                          SUM(CASE WHEN transaction_type = 'Authorize' THEN total_trans ELSE 0 END) AS num_authorize_trans,
                                          SUM(CASE WHEN transaction_type = 'Capture' THEN total_trans ELSE 0 END) AS num_capture_trans,
                                          SUM(CASE WHEN transaction_type = 'Refund' THEN total_trans ELSE 0 END) AS num_refund_trans,
                                          SUM(CASE
                                                WHEN transaction_type = 'Purchase' THEN amount
                                                WHEN transaction_type = 'Void Purchase' THEN -amount
                                                WHEN transaction_type = 'Capture' THEN amount
                                                WHEN transaction_type = 'Void Capture' THEN -amount
                                                WHEN transaction_type = 'Refund' THEN -amount
                                                ELSE 0
                                          END) AS total_revenue,
                                          SUM(CASE
                                                WHEN transaction_type = 'Refund' THEN amount
                                                WHEN transaction_type = 'Void Refund' THEN -amount
                                                WHEN transaction_type = 'Refund Capture' THEN amount
                                                WHEN transaction_type = 'Void Refund Capture' THEN -amount
                                                WHEN transaction_type = 'Refund Dispute' THEN amount
                                                ELSE 0
                                          END) AS total_refund
                                        FROM transaction_report
                                        WHERE d_transaction_day BETWEEN :fromDate AND :toDate
                                    """
                                    .formatted(groupBy));

                    // Add IN clause if merchantIds not empty
                    if (!merchantIds.isEmpty()) {
                        sqlBuilder.append(" AND merchant_id IN (");
                        for (int i = 0; i < merchantIds.size(); i++) {
                            sqlBuilder.append(":merchant").append(i);
                            if (i < merchantIds.size() - 1)
                                sqlBuilder.append(", ");
                        }
                        sqlBuilder.append(")");
                    }

                    sqlBuilder.append("""
                                AND (:currency IS NULL OR currency = :currency)
                                GROUP BY %s, merchant_id
                                ORDER BY %s DESC
                                LIMIT :limit OFFSET :offset
                            """.formatted(groupBy, groupBy));

                    DatabaseClient.GenericExecuteSpec spec = databaseClient.sql(sqlBuilder.toString())
                            .bind("fromDate", LocalDate.parse(fromDate))
                            .bind("toDate", LocalDate.parse(toDate))
                            .bind("currency", currency)
                            .bind("limit", size)
                            .bind("offset", page * size);

                    // Bind merchant IDs dynamically
                    for (int i = 0; i < merchantIds.size(); i++) {
                        spec = spec.bind("merchant" + i, merchantIds.get(i));
                    }

                    Mono<List<TransactionReportDTO>> resultsMono = spec
                            .map((row, meta) -> new TransactionReportDTO(
                                    row.get("merchant_id", String.class),
                                    row.get("date", String.class),
                                    row.get("num_purchase_trans", Long.class),
                                    row.get("num_authorize_trans", Long.class),
                                    row.get("num_capture_trans", Long.class),
                                    row.get("num_refund_trans", Long.class),
                                    row.get("total_revenue", BigDecimal.class),
                                    row.get("total_refund", BigDecimal.class)))
                            .all()
                            .collectList();

                    Mono<Long> countMono = buildCountQuery(fromDate, toDate, merchantIds, currency, groupBy);

                    return Mono.zip(resultsMono, countMono)
                            .map(tuple -> {
                                List<TransactionReportDTO> items = tuple.getT1();
                                Long total = tuple.getT2();

                                // Tính tổng các trường
                                long totalPurchase = 0, totalAuthorize = 0, totalCapture = 0, totalRefund = 0;
                                BigDecimal totalRevenue = BigDecimal.ZERO;
                                BigDecimal totalRefundAmount = BigDecimal.ZERO;

                                for (TransactionReportDTO dto : items) {
                                    totalPurchase += Optional.ofNullable(dto.getNumPurchaseTrans()).orElse(0L);
                                    totalAuthorize += Optional.ofNullable(dto.getNumAuthorizeTrans()).orElse(0L);
                                    totalCapture += Optional.ofNullable(dto.getNumCaptureTrans()).orElse(0L);
                                    totalRefund += Optional.ofNullable(dto.getNumRefundTrans()).orElse(0L);
                                    totalRevenue = totalRevenue
                                            .add(Optional.ofNullable(dto.getTotalRevenue()).orElse(BigDecimal.ZERO));
                                    totalRefundAmount = totalRefundAmount
                                            .add(Optional.ofNullable(dto.getTotalRefund()).orElse(BigDecimal.ZERO));
                                }

                                // Tạo bản ghi tổng
                                String summaryDate = fromDate + " - " + toDate;
                                TransactionReportDTO totalDto = new TransactionReportDTO(
                                        null,
                                        summaryDate,
                                        totalPurchase,
                                        totalAuthorize,
                                        totalCapture,
                                        totalRefund,
                                        totalRevenue,
                                        totalRefundAmount);

                                int totalPages = (int) Math.ceil((double) total / size);
                                PagedResponse<TransactionReportDTO> pagedResponse = PagedResponse
                                        .<TransactionReportDTO>builder()
                                        .currentPage(page)
                                        .pageSize(size)
                                        .totalPages(totalPages)
                                        .items(items)
                                        .total(total)
                                        .build();

                                // Trả về cả paged data và summary
                                return new TransactionReportResponse(pagedResponse, totalDto);
                            });
                });
    }

    private Mono<Long> buildCountQuery(String fromDate, String toDate, List<String> merchantIds, String currency,
            String groupBy) {
        StringBuilder sqlBuilder = new StringBuilder("""
                    SELECT COUNT(*) AS cnt FROM (
                        SELECT 1 FROM transaction_report
                        WHERE d_transaction_day BETWEEN :fromDate AND :toDate
                """);

        if (!merchantIds.isEmpty()) {
            sqlBuilder.append(" AND merchant_id IN (");
            for (int i = 0; i < merchantIds.size(); i++) {
                sqlBuilder.append(":merchant").append(i);
                if (i < merchantIds.size() - 1)
                    sqlBuilder.append(", ");
            }
            sqlBuilder.append(")");
        }

        sqlBuilder.append("""
                    AND (:currency IS NULL OR currency = :currency)
                    GROUP BY %s, merchant_id
                ) sub
                """.formatted(groupBy));

        DatabaseClient.GenericExecuteSpec spec = databaseClient.sql(sqlBuilder.toString())
                .bind("fromDate", LocalDate.parse(fromDate))
                .bind("toDate", LocalDate.parse(toDate))
                .bind("currency", currency);

        for (int i = 0; i < merchantIds.size(); i++) {
            spec = spec.bind("merchant" + i, merchantIds.get(i));
        }

        return spec.map((row, meta) -> row.get("cnt", Long.class)).one().defaultIfEmpty(0L);
    }

    @Override
    public Mono<Void> insertDailyTransactionReport() {
        String baseQuery = IConstants.baseConditionJson;
        logger.info("Starting insertDailyTransactionReport...");

        return fetchTransactionReportFromOpenSearch(baseQuery, null)
                .flatMap(transactionReports -> {
                    logger.info("Fetched {} reports from the first OpenSearch request", transactionReports.size());
                    return insertReportsIntoDatabase(transactionReports)
                            .then(fetchNextPageReports(baseQuery, transactionReports));
                })
                .doOnError(e -> logger.error("Error occurred during insertDailyTransactionReport", e))
                .then();
    }

    private Mono<List<TransactionReportEntity>> fetchTransactionReportFromOpenSearch(String query, String afterKey) {
        return Mono.fromCallable(() -> {
            try {
                logger.info("Sending OpenSearch request{}", afterKey != null ? " with afterKey: " + afterKey : "");
                String response = repository.getTransactionReport(query);
                logger.info("Received response from OpenSearch");
                return convertToDTO(response);
            } catch (Exception e) {
                logger.error("Failed to fetch transaction report from OpenSearch", e);
                throw new RuntimeException("Failed to fetch transaction report from OpenSearch", e);
            }
        });
    }

    private String getAfterKeyFromReports(List<TransactionReportEntity> reports) {
        if (reports.isEmpty())
            return null;

        TransactionReportEntity last = reports.get(reports.size() - 1);
        Map<String, Object> afterKey = new LinkedHashMap<>();
        afterKey.put("transactionDay",
                last.getDTransactionDay().atStartOfDay(ZoneId.of("Asia/Ho_Chi_Minh")).toInstant().toEpochMilli());
        afterKey.put("merchantId", last.getMerchantId());
        afterKey.put("currency", last.getCurrency());
        afterKey.put("txnType", last.getTransactionType());

        try {
            String afterKeyStr = new ObjectMapper().writeValueAsString(afterKey);
            logger.info("Generated afterKey from last report: {}", afterKeyStr);
            return afterKeyStr;
        } catch (JsonProcessingException e) {
            logger.error("Failed to serialize afterKey", e);
            throw new RuntimeException("Failed to serialize afterKey", e);
        }
    }

    private Mono<Void> fetchNextPageReports(String baseQuery, List<TransactionReportEntity> previousReports) {
        String afterKeyJson = getAfterKeyFromReports(previousReports);
        if (afterKeyJson != null) {
            try {
                JsonNode afterKeyNode = new ObjectMapper().readTree(afterKeyJson);
                String queryWithAfterKey = OpenSearchQueryBuilder.buildQueryWithAfterKey(baseQuery, afterKeyNode);
                logger.info("Built query with afterKey: {}", afterKeyJson);

                return fetchTransactionReportFromOpenSearch(queryWithAfterKey, afterKeyJson)
                        .flatMap(nextReports -> {
                            logger.info("Fetched {} reports in next page", nextReports.size());
                            return insertReportsIntoDatabase(nextReports)
                                    .then(fetchNextPageReports(baseQuery, nextReports));
                        });
            } catch (Exception e) {
                logger.error("Failed to parse afterKey JSON: {}", afterKeyJson, e);
                throw new RuntimeException("Failed to parse afterKey JSON: " + afterKeyJson, e);
            }
        } else {
            logger.info("No afterKey found. All pages fetched.");
            return Mono.empty();
        }
    }

    private List<TransactionReportEntity> convertToDTO(String response) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            JsonNode rootNode = mapper.readTree(response);
            JsonNode buckets = rootNode.path("aggregations").path("byComposite").path("buckets");
            List<TransactionReportEntity> reports = new ArrayList<>();
            LocalDate yesterday = LocalDate.now().minusDays(1);

            for (JsonNode bucket : buckets) {
                TransactionReportEntity entity = new TransactionReportEntity();
                entity.setMerchantId(bucket.path("key").path("merchantId").asText());
                entity.setDTransactionDay(yesterday);
                entity.setTotalTrans(bucket.path("doc_count").asLong());
                entity.setCurrency(bucket.path("key").path("currency").asText());
                entity.setTransactionType(bucket.path("key").path("txnType").asText());

                JsonNode totalVolumeNode = bucket.path("totalVolume").path("value");
                Long amount = totalVolumeNode.isNull() ? 0L : totalVolumeNode.asLong();
                entity.setAmount(BigDecimal.valueOf(amount));

                reports.add(entity);
            }

            logger.info("Converted OpenSearch response into {} report entities", reports.size());
            return reports;
        } catch (Exception e) {
            logger.error("Error converting OpenSearch response to DTO", e);
            throw new RuntimeException("Error converting OpenSearch response to DTO", e);
        }
    }

    public Mono<Void> insertReportsIntoDatabase(List<TransactionReportEntity> reports) {
        if (reports.isEmpty()) {
            logger.info("No reports to insert into database");
            return Mono.empty();
        }

        logger.info("Inserting {} reports into the database", reports.size());
        return Mono.when(
                reports.stream()
                        .map(this::upsertReport)
                        .toArray(Mono[]::new))
                .doOnError(e -> logger.error("Error occurred during database insertion", e))
                .then();
    }

    private Mono<Void> upsertReport(TransactionReportEntity report) {
        logger.debug("Upserting report: {}", report);

        String upsertSql = "INSERT INTO transaction_report (d_transaction_day, merchant_id, transaction_type, currency, amount, created_date, updated_date, total_trans) "
                + "VALUES (:d_transaction_day, :merchant_id, :transaction_type, :currency, :amount, :created_date, :updated_date, :total_trans) "
                + "ON CONFLICT (d_transaction_day, merchant_id, transaction_type, currency) "
                + "DO UPDATE SET amount = :amount, updated_date = :updated_date, total_trans = :total_trans";

        return databaseClient.sql(upsertSql)
                .bind("d_transaction_day", report.getDTransactionDay())
                .bind("merchant_id", report.getMerchantId())
                .bind("transaction_type", report.getTransactionType())
                .bind("currency", report.getCurrency())
                .bind("amount", report.getAmount())
                .bind("created_date", LocalDateTime.now())
                .bind("updated_date", LocalDateTime.now())
                .bind("total_trans", report.getTotalTrans())
                .then()
                .doOnError(e -> logger.error("Error upserting report: {}", report, e)); // now valid
    }

    public static String normalizeDate(String fromDate) {
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        try {
            LocalDate date = LocalDate.parse(fromDate, inputFormatter);
            return outputFormatter.format(date);
        } catch (DateTimeParseException e) {
            // Không đúng định dạng dd-MM-yyyy => giữ nguyên
            return fromDate;
        }
    }

    @Override
    public String createExcel(DownloadTaskEntity task) {
        return "done";
    }

}
