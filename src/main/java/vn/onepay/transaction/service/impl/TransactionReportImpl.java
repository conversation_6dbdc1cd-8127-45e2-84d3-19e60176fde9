package vn.onepay.transaction.service.impl;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.r2dbc.core.DatabaseClient;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import vn.onepay.transaction.config.ExportFileProperties;
import vn.onepay.transaction.constant.IConstants;
import vn.onepay.transaction.dto.FileDownloadDto;
import vn.onepay.transaction.dto.PagedResponse;
import vn.onepay.transaction.dto.TransactionReportDTO;
import vn.onepay.transaction.dto.ReportResponse;
import vn.onepay.transaction.entity.DownloadTaskEntity;
import vn.onepay.transaction.entity.TransactionReportEntity;
import vn.onepay.transaction.opensearch.OpenSearchQueryBuilder;
import vn.onepay.transaction.service.MerchantService;
import vn.onepay.transaction.service.TransactionReportService;
import vn.onepay.transaction.repository.TransactionOpenSearchRepository;

@Service
public class TransactionReportImpl implements TransactionReportService {
    private static final Logger logger = LoggerFactory.getLogger(TransactionReportImpl.class);
    private static final int PAGE_SIZE = 10000;
    @Autowired
    private DatabaseClient databaseClient;

    @Autowired
    private TransactionOpenSearchRepository repository;

    @Autowired
    private MerchantService merchantService;
    @Autowired
    private ExportFileProperties exportFileProperties;

    @Override
    public Mono<ReportResponse<TransactionReportDTO>> getTransactionReports(String from,
            String to,
            String interval, String merchantId, String currency, int page, int size, Boolean isPromotion,
            String paymentMethod, String orderSource) {
        String fromDate = normalizeDate(from);
        String toDate = normalizeDate(to);
        String groupBy = switch (interval.toUpperCase()) {
            case "DAILY" -> "d_transaction_day";
            case "WEEKLY" -> "TO_CHAR(d_transaction_day, 'IYYY-\"W\"IW')";
            case "MONTHLY" -> "TO_CHAR(d_transaction_day, 'YYYY-MM')";
            default -> throw new IllegalArgumentException("Invalid interval: " + interval);
        };
        return merchantService.resolveMerchantIdFilter(merchantId)
                .flatMap(merchantIds -> {
                    if (merchantIds.isEmpty()) {
                        logger.warn("No merchantId matched -> returning empty response");
                        PagedResponse<TransactionReportDTO> emptyPaged = PagedResponse
                                .<TransactionReportDTO>builder()
                                .currentPage(page)
                                .pageSize(size)
                                .totalPages(0)
                                .items(Collections.emptyList())
                                .total(0L)
                                .build();
                        return Mono.just(new ReportResponse<>(emptyPaged, new TransactionReportDTO()));
                    }
                    List<String> paymentMethods = parseCsvToList(paymentMethod);
                    List<String> orderSources = parseCsvToList(orderSource);

                    // Build SQL with dynamic IN clause if needed
                    StringBuilder sqlBuilder = buildQuery(groupBy, paymentMethods, orderSources, isPromotion);
                    sqlBuilder.append(" LIMIT :limit OFFSET :offset");
                    DatabaseClient.GenericExecuteSpec spec = databaseClient.sql(sqlBuilder.toString())
                            .bind("fromDate", LocalDate.parse(fromDate))
                            .bind("toDate", LocalDate.parse(toDate))
                            .bind("currency", currency)
                            .bind("limit", size)
                            .bind("offset", page * size)
                            .bind("merchantIds", merchantIds);
                    if (paymentMethods != null && !paymentMethods.isEmpty()) {
                        spec = spec.bind("paymentMethods", paymentMethods);
                    }

                    if (orderSources != null && !orderSources.isEmpty()) {
                        spec = spec.bind("orderSources", orderSources);
                    }

                    if (isPromotion != null) {
                        spec = spec.bind("isPromotion", isPromotion);
                    }

                    logger.debug("Executing query with SQL: {}", sqlBuilder);

                    Mono<List<TransactionReportDTO>> resultsMono = spec
                            .map((row, meta) -> new TransactionReportDTO(
                                    row.get("merchant_id", String.class),
                                    row.get("date", String.class),
                                    row.get("num_purchase_trans", Long.class),
                                    row.get("num_authorize_trans", Long.class),
                                    row.get("num_capture_trans", Long.class),
                                    row.get("num_refund_trans", Long.class),
                                    row.get("total_revenue", BigDecimal.class),
                                    row.get("total_refund", BigDecimal.class)))
                            .all()
                            .collectList();

                    Mono<Long> countMono = buildCountQuery(fromDate, toDate, merchantIds, currency, groupBy,
                            isPromotion, paymentMethod, orderSource);

                    return Mono.zip(resultsMono, countMono)
                            .map(tuple -> {
                                List<TransactionReportDTO> items = tuple.getT1();
                                Long total = tuple.getT2();

                                // Tính tổng các trường
                                long totalPurchase = 0, totalAuthorize = 0, totalCapture = 0, totalRefund = 0;
                                BigDecimal totalRevenue = BigDecimal.ZERO;
                                BigDecimal totalRefundAmount = BigDecimal.ZERO;

                                for (TransactionReportDTO dto : items) {
                                    totalPurchase += Optional.ofNullable(dto.getNumPurchaseTrans()).orElse(0L);
                                    totalAuthorize += Optional.ofNullable(dto.getNumAuthorizeTrans()).orElse(0L);
                                    totalCapture += Optional.ofNullable(dto.getNumCaptureTrans()).orElse(0L);
                                    totalRefund += Optional.ofNullable(dto.getNumRefundTrans()).orElse(0L);
                                    totalRevenue = totalRevenue
                                            .add(Optional.ofNullable(dto.getTotalRevenue()).orElse(BigDecimal.ZERO));
                                    totalRefundAmount = totalRefundAmount
                                            .add(Optional.ofNullable(dto.getTotalRefund()).orElse(BigDecimal.ZERO));
                                }

                                // Tạo bản ghi tổng
                                String summaryDate = fromDate + " - " + toDate;
                                TransactionReportDTO totalDto = new TransactionReportDTO(
                                        null,
                                        summaryDate,
                                        totalPurchase,
                                        totalAuthorize,
                                        totalCapture,
                                        totalRefund,
                                        totalRevenue,
                                        totalRefundAmount);

                                int totalPages = (int) Math.ceil((double) total / size);
                                PagedResponse<TransactionReportDTO> pagedResponse = PagedResponse
                                        .<TransactionReportDTO>builder()
                                        .currentPage(page)
                                        .pageSize(size)
                                        .totalPages(totalPages)
                                        .items(items)
                                        .total(total)
                                        .build();

                                // Trả về cả paged data và summary
                                return new ReportResponse<TransactionReportDTO>(pagedResponse, totalDto);
                            });
                });
    }

    private Mono<Long> buildCountQuery(String fromDate, String toDate, List<String> merchantIds, String currency,
            String groupBy, Boolean isPromotion, String paymentMethod, String orderSource) {
        StringBuilder sqlBuilder = new StringBuilder("""
                    SELECT COUNT(*) AS cnt FROM (
                        SELECT 1 FROM transaction_report
                        WHERE d_transaction_day BETWEEN :fromDate AND :toDate
                """);

        if (!merchantIds.isEmpty()) {
            sqlBuilder.append(" AND merchant_id IN (");
            for (int i = 0; i < merchantIds.size(); i++) {
                sqlBuilder.append(":merchant").append(i);
                if (i < merchantIds.size() - 1)
                    sqlBuilder.append(", ");
            }
            sqlBuilder.append(")");
        }
        // Thêm filter nếu có
        List<String> paymentMethods = parseCsvToList(paymentMethod);
        List<String> orderSources = parseCsvToList(orderSource);
        if (!paymentMethods.isEmpty()) {
            sqlBuilder.append(" AND payment_method IN (:paymentMethods)");
        }

        if (!orderSources.isEmpty()) {
            sqlBuilder.append(" AND order_source IN (:orderSources)");
        }

        if (isPromotion != null) {
            sqlBuilder.append(" AND is_promotion = :isPromotion");
        }
        sqlBuilder.append("""
                    AND (:currency IS NULL OR currency = :currency)
                    GROUP BY %s, merchant_id
                ) sub
                """.formatted(groupBy));

        DatabaseClient.GenericExecuteSpec spec = databaseClient.sql(sqlBuilder.toString())
                .bind("fromDate", LocalDate.parse(fromDate))
                .bind("toDate", LocalDate.parse(toDate))
                .bind("currency", currency);

        for (int i = 0; i < merchantIds.size(); i++) {
            spec = spec.bind("merchant" + i, merchantIds.get(i));
        }
        if (paymentMethods != null && !paymentMethods.isEmpty()) {
            spec = spec.bind("paymentMethods", paymentMethods);
        }

        if (orderSources != null && !orderSources.isEmpty()) {
            spec = spec.bind("orderSources", orderSources);
        }

        if (isPromotion != null) {
            spec = spec.bind("isPromotion", isPromotion);
        }
        return spec.map((row, meta) -> row.get("cnt", Long.class)).one().defaultIfEmpty(0L);
    }

    @Override
    public Mono<Void> insertDailyTransactionReport(String fromDateStr, String toDateStr) {
        LocalDateTime fromDate = LocalDate.parse(fromDateStr).atStartOfDay();
        LocalDateTime toDate = LocalDate.parse(toDateStr).atTime(LocalTime.MAX);
        logger.info("Starting insertDailyTransactionReport from {} to {}", fromDate, toDate);

        DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME;
        String from = fromDate.format(formatter);
        String to = toDate.format(formatter);
        String baseQuery = IConstants.baseConditionJson.formatted(from, to);

        return fetchTransactionReportFromOpenSearch(baseQuery, null)
                .flatMap(transactionReports -> {
                    logger.info("Fetched {} reports from the first OpenSearch request", transactionReports.size());
                    return insertReportsIntoDatabase(transactionReports)
                            .then(fetchNextPageReports(baseQuery, transactionReports));
                })
                .doOnError(e -> logger.error("Error occurred during insertDailyTransactionReport", e))
                .then();
    }

    private Mono<List<TransactionReportEntity>> fetchTransactionReportFromOpenSearch(String query, String afterKey) {
        return Mono.fromCallable(() -> {
            try {
                logger.info("Sending OpenSearch request{}", afterKey != null ? " with afterKey: " + afterKey : "");
                String response = repository.getTransactionReport(query);
                logger.info("Received response from OpenSearch");
                return convertToDTO(response);
            } catch (Exception e) {
                logger.error("Failed to fetch transaction report from OpenSearch", e);
                throw new RuntimeException("Failed to fetch transaction report from OpenSearch", e);
            }
        });
    }

    private String getAfterKeyFromReports(List<TransactionReportEntity> reports) {
        if (reports.isEmpty())
            return null;

        TransactionReportEntity last = reports.get(reports.size() - 1);
        Map<String, Object> afterKey = new LinkedHashMap<>();
        afterKey.put("transactionDay",
                last.getDTransactionDay().atStartOfDay(ZoneId.of("Asia/Ho_Chi_Minh")).toInstant().toEpochMilli());
        afterKey.put("merchantId", last.getMerchantId());
        afterKey.put("currency", last.getCurrency());
        afterKey.put("txnType", last.getTransactionType());

        try {
            String afterKeyStr = new ObjectMapper().writeValueAsString(afterKey);
            logger.info("Generated afterKey from last report: {}", afterKeyStr);
            return afterKeyStr;
        } catch (JsonProcessingException e) {
            logger.error("Failed to serialize afterKey", e);
            throw new RuntimeException("Failed to serialize afterKey", e);
        }
    }

    private Mono<Void> fetchNextPageReports(String baseQuery, List<TransactionReportEntity> previousReports) {
        // Nếu size = PAGE_SIZE thì mới nghĩ đến fetch next page
        if (previousReports.size() == PAGE_SIZE) {
            String afterKeyJson = getAfterKeyFromReports(previousReports);
            if (afterKeyJson != null) {
                try {
                    JsonNode afterKeyNode = new ObjectMapper().readTree(afterKeyJson);
                    String queryWithAfterKey = OpenSearchQueryBuilder.buildQueryWithAfterKey(baseQuery, afterKeyNode);
                    logger.info("Built query with afterKey: {}", afterKeyJson);

                    return fetchTransactionReportFromOpenSearch(queryWithAfterKey, afterKeyJson)
                            .flatMap(nextReports -> {
                                logger.info("Fetched {} reports in next page", nextReports.size());
                                return insertReportsIntoDatabase(nextReports)
                                        .then(fetchNextPageReports(baseQuery, nextReports));
                            });
                } catch (Exception e) {
                    logger.error("Failed to parse afterKey JSON: {}", afterKeyJson, e);
                    throw new RuntimeException("Failed to parse afterKey JSON: " + afterKeyJson, e);
                }
            } else {
                logger.info("No afterKey found. All pages fetched.");
                return Mono.empty();
            }
        } else {
            logger.info("Less than {} reports fetched ({}). Assuming last page, stopping recursion.", PAGE_SIZE,
                    previousReports.size());
            return Mono.empty();
        }
    }

    private List<TransactionReportEntity> convertToDTO(String response) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            JsonNode rootNode = mapper.readTree(response);
            JsonNode buckets = rootNode.path("aggregations").path("byComposite").path("buckets");
            List<TransactionReportEntity> reports = new ArrayList<>();

            for (JsonNode bucket : buckets) {
                TransactionReportEntity entity = new TransactionReportEntity();
                entity.setMerchantId(bucket.path("key").path("merchantId").asText());

                long epochMillis = bucket.path("key").path("transactionDay").asLong();
                LocalDate transactionDay = Instant.ofEpochMilli(epochMillis)
                        .atZone(ZoneId.of("Asia/Ho_Chi_Minh")).toLocalDate();
                entity.setDTransactionDay(transactionDay);
                entity.setTotalTrans(bucket.path("doc_count").asLong());
                entity.setCurrency(bucket.path("key").path("currency").asText());
                entity.setTransactionType(bucket.path("key").path("txnType").asText());
                entity.setOrderSource(bucket.path("key").path("orderSource").asText());
                entity.setPaymentMethod(bucket.path("key").path("paymentMethod").asText());
                // is promotion
                Boolean isPromotion = null;
                JsonNode promoNode = bucket.path("key").path("isPromotion");
                if (promoNode.isBoolean()) {
                    isPromotion = promoNode.asBoolean();
                } else if (promoNode.isTextual()) {
                    isPromotion = "true".equalsIgnoreCase(promoNode.asText());
                }
                entity.setIsPromotion(isPromotion);

                JsonNode totalVolumeNode = bucket.path("totalVolume").path("value");
                Long amount = totalVolumeNode.isNull() ? 0L : totalVolumeNode.asLong();
                entity.setAmount(BigDecimal.valueOf(amount));

                reports.add(entity);
            }

            logger.info("Converted OpenSearch response into {} report entities", reports.size());
            return reports;
        } catch (Exception e) {
            logger.error("Error converting OpenSearch response to DTO", e);
            throw new RuntimeException("Error converting OpenSearch response to DTO", e);
        }
    }

    public Mono<Void> insertReportsIntoDatabase(List<TransactionReportEntity> reports) {
        if (reports.isEmpty()) {
            logger.info("No reports to insert into database");
            return Mono.empty();
        }

        logger.info("Inserting {} reports into the database", reports.size());
        // insert song song nếu nhiều connection
        // return Mono.when(
        // reports.stream()
        // .map(this::upsertReport)
        // .toArray(Mono[]::new))
        // .doOnError(e -> logger.error("Error occurred during database insertion", e))
        // .then();

        return Flux.fromIterable(reports)
                .flatMap(this::upsertReport, 10) // chỉ chạy tối đa 10 request đồng thời
                .then()
                .doOnSuccess(v -> logger.info("Insert reports completed successfully"))
                .then(databaseClient.sql("ANALYZE transaction_report").then() // Analyze toàn bảng để tránh lỗi sau
                                                                              // insert load lâu
                        .doOnError(e -> logger.error("Error during ANALYZE", e))
                        .onErrorResume(e -> Mono.empty()))
                .doOnError(e -> logger.error("Error occurred during database insertion", e));
    }

    private Mono<Void> upsertReport(TransactionReportEntity report) {
        logger.debug("Upserting report: {}", report);

        String upsertSql = "INSERT INTO transaction_report (d_transaction_day, merchant_id, transaction_type, currency, payment_method, order_source, is_promotion, amount, created_date, updated_date, total_trans) "
                + "VALUES (:d_transaction_day, :merchant_id, :transaction_type, :currency, :payment_method, :order_source, :is_promotion, :amount, :created_date, :updated_date, :total_trans) "
                + "ON CONFLICT (d_transaction_day, merchant_id, transaction_type, currency, payment_method, order_source, is_promotion) "
                + "DO UPDATE SET amount = :amount, updated_date = :updated_date, total_trans = :total_trans";

        return databaseClient.sql(upsertSql)
                .bind("d_transaction_day", report.getDTransactionDay())
                .bind("merchant_id", report.getMerchantId())
                .bind("transaction_type", report.getTransactionType())
                .bind("currency", report.getCurrency())
                .bind("payment_method", report.getPaymentMethod())
                .bind("order_source", report.getOrderSource())
                .bind("is_promotion", report.getIsPromotion())
                .bind("amount", report.getAmount())
                .bind("created_date", LocalDateTime.now())
                .bind("updated_date", LocalDateTime.now())
                .bind("total_trans", report.getTotalTrans())
                .then()
                .doOnError(e -> logger.error("Error upserting report: {}", report, e));
    }

    public static String normalizeDate(String fromDate) {
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        try {
            LocalDate date = LocalDate.parse(fromDate, inputFormatter);
            return outputFormatter.format(date);
        } catch (DateTimeParseException e) {
            // Không đúng định dạng dd-MM-yyyy => giữ nguyên
            return fromDate;
        }
    }

    @Override
    public FileDownloadDto createExcel(DownloadTaskEntity task) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        try {
            logger.info("Start exporting Excel for taskId={}, fileName={}", task.getId(), task.getFileName());
            // Tạo thư mục lưu file nếu chưa có
            String baseDir = exportFileProperties.getBaseDir();
            File dir = new File(baseDir);
            if (!dir.exists())
                dir.mkdirs();
            String outputPath = baseDir + task.getFileHashName();

            // Parse requestParams từ task
            Map<String, Object> params = objectMapper.readValue(task.getRequestParams(), new TypeReference<>() {
            });
            String from = (String) params.get("fromDate");
            String to = (String) params.get("toDate");
            String merchantId = (String) params.get("merchantId");
            String currency = (String) params.get("currency");
            String paymentMethod = (String) params.get("paymentMethod");
            Boolean isPromotion = (Boolean) params.get("isPromotion");
            String orderSource = (String) params.get("orderSource");
            String interval = (String) params.get("interval");

            List<TransactionReportDTO> allTransactions = new ArrayList<>();
            if (merchantId != null && !merchantId.isEmpty()) {
                List<String> paymentMethods = parseCsvToList(paymentMethod);
                List<String> orderSources = parseCsvToList(orderSource);
                List<String> merchantIds = parseCsvToList(merchantId);
                String fromDate = normalizeDate(from);
                String toDate = normalizeDate(to);
                String groupBy = switch (interval.toUpperCase()) {
                    case "DAILY" -> "d_transaction_day";
                    case "WEEKLY" -> "TO_CHAR(d_transaction_day, 'IYYY-\"W\"IW')";
                    case "MONTHLY" -> "TO_CHAR(d_transaction_day, 'YYYY-MM')";
                    default -> throw new IllegalArgumentException("Invalid interval: " + interval);
                };
                // Build SQL with dynamic IN clause if needed
                StringBuilder sqlBuilder = buildQuery(groupBy, paymentMethods, orderSources, isPromotion);

                DatabaseClient.GenericExecuteSpec spec = databaseClient.sql(sqlBuilder.toString())
                        .bind("fromDate", LocalDate.parse(fromDate))
                        .bind("toDate", LocalDate.parse(toDate))
                        .bind("currency", currency)
                        .bind("merchantIds", merchantIds);
                if (paymentMethods != null && !paymentMethods.isEmpty()) {
                    spec = spec.bind("paymentMethods", paymentMethods);
                }

                if (orderSources != null && !orderSources.isEmpty()) {
                    spec = spec.bind("orderSources", orderSources);
                }

                if (isPromotion != null) {
                    spec = spec.bind("isPromotion", isPromotion);
                }

                logger.debug("Executing query with SQL: {}", sqlBuilder);
                allTransactions = spec
                        .map((row, meta) -> new TransactionReportDTO(
                                row.get("merchant_id", String.class),
                                row.get("date", String.class),
                                row.get("num_purchase_trans", Long.class),
                                row.get("num_authorize_trans", Long.class),
                                row.get("num_capture_trans", Long.class),
                                row.get("num_refund_trans", Long.class),
                                row.get("total_revenue", BigDecimal.class),
                                row.get("total_refund", BigDecimal.class)))
                        .all()
                        .collectList().block();
                try (InputStream is = getClass().getClassLoader()
                        .getResourceAsStream("template/TransactionReportTemplate.xlsx");
                        Workbook workbook = new XSSFWorkbook(is);
                        FileOutputStream fos = new FileOutputStream(outputPath)) {
                    if (is == null)
                        throw new FileNotFoundException("Template not found in resources/template/");
                    Sheet sheet = workbook.getSheetAt(0);
                    int startRow = 4;

                    Row rowDate = sheet.createRow(1);
                    rowDate.createCell(3).setCellValue("From: " + fromDate);
                    rowDate.createCell(5).setCellValue("To: " + toDate);

                    for (int i = 0; i < allTransactions.size(); i++) {
                        TransactionReportDTO dto = allTransactions.get(i);
                        Row row = sheet.createRow(startRow + i);
                        row.createCell(0).setCellValue(i + 1);
                        row.createCell(1).setCellValue(dto.getDate());
                        row.createCell(2).setCellValue(dto.getMerchantId());
                        row.createCell(3).setCellValue(dto.getNumPurchaseTrans());
                        row.createCell(4).setCellValue(dto.getNumAuthorizeTrans());
                        row.createCell(5).setCellValue(dto.getNumCaptureTrans());
                        row.createCell(6).setCellValue(dto.getNumRefundTrans());
                        row.createCell(7).setCellValue(dto.getTotalRevenue().toString());
                        row.createCell(8).setCellValue(dto.getTotalRefund().toString());
                        row.createCell(9).setCellValue(currency);
                    }
                    workbook.write(fos);
                }
            }

            // Tạo FileDownloadDto trả về
            File file = new File(outputPath);
            FileDownloadDto dto = new FileDownloadDto();
            dto.setFile_hash_name(task.getFileHashName());
            dto.setFile_name(task.getFileName());
            dto.setFile_path(outputPath);
            dto.setFile_size(file.length());
            dto.setUser(task.getUserId());
            logger.info("Exported {} transactions to {}", allTransactions.size(), outputPath);
            return dto;
        } catch (Exception e) {
            logger.error("Error while exporting Excel for task {}: {}", task.getId(), e.getMessage(), e);
            throw new RuntimeException("Export Excel failed", e);
        }
    }

    private List<String> parseCsvToList(String csv) {
        if (!StringUtils.hasText(csv))
            return List.of();
        return Arrays.stream(csv.split(","))
                .map(String::trim)
                .filter(StringUtils::hasText)
                .toList();
    }

    private StringBuilder buildQuery(String groupBy, List<String> paymentMethods, List<String> orderSources,
            Boolean isPromotion) {
        StringBuilder sqlBuilder = new StringBuilder(
                """
                            SELECT
                              %s AS date,
                              merchant_id,
                              SUM(CASE WHEN transaction_type = 'Purchase' THEN total_trans ELSE 0 END) AS num_purchase_trans,
                              SUM(CASE WHEN transaction_type = 'Authorize' THEN total_trans ELSE 0 END) AS num_authorize_trans,
                              SUM(CASE WHEN transaction_type = 'Capture' THEN total_trans ELSE 0 END) AS num_capture_trans,
                              SUM(CASE
                                    WHEN transaction_type IN (
                                        'Refund',
                                        'Refund Capture',
                                        'Refund Dispute',
                                        'Void Purchase',
                                        'Void Authorize',
                                        'Void Capture',
                                        'Void Refund',
                                        'Void Refund Capture'
                                    ) THEN total_trans
                                    ELSE 0
                                END) AS num_refund_trans,
                              SUM(CASE
                                    WHEN transaction_type = 'Purchase' THEN amount
                                    WHEN transaction_type = 'Void Purchase' THEN -amount
                                    WHEN transaction_type = 'Capture' THEN amount
                                    WHEN transaction_type = 'Void Capture' THEN -amount
                                    WHEN transaction_type = 'Refund' THEN -amount
                                    ELSE 0
                              END) AS total_revenue,
                              SUM(CASE
                                    WHEN transaction_type = 'Refund' THEN amount
                                    WHEN transaction_type = 'Void Refund' THEN -amount
                                    WHEN transaction_type = 'Refund Capture' THEN amount
                                    WHEN transaction_type = 'Void Refund Capture' THEN -amount
                                    WHEN transaction_type = 'Refund Dispute' THEN amount
                                    ELSE 0
                              END) AS total_refund
                            FROM transaction_report
                            WHERE d_transaction_day BETWEEN :fromDate AND :toDate
                                AND merchant_id IN (:merchantIds)
                        """
                        .formatted(groupBy));
        // Thêm filter nếu có
        if (!paymentMethods.isEmpty()) {
            sqlBuilder.append(" AND payment_method IN (:paymentMethods)");
        }

        if (!orderSources.isEmpty()) {
            sqlBuilder.append(" AND order_source IN (:orderSources)");
        }

        if (isPromotion != null) {
            sqlBuilder.append(" AND is_promotion = :isPromotion");
        }
        sqlBuilder.append("""
                    AND (:currency IS NULL OR currency = :currency)
                    GROUP BY %s, merchant_id
                    ORDER BY %s DESC
                """.formatted(groupBy, groupBy));
        return sqlBuilder;
    }
}
