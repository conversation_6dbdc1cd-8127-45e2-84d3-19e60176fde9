package vn.onepay.transaction.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.r2dbc.core.DatabaseClient;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import vn.onepay.transaction.constant.IntervalType;
import vn.onepay.transaction.dto.DashboardSummaryDTO;
import vn.onepay.transaction.dto.RevenuePercentDTO;
import vn.onepay.transaction.dto.RevenueTrendDTO;
import vn.onepay.transaction.service.DashboardService;
import vn.onepay.transaction.service.MerchantService;
import vn.onepay.transaction.exception.DateRangeInvalidException;

@Service
public class DashboardServiceImpl implements DashboardService {

    private static final Logger logger = LoggerFactory.getLogger(DashboardServiceImpl.class);

    @Autowired
    private DatabaseClient databaseClient;

    @Autowired
    private MerchantService merchantService;

    @Override
    public Mono<DashboardSummaryDTO> getSummary(String fromDate, String toDate,
            String currency, String merchantId, Boolean isPromotion, String paymentMethod, String orderSource) {
        logger.info(
                "Start processing summary for date range: {} to {}, currency: {}, merchantId: {},isPromotion: {},paymentMethod: {},orderSource: {}",
                fromDate, toDate, currency, merchantId, isPromotion, paymentMethod, orderSource);

        try {
            fromDate = normalizeDate(fromDate);
            toDate = normalizeDate(toDate);
            LocalDate from = LocalDate.parse(fromDate);
            LocalDate to = LocalDate.parse(toDate);
            return merchantService.resolveMerchantIdFilter(merchantId)
                    .flatMap(merchantIds -> {
                        if (merchantIds.isEmpty()) {
                            logger.warn("No merchantId matched -> returning empty response");
                            return Mono
                                    .just(new DashboardSummaryDTO(currency, 0L, 0L, BigDecimal.ZERO, BigDecimal.ZERO));
                        }
                        StringBuilder baseSql = new StringBuilder(
                                """
                                                SELECT
                                                    currency,
                                                    COALESCE(SUM(CASE
                                                                WHEN transaction_type IN ('Purchase', 'Authorize', 'Capture') THEN total_trans
                                                                ELSE 0
                                                                END), 0) AS transactionCount,
                                                     (
                                                        COALESCE(SUM(CASE WHEN transaction_type = 'Refund' THEN amount ELSE 0 END), 0)
                                                        -
                                                        COALESCE(SUM(CASE WHEN transaction_type = 'Void Refund' THEN amount ELSE 0 END), 0)
                                                        +
                                                        COALESCE(SUM(CASE WHEN transaction_type = 'Refund Capture' THEN amount ELSE 0 END), 0)
                                                        -
                                                        COALESCE(SUM(CASE WHEN transaction_type = 'Void Refund Capture' THEN amount ELSE 0 END), 0)
                                                        +
                                                        COALESCE(SUM(CASE WHEN transaction_type = 'Refund Dispute' THEN amount ELSE 0 END), 0)
                                                    ) AS totalRefund,
                                                    COALESCE(SUM(CASE
                                                                WHEN transaction_type IN (
                                                                    'Refund',
                                                                    'Refund Capture',
                                                                    'Refund Dispute',
                                                                    'Void Purchase',
                                                                    'Void Authorize',
                                                                    'Void Capture',
                                                                    'Void Refund',
                                                                    'Void Refund Capture'
                                                                )
                                                                THEN total_trans
                                                                ELSE 0
                                                                END), 0) AS refundCount,
                                                    COALESCE(SUM(CASE WHEN transaction_type = 'Purchase' THEN amount ELSE 0 END), 0)
                                                  - COALESCE(SUM(CASE WHEN transaction_type = 'Void Purchase' THEN amount ELSE 0 END), 0)
                                                  + COALESCE(SUM(CASE WHEN transaction_type = 'Capture' THEN amount ELSE 0 END), 0)
                                                  - COALESCE(SUM(CASE WHEN transaction_type = 'Void Capture' THEN amount ELSE 0 END), 0)
                                                  - COALESCE(SUM(CASE WHEN transaction_type = 'Refund' THEN amount ELSE 0 END), 0)
                                                  AS totalRevenue
                                                FROM transaction_report
                                                WHERE d_transaction_day BETWEEN :fromDate AND :toDate
                                                  AND currency = :currency
                                                  AND merchant_id IN (:merchantIds)
                                        """);

                        // Thêm filter nếu có
                        List<String> paymentMethods = parseCsvToList(paymentMethod);
                        List<String> orderSources = parseCsvToList(orderSource);
                        if (!paymentMethods.isEmpty()) {
                            baseSql.append(" AND payment_method IN (:paymentMethods)");
                        }

                        if (!orderSources.isEmpty()) {
                            baseSql.append(" AND order_source IN (:orderSources)");
                        }

                        if (isPromotion != null) {
                            baseSql.append(" AND is_promotion = :isPromotion");
                        }

                        baseSql.append(" GROUP BY currency");

                        DatabaseClient.GenericExecuteSpec spec = databaseClient.sql(baseSql.toString())
                                .bind("fromDate", from)
                                .bind("toDate", to)
                                .bind("currency", currency)
                                .bind("merchantIds", merchantIds);

                        if (paymentMethods != null && !paymentMethods.isEmpty()) {
                            spec = spec.bind("paymentMethods", paymentMethods);
                        }

                        if (orderSources != null && !orderSources.isEmpty()) {
                            spec = spec.bind("orderSources", orderSources);
                        }

                        if (isPromotion != null) {
                            spec = spec.bind("isPromotion", isPromotion);
                        }

                        logger.debug("Executing summary query with SQL: {}", baseSql);

                        return spec.map((row, meta) -> {
                            DashboardSummaryDTO dto = DashboardSummaryDTO.builder()
                                    .currency(row.get("currency", String.class))
                                    .transactionCount(row.get("transactionCount", Long.class))
                                    .totalRefund(row.get("totalRefund", BigDecimal.class))
                                    .totalRevenue(row.get("totalRevenue", BigDecimal.class))
                                    .refundCount(row.get("refundCount", Long.class))
                                    .build();
                            logger.info("Fetched summary result: {}", dto);
                            return dto;
                        })
                                .one()
                                .switchIfEmpty(Mono.fromSupplier(() -> {
                                    logger.warn("No summary data found for the given parameters.");
                                    return DashboardSummaryDTO.builder()
                                            .currency(currency)
                                            .transactionCount(0L)
                                            .totalRefund(BigDecimal.ZERO)
                                            .totalRevenue(BigDecimal.ZERO)
                                            .refundCount(0L)
                                            .build();
                                }))
                                .doOnError(e -> logger.error("Error during summary query: {}", e.getMessage(), e));
                    });

        } catch (Exception e) {
            logger.error("Invalid input or unexpected error during summary: {}", e.getMessage(), e);
            return Mono.error(new RuntimeException("An error occurred while fetching the summary data", e));
        }
    }

    @Override
    public Flux<RevenueTrendDTO> getRevenueTrend(String fromDateStr, String toDateStr,
            String currency, String merchantId, IntervalType intervalType, Boolean isPromotion, String paymentMethod,
            String orderSource) {
        logger.info(
                "Generating revenue trend from {} to {}, currency={}, merchantId={}, interval={},isPromotion: {},paymentMethod: {},orderSource: {}",
                fromDateStr, toDateStr, currency, merchantId, intervalType, isPromotion, paymentMethod, orderSource);

        try {
            fromDateStr = normalizeDate(fromDateStr);
            toDateStr = normalizeDate(toDateStr);
            LocalDate fromDate = LocalDate.parse(fromDateStr);
            LocalDate toDate = LocalDate.parse(toDateStr);
            // Validate only for WEEKLY and MONTHLY
            if (intervalType == IntervalType.WEEKLY && !fromDate.getDayOfWeek().equals(DayOfWeek.MONDAY)) {
                throw new DateRangeInvalidException("fromDate must be a Monday for weekly interval");
            }
            if (intervalType == IntervalType.MONTHLY && fromDate.getDayOfMonth() != 1) {
                throw new DateRangeInvalidException("fromDate must be the first day of the month for monthly interval");
            }

            return merchantService.resolveMerchantIdFilter(merchantId)
                    .flatMapMany(merchantIds -> {
                        if (merchantIds.isEmpty()) {
                            logger.warn("No merchantId matched -> returning empty response");
                            RevenueTrendDTO defaultDto = new RevenueTrendDTO(
                                    fromDate.toString() + "-" + toDate.toString(), BigDecimal.ZERO, 0L);
                            return Flux.just(defaultDto);
                        }
                        return switch (intervalType) {
                            case DAILY ->
                                processDaily(fromDate, currency, merchantIds, isPromotion, paymentMethod, orderSource);
                            case WEEKLY ->
                                processWeekly(fromDate, currency, merchantIds, isPromotion, paymentMethod, orderSource);
                            case MONTHLY -> processMonthly(fromDate, currency, merchantIds, isPromotion, paymentMethod,
                                    orderSource);
                        };
                    });
        } catch (Exception e) {
            logger.error("Invalid input or unexpected error during getRevenueTrend: {}", e.getMessage(), e);
            return Flux.error(new RuntimeException("An error occurred while fetching the summary data", e));
        }
    }

    private Flux<RevenueTrendDTO> processDaily(LocalDate fromDate, String currency, List<String> merchantIds,
            Boolean isPromotion, String paymentMethod, String orderSource) {
        List<LocalDate> days = IntStream.rangeClosed(0, 6)
                .mapToObj(fromDate::minusDays)
                .sorted()
                .toList();

        return fetchData("d_transaction_day", days.get(0), days.get(6), currency, merchantIds, isPromotion,
                paymentMethod, orderSource)
                .flatMapMany(dataMap -> Flux.fromIterable(days)
                        .map(date -> {
                            String key = date.format(DateTimeFormatter.ISO_DATE);
                            return dataMap.getOrDefault(key, new RevenueTrendDTO(key, BigDecimal.ZERO, 0L));
                        }));
    }

    private Flux<RevenueTrendDTO> processWeekly(LocalDate fromDate, String currency, List<String> merchantIds,
            Boolean isPromotion, String paymentMethod, String orderSource) {
        List<String> weeks = IntStream.rangeClosed(0, 11)
                .mapToObj(i -> fromDate.minusWeeks(i))
                .map(date -> getYearWeek(date))
                .sorted()
                .toList();

        LocalDate start = fromDate.minusWeeks(11).with(DayOfWeek.MONDAY);
        LocalDate end = fromDate.with(DayOfWeek.SUNDAY);

        return fetchData(
                "EXTRACT(YEAR FROM d_transaction_day) || '-W' || LPAD(EXTRACT(WEEK FROM d_transaction_day)::text, 2, '0')",
                start, end, currency, merchantIds, isPromotion, paymentMethod, orderSource)
                .flatMapMany(dataMap -> Flux.fromIterable(weeks)
                        .map(week -> dataMap.getOrDefault(week, new RevenueTrendDTO(week, BigDecimal.ZERO, 0L))));
    }

    private Flux<RevenueTrendDTO> processMonthly(LocalDate fromDate, String currency, List<String> merchantIds,
            Boolean isPromotion, String paymentMethod, String orderSource) {
        List<String> months = IntStream.rangeClosed(0, 11)
                .mapToObj(i -> YearMonth.from(fromDate.minusMonths(i)))
                .map(ym -> ym.format(DateTimeFormatter.ofPattern("yyyy-MM")))
                .sorted()
                .toList();

        LocalDate start = fromDate.minusMonths(11).withDayOfMonth(1);
        LocalDate end = YearMonth.from(fromDate).atEndOfMonth();

        return fetchData("TO_CHAR(d_transaction_day, 'yyyy-MM')", start, end, currency, merchantIds, isPromotion,
                paymentMethod, orderSource)
                .flatMapMany(dataMap -> Flux.fromIterable(months)
                        .map(month -> dataMap.getOrDefault(month, new RevenueTrendDTO(month, BigDecimal.ZERO, 0L))));
    }

    private Mono<Map<String, RevenueTrendDTO>> fetchData(String periodExpression, LocalDate start, LocalDate end,
            String currency, List<String> merchantIds, Boolean isPromotion, String paymentMethod, String orderSource) {

        StringBuilder sql = new StringBuilder(String.format("""
                SELECT %s AS period,
                       COALESCE(SUM(amount), 0) AS transactionVolume,
                       COALESCE(SUM(total_trans), 0) AS transactionCount
                FROM transaction_report
                WHERE d_transaction_day BETWEEN :start AND :end
                  AND currency = :currency
                  AND merchant_id IN (:merchantIds)
                """, periodExpression));

        List<String> paymentMethods = null;
        if (StringUtils.hasText(paymentMethod)) {
            paymentMethods = Arrays.stream(paymentMethod.split(","))
                    .map(String::trim)
                    .filter(StringUtils::hasText)
                    .toList();
            if (!paymentMethods.isEmpty()) {
                sql.append(" AND payment_method IN (:paymentMethods)");
            }
        }

        List<String> orderSources = null;
        if (StringUtils.hasText(orderSource)) {
            orderSources = Arrays.stream(orderSource.split(","))
                    .map(String::trim)
                    .filter(StringUtils::hasText)
                    .toList();
            if (!orderSources.isEmpty()) {
                sql.append(" AND order_source IN (:orderSources)");
            }
        }

        if (isPromotion != null) {
            sql.append(" AND is_promotion = :isPromotion");
        }

        sql.append(" GROUP BY period ORDER BY period");

        DatabaseClient.GenericExecuteSpec spec = databaseClient.sql(sql.toString())
                .bind("start", start)
                .bind("end", end)
                .bind("currency", currency)
                .bind("merchantIds", merchantIds);

        if (paymentMethods != null && !paymentMethods.isEmpty()) {
            spec = spec.bind("paymentMethods", paymentMethods);
        }

        if (orderSources != null && !orderSources.isEmpty()) {
            spec = spec.bind("orderSources", orderSources);
        }

        if (isPromotion != null) {
            spec = spec.bind("isPromotion", isPromotion);
        }

        return spec.map((row, meta) -> {
            String period = row.get("period", String.class);
            BigDecimal volume = row.get("transactionVolume", BigDecimal.class);
            Long count = row.get("transactionCount", Long.class);
            return new RevenueTrendDTO(period, volume, count);
        })
                .all()
                .collectMap(RevenueTrendDTO::getPeriod);
    }

    private String getYearWeek(LocalDate date) {
        int week = date.get(java.time.temporal.WeekFields.ISO.weekOfWeekBasedYear());
        int year = date.get(java.time.temporal.WeekFields.ISO.weekBasedYear());
        return String.format("%d-W%02d", year, week);
    }

    @Override
    public Flux<RevenuePercentDTO> getRevenueGroupBy(String fromDateStr,
            String toDateStr, String currency, String merchantId, Boolean isPromotion, String paymentMethod,
            String orderSource, String groupBy) {
        String fromDate = normalizeDate(fromDateStr);
        String toDate = normalizeDate(toDateStr);

        return merchantService.resolveMerchantIdFilter(merchantId)
                .flatMapMany(merchantIds -> {
                    if (merchantIds.isEmpty()) {
                        logger.warn("No merchantId matched -> returning empty response");
                        return Flux.<RevenuePercentDTO>empty();
                    }
                    String groupColumn;
                    switch (groupBy) {
                        case "paymentMethod" -> groupColumn = "payment_method";
                        case "orderSource" -> groupColumn = "order_source";
                        case "merchantId" -> groupColumn = "merchant_id";
                        default -> throw new IllegalArgumentException("Invalid groupBy value: " + groupBy);
                    }
                    StringBuilder baseSql = new StringBuilder("""
                                FROM transaction_report
                                WHERE d_transaction_day BETWEEN :fromDate AND :toDate
                                    AND merchant_id IN (:merchantIds)
                                    AND currency = :currency
                            """);
                    List<String> paymentMethods = null;
                    if (StringUtils.hasText(paymentMethod)) {
                        paymentMethods = Arrays.stream(paymentMethod.split(","))
                                .map(String::trim)
                                .filter(StringUtils::hasText)
                                .toList();
                        if (!paymentMethods.isEmpty()) {
                            baseSql.append(" AND payment_method IN (:paymentMethods)");
                        }
                    }

                    List<String> orderSources = null;
                    if (StringUtils.hasText(orderSource)) {
                        orderSources = Arrays.stream(orderSource.split(","))
                                .map(String::trim)
                                .filter(StringUtils::hasText)
                                .toList();
                        if (!orderSources.isEmpty()) {
                            baseSql.append(" AND order_source IN (:orderSources)");
                        }
                    }

                    if (isPromotion != null) {
                        baseSql.append(" AND is_promotion = :isPromotion");
                    }

                    // 1. Truy vấn danh sách merchant và revenue
                    String revenueSql = String.format("""
                            SELECT %s AS group_value,
                                   SUM(CASE WHEN transaction_type = 'Purchase' THEN amount ELSE 0 END)
                                 - SUM(CASE WHEN transaction_type = 'Void Purchase' THEN amount ELSE 0 END)
                                 + SUM(CASE WHEN transaction_type = 'Capture' THEN amount ELSE 0 END)
                                 - SUM(CASE WHEN transaction_type = 'Void Capture' THEN amount ELSE 0 END)
                                 - SUM(CASE WHEN transaction_type = 'Refund' THEN amount ELSE 0 END)
                                   AS revenue
                            """, groupColumn) + baseSql + " GROUP BY " + groupColumn
                            + """
                                        HAVING
                                               SUM(CASE WHEN transaction_type = 'Purchase' THEN amount ELSE 0 END)
                                             - SUM(CASE WHEN transaction_type = 'Void Purchase' THEN amount ELSE 0 END)
                                             + SUM(CASE WHEN transaction_type = 'Capture' THEN amount ELSE 0 END)
                                             - SUM(CASE WHEN transaction_type = 'Void Capture' THEN amount ELSE 0 END)
                                             - SUM(CASE WHEN transaction_type = 'Refund' THEN amount ELSE 0 END)
                                             > 0
                                        ORDER BY revenue DESC
                                    """;

                    DatabaseClient.GenericExecuteSpec revenueSpec = databaseClient.sql(revenueSql)
                            .bind("fromDate", LocalDate.parse(fromDate))
                            .bind("toDate", LocalDate.parse(toDate))
                            .bind("merchantIds", merchantIds)
                            .bind("currency", currency);
                    if (paymentMethods != null && !paymentMethods.isEmpty()) {
                        revenueSpec = revenueSpec.bind("paymentMethods", paymentMethods);
                    }

                    if (orderSources != null && !orderSources.isEmpty()) {
                        revenueSpec = revenueSpec.bind("orderSources", orderSources);
                    }

                    if (isPromotion != null) {
                        revenueSpec = revenueSpec.bind("isPromotion", isPromotion);
                    }

                    Flux<RevenuePercentDTO> revenueFlux = revenueSpec.map((row, meta) -> {
                        String mid = row.get("group_value", String.class);
                        BigDecimal revenue = row.get("revenue", BigDecimal.class);
                        return new RevenuePercentDTO(mid, revenue, null);
                    }).all();

                    // 2. Truy vấn tổng doanh thu tất cả merchant
                    String totalSql = """
                            SELECT
                                   SUM(CASE WHEN transaction_type = 'Purchase' THEN amount ELSE 0 END)
                                 - SUM(CASE WHEN transaction_type = 'Void Purchase' THEN amount ELSE 0 END)
                                 + SUM(CASE WHEN transaction_type = 'Capture' THEN amount ELSE 0 END)
                                 - SUM(CASE WHEN transaction_type = 'Void Capture' THEN amount ELSE 0 END)
                                 - SUM(CASE WHEN transaction_type = 'Refund' THEN amount ELSE 0 END)
                                   AS total_revenue
                            """ + baseSql;

                    DatabaseClient.GenericExecuteSpec totalSpec = databaseClient.sql(totalSql)
                            .bind("fromDate", LocalDate.parse(fromDate))
                            .bind("toDate", LocalDate.parse(toDate))
                            .bind("merchantIds", merchantIds)
                            .bind("currency", currency);

                    if (paymentMethods != null && !paymentMethods.isEmpty()) {
                        totalSpec = totalSpec.bind("paymentMethods", paymentMethods);
                    }

                    if (orderSources != null && !orderSources.isEmpty()) {
                        totalSpec = totalSpec.bind("orderSources", orderSources);
                    }

                    if (isPromotion != null) {
                        totalSpec = totalSpec.bind("isPromotion", isPromotion);
                    }

                    Mono<BigDecimal> totalRevenueMono = totalSpec.map((row, meta) -> {
                        BigDecimal total = row.get("total_revenue", BigDecimal.class);
                        return total != null ? total : BigDecimal.ZERO;
                    }).one();

                    // 3. Zip lại để tính phần trăm
                    return totalRevenueMono.flatMapMany(totalRevenue -> {
                        if (totalRevenue.compareTo(BigDecimal.ZERO) == 0) {
                            return Flux.<RevenuePercentDTO>empty();
                        }

                        return revenueFlux.map(dto -> {
                            double percent = dto.getRevenue()
                                    .divide(totalRevenue, 4, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
                                    .doubleValue();
                            dto.setPercent(percent);
                            return dto;
                        });
                    });
                });
    }

    public static String normalizeDate(String fromDate) {
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        try {
            LocalDate date = LocalDate.parse(fromDate, inputFormatter);
            return outputFormatter.format(date);
        } catch (DateTimeParseException e) {
            // Không đúng định dạng dd-MM-yyyy => giữ nguyên
            return fromDate;
        }
    }

    private List<String> parseCsvToList(String csv) {
        if (!StringUtils.hasText(csv))
            return List.of();
        return Arrays.stream(csv.split(","))
                .map(String::trim)
                .filter(StringUtils::hasText)
                .toList();
    }

}
