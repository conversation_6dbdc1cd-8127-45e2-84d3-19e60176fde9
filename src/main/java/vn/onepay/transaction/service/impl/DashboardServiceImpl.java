package vn.onepay.transaction.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.r2dbc.core.DatabaseClient;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ServerWebExchange;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import vn.onepay.transaction.constant.IntervalType;
import vn.onepay.transaction.dto.DashboardSummaryDTO;
import vn.onepay.transaction.dto.RevenueMerchantDTO;
import vn.onepay.transaction.dto.RevenueTrendDTO;
import vn.onepay.transaction.service.DashboardService;
import vn.onepay.transaction.service.MerchantService;
import vn.onepay.transaction.util.DateValidatorUtil;
import vn.onepay.transaction.exception.DateRangeInvalidException;

@Service
public class DashboardServiceImpl implements DashboardService {

    private static final Logger logger = LoggerFactory.getLogger(DashboardServiceImpl.class);

    @Autowired
    private DatabaseClient databaseClient;

    @Autowired
    private MerchantService merchantService;

    @Override
    public Mono<DashboardSummaryDTO> getSummary(ServerWebExchange exchange, String fromDate, String toDate,
            String currency, String merchantId) {
        logger.info("Start processing summary for date range: {} to {}, currency: {}, merchantId: {}",
                fromDate, toDate, currency, merchantId);

        try {
            fromDate = normalizeDate(fromDate);
            toDate = normalizeDate(toDate);
            LocalDate from = LocalDate.parse(fromDate);
            LocalDate to = LocalDate.parse(toDate);
            return merchantService.resolveMerchantIdFilter(exchange, merchantId)
                    .flatMap(merchantIds -> {
                        if (merchantIds.isEmpty()) {
                            logger.warn("No merchantId matched -> returning empty response");
                            return Mono
                                    .just(new DashboardSummaryDTO(currency, 0L, 0L, BigDecimal.ZERO, BigDecimal.ZERO));
                        }
                        StringBuilder baseSql = new StringBuilder(
                                """
                                                SELECT
                                                    currency,
                                                    COALESCE(SUM(total_trans), 0) AS transactionCount,
                                                    COALESCE(SUM(CASE WHEN transaction_type = 'Refund' THEN amount ELSE 0 END), 0) AS totalRefund,
                                                    COALESCE(SUM(CASE WHEN transaction_type = 'Refund' THEN total_trans ELSE 0 END), 0) AS refundCount,
                                                    COALESCE(SUM(CASE WHEN transaction_type = 'Purchase' THEN amount ELSE 0 END), 0)
                                                  - COALESCE(SUM(CASE WHEN transaction_type = 'Void Purchase' THEN amount ELSE 0 END), 0)
                                                  + COALESCE(SUM(CASE WHEN transaction_type = 'Capture' THEN amount ELSE 0 END), 0)
                                                  - COALESCE(SUM(CASE WHEN transaction_type = 'Void Capture' THEN amount ELSE 0 END), 0)
                                                  - COALESCE(SUM(CASE WHEN transaction_type = 'Refund' THEN amount ELSE 0 END), 0)
                                                  AS totalRevenue
                                                FROM transaction_report
                                                WHERE d_transaction_day BETWEEN :fromDate AND :toDate
                                                  AND currency = :currency
                                        """);

                        if (!merchantIds.isEmpty()) {
                            baseSql.append(" AND merchant_id IN (");
                            for (int i = 0; i < merchantIds.size(); i++) {
                                baseSql.append(":merchant").append(i);
                                if (i < merchantIds.size() - 1)
                                    baseSql.append(", ");
                            }
                            baseSql.append(")");
                        }

                        baseSql.append(" GROUP BY currency");

                        DatabaseClient.GenericExecuteSpec spec = databaseClient.sql(baseSql.toString())
                                .bind("fromDate", from)
                                .bind("toDate", to)
                                .bind("currency", currency);

                        for (int i = 0; i < merchantIds.size(); i++) {
                            spec = spec.bind("merchant" + i, merchantIds.get(i));
                        }

                        logger.debug("Executing summary query with SQL: {}", baseSql);

                        return spec.map((row, meta) -> {
                            DashboardSummaryDTO dto = DashboardSummaryDTO.builder()
                                    .currency(row.get("currency", String.class))
                                    .transactionCount(row.get("transactionCount", Long.class))
                                    .totalRefund(row.get("totalRefund", BigDecimal.class))
                                    .totalRevenue(row.get("totalRevenue", BigDecimal.class))
                                    .refundCount(row.get("refundCount", Long.class))
                                    .build();
                            logger.info("Fetched summary result: {}", dto);
                            return dto;
                        })
                                .one()
                                .switchIfEmpty(Mono.fromSupplier(() -> {
                                    logger.warn("No summary data found for the given parameters.");
                                    return DashboardSummaryDTO.builder()
                                            .currency(currency)
                                            .transactionCount(0L)
                                            .totalRefund(BigDecimal.ZERO)
                                            .totalRevenue(BigDecimal.ZERO)
                                            .refundCount(0L)
                                            .build();
                                }))
                                .doOnError(e -> logger.error("Error during summary query: {}", e.getMessage(), e));
                    });

        } catch (Exception e) {
            logger.error("Invalid input or unexpected error during summary: {}", e.getMessage(), e);
            return Mono.error(new RuntimeException("An error occurred while fetching the summary data", e));
        }
    }

    @Override
    public Flux<RevenueTrendDTO> getRevenueTrend(ServerWebExchange exchange, String fromDateStr, String toDateStr,
            String currency,
            String merchantId,
            IntervalType intervalType) {
        logger.info("Generating revenue trend from {} to {}, currency={}, merchantId={}, interval={}",
                fromDateStr, toDateStr, currency, merchantId, intervalType);

        try {
            fromDateStr = normalizeDate(fromDateStr);
            toDateStr = normalizeDate(toDateStr);
            LocalDate fromDate = LocalDate.parse(fromDateStr);
            LocalDate toDate = LocalDate.parse(toDateStr);
            // Validate only for WEEKLY and MONTHLY
            if (intervalType == IntervalType.WEEKLY && !fromDate.getDayOfWeek().equals(DayOfWeek.MONDAY)) {
                throw new DateRangeInvalidException("fromDate must be a Monday for weekly interval");
            }
            if (intervalType == IntervalType.MONTHLY && fromDate.getDayOfMonth() != 1) {
                throw new DateRangeInvalidException("fromDate must be the first day of the month for monthly interval");
            }

            return merchantService.resolveMerchantIdFilter(exchange, merchantId)
                    .flatMapMany(merchantIds -> {
                        if (merchantIds.isEmpty()) {
                            logger.warn("No merchantId matched -> returning empty response");
                            RevenueTrendDTO defaultDto = new RevenueTrendDTO(
                                    fromDate.toString() + "-" + toDate.toString(), BigDecimal.ZERO, 0L);
                            return Flux.just(defaultDto);
                        }
                        return switch (intervalType) {
                            case DAILY -> processDaily(fromDate, currency, merchantIds);
                            case WEEKLY -> processWeekly(fromDate, currency, merchantIds);
                            case MONTHLY -> processMonthly(fromDate, currency, merchantIds);
                        };
                    });
        } catch (Exception e) {
            logger.error("Invalid input or unexpected error during getRevenueTrend: {}", e.getMessage(), e);
            return Flux.error(new RuntimeException("An error occurred while fetching the summary data", e));
        }
    }

    private Flux<RevenueTrendDTO> processDaily(LocalDate fromDate, String currency, List<String> merchantIds) {
        List<LocalDate> days = IntStream.rangeClosed(0, 6)
                .mapToObj(fromDate::minusDays)
                .sorted()
                .toList();

        return fetchData("d_transaction_day", days.get(0), days.get(6), currency, merchantIds)
                .flatMapMany(dataMap -> Flux.fromIterable(days)
                        .map(date -> {
                            String key = date.format(DateTimeFormatter.ISO_DATE);
                            return dataMap.getOrDefault(key, new RevenueTrendDTO(key, BigDecimal.ZERO, 0L));
                        }));
    }

    private Flux<RevenueTrendDTO> processWeekly(LocalDate fromDate, String currency, List<String> merchantIds) {
        List<String> weeks = IntStream.rangeClosed(0, 11)
                .mapToObj(i -> fromDate.minusWeeks(i))
                .map(date -> getYearWeek(date))
                .sorted()
                .toList();

        LocalDate start = fromDate.minusWeeks(11).with(DayOfWeek.MONDAY);
        LocalDate end = fromDate.with(DayOfWeek.SUNDAY);

        return fetchData(
                "EXTRACT(YEAR FROM d_transaction_day) || '-W' || LPAD(EXTRACT(WEEK FROM d_transaction_day)::text, 2, '0')",
                start, end, currency, merchantIds)
                .flatMapMany(dataMap -> Flux.fromIterable(weeks)
                        .map(week -> dataMap.getOrDefault(week, new RevenueTrendDTO(week, BigDecimal.ZERO, 0L))));
    }

    private Flux<RevenueTrendDTO> processMonthly(LocalDate fromDate, String currency, List<String> merchantIds) {
        List<String> months = IntStream.rangeClosed(0, 11)
                .mapToObj(i -> YearMonth.from(fromDate.minusMonths(i)))
                .map(ym -> ym.format(DateTimeFormatter.ofPattern("yyyy-MM")))
                .sorted()
                .toList();

        LocalDate start = fromDate.minusMonths(11).withDayOfMonth(1);
        LocalDate end = YearMonth.from(fromDate).atEndOfMonth();

        return fetchData("TO_CHAR(d_transaction_day, 'yyyy-MM')", start, end, currency, merchantIds)
                .flatMapMany(dataMap -> Flux.fromIterable(months)
                        .map(month -> dataMap.getOrDefault(month, new RevenueTrendDTO(month, BigDecimal.ZERO, 0L))));
    }

    private Mono<Map<String, RevenueTrendDTO>> fetchData(String periodExpression, LocalDate start, LocalDate end,
            String currency, List<String> merchantIds) {
        String sql = String.format("""
                    SELECT %s AS period,
                           COALESCE(SUM(amount), 0) AS transactionVolume,
                           COALESCE(SUM(total_trans), 0) AS transactionCount
                    FROM transaction_report
                    WHERE d_transaction_day BETWEEN :start AND :end
                    AND currency = :currency
                    %s
                    GROUP BY period
                    ORDER BY period
                """, periodExpression,
                merchantIds != null && !merchantIds.isEmpty() ? "AND merchant_id IN (:merchantIds)" : "");

        DatabaseClient.GenericExecuteSpec spec = databaseClient.sql(sql)
                .bind("start", start)
                .bind("end", end)
                .bind("currency", currency);

        if (merchantIds != null && !merchantIds.isEmpty()) {
            spec = spec.bind("merchantIds", merchantIds);
        }

        return spec.map((row, meta) -> {
            String period = row.get("period", String.class);
            BigDecimal volume = row.get("transactionVolume", BigDecimal.class);
            Long count = row.get("transactionCount", Long.class);
            return new RevenueTrendDTO(period, volume, count);
        })
                .all()
                .collectMap(RevenueTrendDTO::getPeriod);
    }

    private String getYearWeek(LocalDate date) {
        int week = date.get(java.time.temporal.WeekFields.ISO.weekOfWeekBasedYear());
        int year = date.get(java.time.temporal.WeekFields.ISO.weekBasedYear());
        return String.format("%d-W%02d", year, week);
    }

    @Override
    public Flux<RevenueMerchantDTO> getRevenueByMerhantId(ServerWebExchange exchange, String fromDateStr,
            String toDateStr, String currency, String merchantId) {
        String fromDate = normalizeDate(fromDateStr);
        String toDate = normalizeDate(toDateStr);

        return merchantService.resolveMerchantIdFilter(exchange, merchantId)
                .flatMapMany(merchantIds -> {
                    if (merchantIds.isEmpty()) {
                        logger.warn("No merchantId matched -> returning empty response");
                        return Flux.<RevenueMerchantDTO>empty();
                    }
                    StringBuilder baseSql = new StringBuilder("""
                                FROM transaction_report
                                WHERE d_transaction_day BETWEEN :fromDate AND :toDate
                            """);

                    if (!merchantIds.isEmpty()) {
                        baseSql.append(" AND merchant_id IN (");
                        for (int i = 0; i < merchantIds.size(); i++) {
                            baseSql.append(":merchant").append(i);
                            if (i < merchantIds.size() - 1)
                                baseSql.append(", ");
                        }
                        baseSql.append(")");
                    }

                    baseSql.append(" AND (:currency IS NULL OR currency = :currency)");

                    // 1. Truy vấn danh sách merchant và revenue
                    String revenueSql = """
                            SELECT merchant_id,
                                   SUM(CASE WHEN transaction_type = 'Purchase' THEN amount ELSE 0 END)
                                 - SUM(CASE WHEN transaction_type = 'Void Purchase' THEN amount ELSE 0 END)
                                 + SUM(CASE WHEN transaction_type = 'Capture' THEN amount ELSE 0 END)
                                 - SUM(CASE WHEN transaction_type = 'Void Capture' THEN amount ELSE 0 END)
                                 - SUM(CASE WHEN transaction_type = 'Refund' THEN amount ELSE 0 END)
                                   AS revenue
                            """ + baseSql + """
                                GROUP BY merchant_id
                                HAVING
                                       SUM(CASE WHEN transaction_type = 'Purchase' THEN amount ELSE 0 END)
                                     - SUM(CASE WHEN transaction_type = 'Void Purchase' THEN amount ELSE 0 END)
                                     + SUM(CASE WHEN transaction_type = 'Capture' THEN amount ELSE 0 END)
                                     - SUM(CASE WHEN transaction_type = 'Void Capture' THEN amount ELSE 0 END)
                                     - SUM(CASE WHEN transaction_type = 'Refund' THEN amount ELSE 0 END)
                                     > 0
                                ORDER BY revenue DESC
                            """;

                    DatabaseClient.GenericExecuteSpec revenueSpec = databaseClient.sql(revenueSql)
                            .bind("fromDate", LocalDate.parse(fromDate))
                            .bind("toDate", LocalDate.parse(toDate))
                            .bind("currency", currency);

                    for (int i = 0; i < merchantIds.size(); i++) {
                        revenueSpec = revenueSpec.bind("merchant" + i, merchantIds.get(i));
                    }

                    Flux<RevenueMerchantDTO> revenueFlux = revenueSpec.map((row, meta) -> {
                        String mid = row.get("merchant_id", String.class);
                        BigDecimal revenue = row.get("revenue", BigDecimal.class);
                        return new RevenueMerchantDTO(mid, revenue, null);
                    }).all();

                    // 2. Truy vấn tổng doanh thu tất cả merchant
                    String totalSql = """
                            SELECT
                                   SUM(CASE WHEN transaction_type = 'Purchase' THEN amount ELSE 0 END)
                                 - SUM(CASE WHEN transaction_type = 'Void Purchase' THEN amount ELSE 0 END)
                                 + SUM(CASE WHEN transaction_type = 'Capture' THEN amount ELSE 0 END)
                                 - SUM(CASE WHEN transaction_type = 'Void Capture' THEN amount ELSE 0 END)
                                 - SUM(CASE WHEN transaction_type = 'Refund' THEN amount ELSE 0 END)
                                   AS total_revenue
                            """ + baseSql;

                    DatabaseClient.GenericExecuteSpec totalSpec = databaseClient.sql(totalSql)
                            .bind("fromDate", LocalDate.parse(fromDate))
                            .bind("toDate", LocalDate.parse(toDate))
                            .bind("currency", currency);

                    for (int i = 0; i < merchantIds.size(); i++) {
                        totalSpec = totalSpec.bind("merchant" + i, merchantIds.get(i));
                    }

                    Mono<BigDecimal> totalRevenueMono = totalSpec.map((row, meta) -> {
                        BigDecimal total = row.get("total_revenue", BigDecimal.class);
                        return total != null ? total : BigDecimal.ZERO;
                    }).one();

                    // 3. Zip lại để tính phần trăm
                    return totalRevenueMono.flatMapMany(totalRevenue -> {
                        if (totalRevenue.compareTo(BigDecimal.ZERO) == 0) {
                            return Flux.<RevenueMerchantDTO>empty();
                        }

                        return revenueFlux.map(dto -> {
                            double percent = dto.getRevenue()
                                    .divide(totalRevenue, 4, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
                                    .doubleValue();
                            dto.setPercent(percent);
                            return dto;
                        });
                    });
                });
    }

    public static String normalizeDate(String fromDate) {
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        try {
            LocalDate date = LocalDate.parse(fromDate, inputFormatter);
            return outputFormatter.format(date);
        } catch (DateTimeParseException e) {
            // Không đúng định dạng dd-MM-yyyy => giữ nguyên
            return fromDate;
        }
    }

}
