package vn.onepay.transaction.service.impl;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.StringReader;
import java.math.BigDecimal;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.sql.CallableStatement;
import java.sql.Clob;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.r2dbc.core.DatabaseClient;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;

import io.r2dbc.spi.Type;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import vn.onepay.transaction.config.ExportFileProperties;
import vn.onepay.transaction.constant.IConstants;
import vn.onepay.transaction.dto.AcquirerDTO;
import vn.onepay.transaction.dto.ActionMessage;
import vn.onepay.transaction.dto.AdvanceInformation;
import vn.onepay.transaction.dto.ApiResponse;
import vn.onepay.transaction.dto.FeeInformation;
import vn.onepay.transaction.dto.FileDownloadDto;
import vn.onepay.transaction.dto.InstallmentDetail;
import vn.onepay.transaction.dto.MSPMerchant;
import vn.onepay.transaction.dto.MerchantInformation;
import vn.onepay.transaction.dto.OrderInformation;
import vn.onepay.transaction.dto.PagedResponse;
import vn.onepay.transaction.dto.PaymentMethod;
import vn.onepay.transaction.dto.PromotionInformation;
import vn.onepay.transaction.dto.RequestRefundResponse;
import vn.onepay.transaction.dto.RiskManagement;
import vn.onepay.transaction.dto.TransactionDTO;
import vn.onepay.transaction.dto.TransactionDetailResponse;
import vn.onepay.transaction.dto.TransactionHistoryDTO;
import vn.onepay.transaction.dto.TransactionInformation;
import vn.onepay.transaction.dto.UposListResponse;
import vn.onepay.transaction.entity.DownloadTaskEntity;
import vn.onepay.transaction.exception.NoItemExistsException;
import vn.onepay.transaction.opensearch.OpenSearchQueryBuilder;
import vn.onepay.transaction.repository.TransactionOpenSearchRepository;
import vn.onepay.transaction.service.AcquirerService;
import vn.onepay.transaction.service.MerchantService;
import vn.onepay.transaction.service.TransactionOldService;
import vn.onepay.transaction.util.AmountUtil;
import vn.onepay.transaction.util.CheckPermissionUtil;
import vn.onepay.transaction.util.DateValidatorUtil;
import vn.onepay.transaction.constant.IConstants;

@Service
public class TransactionOldServiceImpl implements TransactionOldService {
        private static final Logger logger = LoggerFactory.getLogger(TransactionOldServiceImpl.class);
        private static final DateTimeFormatter INPUT_FORMATTER = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
        private static final DateTimeFormatter OUTPUT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
        @Value("${international.url.base}")
        private String internationalUrl;
        @Value("${domestic.url.base}")
        private String domesticUrl;
        @Value("${qr.url.base}")
        private String qrUrl;
        @Value("${vietqr.url.base}")
        private String vietqrUrl;
        @Value("${bnpl.url.base}")
        private String bnplUrl;
        @Value("${directdebit.url.base}")
        private String directDebitUrl;
        @Value("${upos.url.base}")
        private String uposUrl;
        @Value("${opensearch.url.base}")
        private String opensearchUrl;
        @Value("${ma-service.url.base}")
        private String maServiceUrl;
        @Autowired
        private TransactionOpenSearchRepository repository;
        @Autowired
        @Qualifier("oracleJdbcTemplate")
        private JdbcTemplate oracleJdbcTemplate;
        @Autowired
        private DatabaseClient databaseClient;
        @Autowired
        @Qualifier("oracleMerchantPortalJdbcTemplate")
        private JdbcTemplate oracleMerchantPortalJdbcTemplate;

        @Autowired
        private MerchantService merchantService;

        @Autowired
        private AcquirerService acquirerService;
        @Autowired
        private ExportFileProperties exportFileProperties;
        
        
        @Override
        public Mono<String> checkUserExistsInPostgres(String userId) {
        return databaseClient.sql("SELECT ma.get_forward_user_type(:userId) AS type")
                .bind("userId", userId)
                .map((row, meta) -> row.get("type", String.class))
                .one()
                .onErrorResume(e -> {
                logger.error("Lỗi khi truy vấn forward_user: {}", e.getMessage());
                return Mono.just("");
                });
        }

        public Mono<PagedResponse<TransactionDTO>> searchGeneralReport(
                String fromDate, String toDate,  String merchantId,
                String transId, String paymentMethod, String transactionStatus, String transactionType, String orderInfo, String cardNumber, String promotion, String orderSource, String searchType, String searchKeyword, int page, int size,
                String queryMerchantQT, String queryMerchantND, String queryMerchantQR, String queryMerchantVietQr, String queryMerchantDirectDebit) {
                try {
                     return oracleJdbcTemplate.execute((Connection con) -> {
                        CallableStatement cs = null;
                        ResultSet rs = null;
                        try {
                                String finalTransactionTypes = replaceTransactionType(transactionType);
                                String finalTransactionStatus = replaceTransactionStatus(transactionStatus);
                                cs = con.prepareCall("{call search_general_report_v6(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)}");
                                 // IN parameters
                                cs.setString(1, "SELECT");
                                cs.setString(2, transId);
                                cs.setClob(3, new StringReader(queryMerchantQT));
                                cs.setClob(4, new StringReader(queryMerchantND));
                                cs.setClob(5, new StringReader(queryMerchantQR));
                                cs.setClob(6, new StringReader(queryMerchantVietQr));
                                cs.setClob(7, new StringReader(queryMerchantDirectDebit));
                                cs.setString(8, formatDate(fromDate));
                                cs.setString(9, formatDate(toDate));
                                cs.setString(10, "");
                                cs.setString(11, orderInfo);
                                cs.setString(12, "");
                                cs.setString(13, cardNumber);
                                cs.setString(14, finalTransactionTypes);
                                cs.setString(15, finalTransactionStatus);
                                cs.setString(16, "");
                                cs.setString(17, "");
                                cs.setInt(18, page);
                                cs.setInt(19, size);
                                cs.setInt(20, 0);
                                cs.setString(21, "");

                                // OUT params
                                cs.registerOutParameter(22, Types.REF_CURSOR);
                                cs.registerOutParameter(23, Types.NUMERIC);
                                cs.registerOutParameter(24, Types.VARCHAR);
                                cs.setString(25, promotion);
                                cs.setString(26, paymentMethod);
                                cs.setString(27, searchType);
                                cs.setString(28, searchKeyword);

                                cs.execute();

                                rs = (ResultSet) cs.getObject(22);
                                String result = cs.getString(24);
                                logger.info("Result: {}", result);

                                List<TransactionDTO> list = new ArrayList<>();
                                while (rs.next()) {
                                        String state = rs.getString("s_transaction_state");
                                        String type = rs.getString("s_transaction_type");
                                        TransactionDTO dto = new TransactionDTO();
                                        dto.setCardNumber(rs.getString("s_card_number"));
                                        dto.setCurrency(rs.getString("s_currency"));
                                        dto.setMerchantId(rs.getString("s_merchant_id"));
                                        dto.setDocId(null);
                                        dto.setOrderCreatedTime("d_transaction_date");
                                        dto.setOrderReference(rs.getString("s_order_info"));
                                        dto.setOrderSource(null);
                                        dto.setPaymentMethod(IConstants.PAYMENT_METHOD_MAP_IN_LIST.getOrDefault(rs.getString("payment_channel"), rs.getString("payment_channel")));
                                        dto.setPaymentSource(IConstants.PAYMENT_METHOD_MAP_IN_LIST.getOrDefault(rs.getString("payment_channel"), rs.getString("payment_channel")));
                                        dto.setResponseCode(rs.getString("s_response_code"));
                                        dto.setResponseDescription(null);
                                        dto.setTransAmount(rs.getBigDecimal("n_amount"));
                                        dto.setTransCompletedTime("d_transaction_date");
                                        dto.setTransCreatedTime(rs.getString("d_transaction_date"));
                                        dto.setTransStatus(IConstants.TRANSACTION_STATUS_MAP.getOrDefault(state, state));
                                        dto.setTransType(IConstants.TRANSACTION_TYPE_MAP.getOrDefault(type, type));
                                        dto.setTransactionId(rs.getString("s_transaction_id"));
                                        dto.setOriginalTransactionId(rs.getString("s_original_id"));
                                        dto.setMerchantTransactionRef(rs.getString("S_MERCHANT_TRANS_REF"));
                                        list.add(dto);
                                }

                                PagedResponse<TransactionDTO> pagedResponse = PagedResponse.<TransactionDTO>builder()
                                        .currentPage(page)
                                        .pageSize(size)
                                        .totalPages(1)
                                        .items(list)
                                        .build();

                                return Mono.just(pagedResponse);
                        }finally{
                                if (rs != null) try { rs.close(); } catch (SQLException ignored) {}
                                if (cs != null) try { cs.close(); } catch (SQLException ignored) {}
                        }
                       
                });    
                } catch (Exception e) {
                    logger.error("Error searchGeneralReport: ", e);
                    return Mono.just(PagedResponse.<TransactionDTO>builder()
                                .currentPage(0)
                                .pageSize(0)
                                .totalPages(0)
                                .items(null)
                                .build());    
                }
               
        }

        public Mono<List<TransactionDTO>> searchGeneralReportDownload(
                String fromDate, String toDate,  String merchantId,
                String transId, String paymentMethod, String transactionStatus, String transactionType, String orderInfo, String cardNumber, String promotion, String orderSource, String searchType, String searchKeyword, int page, int size,
                String queryMerchantQT, String queryMerchantND, String queryMerchantQR, String queryMerchantVietQr, String queryMerchantDirectDebit) {
                try {
                     return oracleJdbcTemplate.execute((Connection con) -> {
                        CallableStatement cs = null;
                        ResultSet rs = null;
                        try {
                                String finalTransactionTypes = replaceTransactionType(transactionType);
                                String finalTransactionStatus = replaceTransactionStatus(transactionStatus);

                                cs = con.prepareCall("{call search_general_report_v6(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)}");
                                 // IN parameters
                                cs.setString(1, "DOWNLOAD");
                                cs.setString(2, transId);
                                cs.setClob(3, new StringReader(queryMerchantQT));
                                cs.setClob(4, new StringReader(queryMerchantND));
                                cs.setClob(5, new StringReader(queryMerchantQR));
                                cs.setClob(6, new StringReader(queryMerchantVietQr));
                                cs.setClob(7, new StringReader(queryMerchantDirectDebit));
                                cs.setString(8, formatDate(fromDate));
                                cs.setString(9, formatDate(toDate));
                                cs.setString(10, "");
                                cs.setString(11, orderInfo);
                                cs.setString(12, "");
                                cs.setString(13, cardNumber);
                                cs.setString(14, finalTransactionTypes);
                                cs.setString(15, finalTransactionStatus);
                                cs.setString(16, "");
                                cs.setString(17, "");
                                cs.setInt(18, page);
                                cs.setInt(19, size);
                                cs.setInt(20, 0);
                                cs.setString(21, "");

                                // OUT params
                                cs.registerOutParameter(22, Types.REF_CURSOR);
                                cs.registerOutParameter(23, Types.NUMERIC);
                                cs.registerOutParameter(24, Types.VARCHAR);
                                cs.setString(25, promotion);
                                cs.setString(26, paymentMethod);
                                cs.setString(27, searchType);
                                cs.setString(28, searchKeyword);

                                cs.execute();

                                rs = (ResultSet) cs.getObject(22);
                                String result = cs.getString(24);
                                logger.info("Result: {}", result);

                                List<TransactionDTO> list = new ArrayList<>();
                                while (rs.next()) {
                                        String state = rs.getString("s_transaction_state");
                                        String type = rs.getString("s_transaction_type");
                                        TransactionDTO dto = new TransactionDTO();
                                        dto.setCardNumber(rs.getString("s_card_number"));
                                        dto.setCurrency(rs.getString("s_currency"));
                                        dto.setMerchantId(rs.getString("s_merchant_id"));
                                        dto.setDocId(null);
                                        dto.setOrderCreatedTime("d_transaction_date");
                                        dto.setOrderReference(rs.getString("s_order_info"));
                                        dto.setOrderSource(null);
                                        dto.setPaymentMethod(IConstants.PAYMENT_METHOD_MAP_IN_LIST.getOrDefault(rs.getString("payment_channel"), rs.getString("payment_channel")));
                                        dto.setPaymentSource(IConstants.PAYMENT_METHOD_MAP_IN_LIST.getOrDefault(rs.getString("payment_channel"), rs.getString("payment_channel")));
                                        dto.setResponseCode(rs.getString("s_response_code"));
                                        dto.setResponseDescription(null);
                                        dto.setTransAmount(rs.getBigDecimal("n_amount"));
                                        dto.setTransCompletedTime("d_transaction_date");
                                        dto.setTransCreatedTime(rs.getString("d_transaction_date"));
                                        dto.setTransStatus(IConstants.TRANSACTION_STATUS_MAP.getOrDefault(state, state));
                                        dto.setTransType(IConstants.TRANSACTION_TYPE_MAP.getOrDefault(type, type));
                                        dto.setTransactionId(rs.getString("s_transaction_id"));
                                        dto.setOriginalTransactionId(rs.getString("s_original_id"));
                                        dto.setMerchantTransactionRef(rs.getString("S_MERCHANT_TRANS_REF"));
                                        list.add(dto);
                                }

                                return Mono.just(list);
                        }finally{
                                if (rs != null) try { rs.close(); } catch (SQLException ignored) {}
                                if (cs != null) try { cs.close(); } catch (SQLException ignored) {}
                        }
                       
                });    
                } catch (Exception e) {
                    logger.error("Error searchGeneralReportDownload: ", e);
                    return Mono.just(List.of());    
                }
               
        }

        public Mono<Integer> fetchTotalRecords(
                String fromDate, String toDate,  String merchantId,
                String transId, String paymentMethod, String transactionStatus, String transactionType, String orderInfo, String cardNumber, String promotion, String orderSource, String searchType, String searchKeyword,
                int page, int size,
                String queryMerchantQT, String queryMerchantND, String queryMerchantQR, String queryMerchantVietQr, String queryMerchantDirectDebit) {
                
                try {
                       return oracleJdbcTemplate.execute((Connection con) -> {
                                CallableStatement cs = null;
                                ResultSet rs = null;
                                try {
                                        String finalTransactionTypes = replaceTransactionType(transactionType);
                                        String finalTransactionStatus = replaceTransactionStatus(transactionStatus);

                                        cs = con.prepareCall("{call search_general_report_v6(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)}");
                                        cs.setString(1, "TOTAL");
                                        cs.setString(2, transId);
                                        cs.setClob(3, new StringReader(queryMerchantQT));
                                        cs.setClob(4, new StringReader(queryMerchantND));
                                        cs.setClob(5, new StringReader(queryMerchantQR));
                                        cs.setClob(6, new StringReader(queryMerchantVietQr));
                                        cs.setClob(7, new StringReader(queryMerchantDirectDebit));
                                        cs.setString(8, formatDate(fromDate));
                                        cs.setString(9, formatDate(toDate));
                                        cs.setString(10, "");
                                        cs.setString(11, orderInfo);
                                        cs.setString(12, "");
                                        cs.setString(13, cardNumber);
                                        cs.setString(14, finalTransactionTypes);
                                        cs.setString(15, finalTransactionStatus);
                                        cs.setString(16, "");
                                        cs.setString(17, "");
                                        cs.setInt(18, page);
                                        cs.setInt(19, size);
                                        cs.setInt(20, 0);
                                        cs.setString(21, "");
                                        cs.setString(25, promotion);
                                        cs.setString(26, paymentMethod);

                                        // OUT
                                        cs.registerOutParameter(22, Types.REF_CURSOR);
                                        cs.registerOutParameter(23, Types.NUMERIC);
                                        cs.registerOutParameter(24, Types.VARCHAR);
                                        cs.setString(27, searchType);
                                        cs.setString(28, searchKeyword);

                                        cs.execute();
                                        rs = (ResultSet) cs.getObject(22);
                                        int total = 0;
                                        while (rs.next()) {
                                                total = rs.getInt("N_TOTAL");  
                                        }
                                        return Mono.just(total);
                                }finally{
                                        if (rs != null) try { rs.close(); } catch (SQLException ignored) {}
                                        if (cs != null) try { cs.close(); } catch (SQLException ignored) {}
                                }
                              
                        });  
                } catch (Exception e) {
                        logger.error("Error fetchTotalRecords: ", e);
                         return Mono.just(0);
                }

               
        }

        public Mono<BigDecimal> sumAmount(
                String fromDate, String toDate,  String merchantId,
                String transId, String paymentMethod, String transactionStatus, String transactionType, int page, int size,
                String queryMerchantQT, String queryMerchantND, String queryMerchantQR, String queryMerchantVietQr, String queryMerchantDirectDebit) {
                try {
                         return oracleJdbcTemplate.execute((Connection con) -> {
                        CallableStatement cs = null;
                        ResultSet rs = null;
                        try {
                            cs = con.prepareCall("{call search_general_report_v5(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)}");
                            cs.setString(1, "SUM");
                                cs.setString(2, "");
                                cs.setClob(3, new StringReader(queryMerchantQT));
                                cs.setClob(4, new StringReader(queryMerchantND));
                                cs.setClob(5, new StringReader(queryMerchantQR));
                                cs.setClob(6, new StringReader(queryMerchantVietQr));
                                cs.setClob(7, new StringReader(queryMerchantDirectDebit));
                                cs.setString(8, formatDate(fromDate));
                                cs.setString(9, formatDate(toDate));
                                cs.setString(10, "");
                                cs.setString(11, "");
                                cs.setString(12, "");
                                cs.setString(13, "");
                                cs.setString(14, transactionType);
                                cs.setString(15, transactionStatus);
                                cs.setString(16, "");
                                cs.setString(17, "");
                                cs.setInt(18, page);
                                cs.setInt(19, size);
                                cs.setInt(20, 0);
                                cs.setString(21, "");

                                // OUT
                                cs.registerOutParameter(22, Types.REF_CURSOR);
                                cs.registerOutParameter(23, Types.NUMERIC);
                                cs.registerOutParameter(24, Types.VARCHAR);

                                cs.execute();
                                rs = (ResultSet) cs.getObject(22);
                                BigDecimal sumAmount = BigDecimal.ZERO;
                                while (rs.next()) {
                                sumAmount = rs.getBigDecimal("SUM_AMOUNT");
                                if (sumAmount == null) {
                                        sumAmount = BigDecimal.ZERO;
                                }
                                }
                                return Mono.just(sumAmount);    
                        }finally{
                                if (rs != null) try { rs.close(); } catch (SQLException ignored) {}
                                if (cs != null) try { cs.close(); } catch (SQLException ignored) {}
                        }
                 });
                } catch (Exception e) {
                        logger.error("Error sumAmount: ", e);
                        return Mono.just(BigDecimal.ZERO);
                }
               
        }

        private String replaceTransactionStatus(String transactionStatus) {
                if (transactionStatus == null) {
                        return null;
                }

                return transactionStatus.replace("Successful", "Successful,approved,400")
                                .replace("Failed", "Failed,failed,decline")
                                .replace("Processing", "Processing,authorization_required,verified");
        }

        private String replaceTransactionType(String transactionType) {
                if (transactionType == null) {
                        return null;
                }

                return transactionType.replace("Purchase", "Purchase,Pay Later");
        }

        public Mono<ApiResponse<PagedResponse<TransactionDTO>>> callSearchGeneralReport(
                String fromDate,
                String toDate,
                String merchantId,
                String transId,
                String paymentMethod,
                String transactionStatus,
                String transactionType,
                String orderInfo,
                String cardNumber,
                String promotion,
                String orderSource,
                String searchType,
                String searchKeyword,
                int page,
                int size,
                String sortField,
                String sortOrder, String userId) {
                try {
                        logger.info("---------------------start call api getTransactionAll-------------------------");

                        return merchantService.resolveMerchantIdFilter(merchantId)
                                .flatMap(merchantIds -> {
                                        String queryMerchantQT = String.join(",", merchantIds);

                                        // For now, using the same merchant filter for all types
                                        // TODO: Implement proper type-specific filtering
                                        String queryMerchantND = queryMerchantQT;
                                        String queryMerchantQR = queryMerchantQT;
                                        String queryMerchantVietQr = queryMerchantQT;
                                        String queryMerchantDirectDebit = queryMerchantQT;

                                        Mono<PagedResponse<TransactionDTO>> dataMono = searchGeneralReport(
                                                fromDate, toDate, merchantId, transId, paymentMethod, transactionStatus,
                                                transactionType, orderInfo, cardNumber, promotion, orderSource, searchType, searchKeyword, page, size, queryMerchantQT, queryMerchantND,
                                                queryMerchantQR, queryMerchantVietQr, queryMerchantDirectDebit);

                                        Mono<Integer> totalMono = fetchTotalRecords(
                                                fromDate, toDate, merchantId, transId, paymentMethod, transactionStatus,
                                                transactionType, orderInfo, cardNumber, promotion, orderSource, searchType, searchKeyword, page, size, queryMerchantQT, queryMerchantND,
                                                queryMerchantQR, queryMerchantVietQr, queryMerchantDirectDebit);

                                        return Mono.zip(dataMono, totalMono)
                                                .map(tuple -> {
                                                        PagedResponse<TransactionDTO> data = tuple.getT1();
                                                        int total = tuple.getT2();

                                                        int totalPages = (int) Math.ceil((double) total / size);
                                                        data.setTotalPages(totalPages);

                                                        return ApiResponse.<PagedResponse<TransactionDTO>>builder()
                                                                .status("OK")
                                                                .total(total)
                                                                .data(data)
                                                                .build();
                                                });
                                });
                } catch (Exception e) {
                        logger.error("Error callSearchGeneralReport: ", e);
                        return Mono.just(ApiResponse.<PagedResponse<TransactionDTO>>builder()
                                        .status("Fail")
                                        .total(0)
                                        .data(null)
                                        .build()); 
                }finally{
                        logger.info("---------------------end call api getTransactionAll-------------------------");
                }
        }

        public Map<String, List<String>> callGetAllMerchantsByUser(String userId) {
                return oracleMerchantPortalJdbcTemplate.execute((Connection con) -> {
                        CallableStatement cs = null;
                        ResultSet rs = null;
                        try {
                                cs = con.prepareCall("{call get_all_merchants_by_user(?, ?, ?, ?)}");
                                cs.setString(1, userId);

                                // Đăng ký các OUT parameter
                                cs.registerOutParameter(2, Types.REF_CURSOR);
                                cs.registerOutParameter(3, Types.INTEGER);
                                cs.registerOutParameter(4, Types.VARCHAR);

                                cs.execute();

                                rs = (ResultSet) cs.getObject(2);
                                int nResult = cs.getInt(3);
                                String sResult = cs.getString(4);

                                Map<String, List<String>> merchantIdsByType = new HashMap<>();

                                while (rs != null && rs.next()) {
                                String type = rs.getString("S_TYPE");
                                String mchnt = rs.getString("S_MERCHANT_ID");
                                // Nếu đã có type trong map, lấy ra list; nếu chưa có thì tạo mới
                                merchantIdsByType
                                        .computeIfAbsent(type, k -> new ArrayList<>())
                                        .add(mchnt);
                                }

                                return merchantIdsByType;       
                        }finally{
                                if (rs != null) try { rs.close(); } catch (SQLException ignored) {}
                                if (cs != null) try { cs.close(); } catch (SQLException ignored) {}
                        }
                       
                });
        }



       private String formatDate(String dateStr){
        if (dateStr != null && !"".equalsIgnoreCase(dateStr)) {
            OffsetDateTime dateTime = OffsetDateTime.parse(dateStr);   
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy hh:mm a", Locale.ENGLISH);
            return dateTime.format(outputFormatter); 
        }
        return dateStr;
       }

        private String getMerchantIdsByType(String merchantIds, Map<String, List<String>> merchantIdsByType, String type) {
                if (merchantIds == null) {
                   merchantIds = ""; 
                }
                List<String> illigalMerchantsList = merchantIdsByType.containsKey(type)
                        ? merchantIdsByType.get(type)
                        : new ArrayList<>();

                List<String> finalMerchantsList = new ArrayList<String>();

                String[] splitted = merchantIds.trim().split("\\s*,\\s*");

                List<String> requestMerchantsList = new ArrayList<String>(Arrays.asList(splitted));

                if ("".equals(merchantIds)) {
                finalMerchantsList = illigalMerchantsList;
                } else {
                if (!illigalMerchantsList.isEmpty() && "ALL".equals(illigalMerchantsList.get(0))) {
                        finalMerchantsList = requestMerchantsList;
                } else {
                        requestMerchantsList.retainAll(illigalMerchantsList);
                        finalMerchantsList = requestMerchantsList;
                }
                }
                String finalMerchants = String.join(",", finalMerchantsList);

                return finalMerchants;
        }

        @Override
        public TransactionDetailResponse getTransactionDetail(String transactionId, String paymentMethod, String transactionType, String paymentChannel, String xUserId) {
                TransactionDetailResponse detail = getTranDetail(transactionId, paymentMethod, transactionType, paymentChannel, xUserId);
                if (detail == null) {
                        return null;
                }
                
                TransactionDetailResponse advanceFee = getAdvanceFeeInfo(detail);
                if (advanceFee != null) {
                        detail.setAdvanceInformation(advanceFee.getAdvanceInformation());
                        detail.setFeeInformation(advanceFee.getFeeInformation());
                }

                return normalizeTransactionStatuses(detail);
        }

        private TransactionDetailResponse normalizeTransactionStatuses(TransactionDetailResponse detail) {
                TransactionInformation transactionInformation = detail.getTransactionInformation();
                if (transactionInformation == null || transactionInformation.getTransactionStatus() == null) {
                        return detail;
                }
                
                String transactionStatus = transactionInformation.getTransactionStatus();
                transactionInformation.setTransactionStatus(IConstants.TRANSACTION_STATUS_MAP.getOrDefault(transactionStatus, transactionStatus));
                return detail;
        }

        private List<TransactionHistoryDTO> normalizeTransactionHistoryStatuses(List<TransactionHistoryDTO> history) {
                return history.stream()
                        .map(dto -> {
                                dto.setTransactionType(IConstants.TRANSACTION_TYPE_MAP.getOrDefault(dto.getTransactionType(), dto.getTransactionType()));
                                dto.setStatus(IConstants.TRANSACTION_STATUS_MAP.getOrDefault(dto.getStatus(), dto.getStatus()));
                                return dto;
                        }).toList();
        }

        public TransactionDetailResponse getTranDetail(String transactionId, String paymentMethod, String transactionType, String paymentChannel, String xUserId) {
                try {
                        logger.info("REQUEST: transactionId="+transactionId+" ,paymentMethod="+paymentMethod+" ,transactionType="+transactionType+" ,xUserId="+xUserId);
                        if (IConstants.PAYMENT_METHOD_QT.equalsIgnoreCase(paymentMethod) || 
                                IConstants.PAYMENT_METHOD_SS_PAY.equalsIgnoreCase(paymentMethod) || 
                                IConstants.PAYMENT_METHOD_GG_PAY.equalsIgnoreCase(paymentMethod) || 
                                IConstants.PAYMENT_METHOD_AP_PAY.equalsIgnoreCase(paymentMethod) || 
                                IConstants.PAYMENT_METHOD_INSTALLMENT.equalsIgnoreCase(paymentMethod)) {
                                String opsStrData = callApiGetTransDetail(internationalUrl + "/international/transaction/" + transactionId, xUserId);
                                ObjectMapper mapper = new ObjectMapper();
                                mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                                JsonNode root = mapper.readTree(opsStrData);
                                if (root == null || !root.has("transaction_id")){
                                        throw new NoItemExistsException("Transaction purchase with transactionId " + transactionId + " not found");
                                }
                                TransactionDetailResponse responsePurchase = mapJsonPurchaseIntenational(root, paymentMethod);
                                logger.info("RESPONSE PURCHASE INTERNATIONAL: "+responsePurchase);
                                if ("Void".equalsIgnoreCase(transactionType) || "Refund".equalsIgnoreCase(transactionType)) {
                                        opsStrData = callApiGetTransDetail(internationalUrl + "/international/transaction/void/" + transactionId, xUserId);
                                        ObjectMapper mapperVoid = new ObjectMapper();
                                        mapperVoid.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                                        root = mapperVoid.readTree(opsStrData);
                                        if (root == null || (!root.has("tranaction_id") && !root.has("transaction_id"))){
                                                throw new NoItemExistsException("Transaction void/refun with transactionId " + transactionId + " not found");
                                        }
                                     TransactionDetailResponse responseVoid = mapJsonVoidIntenational(root, paymentMethod);
                                     responseVoid.setPaymentMethod(responsePurchase.getPaymentMethod() != null ? responsePurchase.getPaymentMethod() : null);
                                     responseVoid.setRiskManagement(responsePurchase.getRiskManagement() != null ? responsePurchase.getRiskManagement() : null);
                                     logger.info("RESPONSE VOID INTERNATIONAL: "+responseVoid);
                                     return responseVoid;
                                } else if( "Request Refund".equalsIgnoreCase(transactionType)){
                                      opsStrData = callApiGetTransDetail(internationalUrl + "/international/transaction/refund/" + transactionId, xUserId);
                                        ObjectMapper mapperRequestRefun = new ObjectMapper();
                                        mapperRequestRefun.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                                        root = mapperRequestRefun.readTree(opsStrData);
                                        if (root == null || !root.has("transaction_id")){
                                                throw new NoItemExistsException("Transaction void/refun with transactionId " + transactionId + " not found");
                                        }      
                                     TransactionDetailResponse responseRefund = mapJsonRequestRefundIntenational(root, paymentMethod);
                                     responseRefund.setRiskManagement(responsePurchase.getRiskManagement() != null ? responsePurchase.getRiskManagement() : null);
                                     logger.info("RESPONSE REFUND INTERNATIONAL: "+responseRefund);
                                     return responseRefund;
                                }else{
                                        return responsePurchase;
                                }
                        } else if ("ND".equalsIgnoreCase(paymentMethod)) {
                                if( "Purchase".equalsIgnoreCase(transactionType) || "Request Refund".equalsIgnoreCase(transactionType)){
                                        String opsStrData = callApiGetTransDetail(domesticUrl + "/domestic/transaction/" + transactionId, xUserId);
                                        ObjectMapper mapper = new ObjectMapper();
                                        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                                        JsonNode root = mapper.readTree(opsStrData);
                                        if (root == null || !root.has("transaction_id")){
                                                throw new NoItemExistsException("Transaction purchase with transactionId " + transactionId + " not found");
                                        }
                                        TransactionDetailResponse responsePurchase = mapJsonPurchaseDomastic(root, paymentMethod);
                                        logger.info("RESPONSE PURCHASE DOMESTIC: "+responsePurchase);
                                        return responsePurchase;
                                }else{
                                      String opsStrData = callApiGetTransDetail(domesticUrl + "/domestic/refund/" + transactionId, xUserId);
                                        ObjectMapper mapper = new ObjectMapper();
                                        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                                        JsonNode root = mapper.readTree(opsStrData);
                                        if (root == null || !root.has("transaction_id")){
                                                throw new NoItemExistsException("Transaction refund with transactionId " + transactionId + " not found");
                                        }
                                        TransactionDetailResponse responseRefund = mapJsonPurchaseDomastic(root, paymentMethod);
                                        logger.info("RESPONSE REFUND DOMESTIC: "+responseRefund);
                                        return responseRefund;  
                                }
                        } else if ("QR".equalsIgnoreCase(paymentMethod)) {
                                if( "Refund".equalsIgnoreCase(transactionType) || "Request Refund".equalsIgnoreCase(transactionType) || "Purchase".equalsIgnoreCase(transactionType)){
                                        String opsStrData = callApiGetTransDetail(qrUrl + "/qr/transaction/" + transactionId, xUserId);
                                        ObjectMapper mapper = new ObjectMapper();
                                        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                                        JsonNode root = mapper.readTree(opsStrData);
                                        if (root == null || !root.has("transactionId")){
                                                throw new NoItemExistsException("Transaction refund with transactionId " + transactionId + " not found");
                                        }
                                        TransactionDetailResponse responseRefund = mapJsonAllQr(root, paymentMethod);
                                        logger.info("RESPONSE QR={}, transactionType={}, transactionId={}: "+responseRefund, transactionType, transactionId);
                                        return responseRefund;  
                                } else{
                                         String opsStrData = callApiGetTransDetail(qrUrl + "/qr/moca/transaction/" + transactionId, xUserId);
                                        ObjectMapper mapper = new ObjectMapper();
                                        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                                        JsonNode root = mapper.readTree(opsStrData);
                                        if (root == null || !root.has("transactionId")){
                                                throw new NoItemExistsException("Transaction refund with transactionId " + transactionId + " not found");
                                        }
                                        TransactionDetailResponse responseRefund = mapJsonAllQr(root, paymentMethod);
                                        logger.info("RESPONSE QR={}, transactionType={}, transactionId={}: "+responseRefund, transactionType, transactionId);
                                        return responseRefund;  
                                }
                        } else if ("VIETQR".equalsIgnoreCase(paymentMethod)) {
                                String opsStrData = callApiGetTransDetail(vietqrUrl + "/vietqr/transaction/" + transactionId, xUserId);
                                 ObjectMapper mapper = new ObjectMapper();
                                 mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                                JsonNode root = mapper.readTree(opsStrData);
                                if (root == null || !root.has("transaction_id")){
                                        throw new NoItemExistsException("Transaction refund with transactionId " + transactionId + " not found");
                                }
                                TransactionDetailResponse responseRefund = mapJsonAllVietQr(root, paymentMethod);
                                logger.info("RESPONSE VIETQR: "+responseRefund);
                                return responseRefund;  
                        } else if ("BNPL".equalsIgnoreCase(paymentMethod)) {
                                if( "Refund".equalsIgnoreCase(transactionType)){
                                        String opsStrData = callApiGetTransDetail(bnplUrl + "/bnpl/transaction/refund/" + transactionId, xUserId);
                                        ObjectMapper mapper = new ObjectMapper();
                                        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                                        JsonNode root = mapper.readTree(opsStrData);
                                        if (root == null || !root.has("transactionId")){
                                                throw new NoItemExistsException("Transaction refund with transactionId " + transactionId + " not found");
                                        }
                                        TransactionDetailResponse responseRefund = mapJsonAllBNPL(root, paymentMethod);
                                        logger.info("RESPONSE REFUND BNPL: "+responseRefund);
                                        return responseRefund;  
                                } else if( "Request Refund".equalsIgnoreCase(transactionType)){
                                        String opsStrData = callApiGetTransDetail(bnplUrl + "/bnpl/transaction/request-refund/" + transactionId, xUserId);
                                        ObjectMapper mapper = new ObjectMapper();
                                        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                                        JsonNode root = mapper.readTree(opsStrData);
                                        if (root == null || !root.has("transactionId")){
                                                throw new NoItemExistsException("Transaction refund with transactionId " + transactionId + " not found");
                                        }
                                        TransactionDetailResponse responseRequestRefund = mapJsonAllBNPL(root, paymentMethod);
                                        logger.info("RESPONSE REQUEST REFUND BNPL: "+responseRequestRefund);
                                        return responseRequestRefund;  
                                }else{
                                         String opsStrData = callApiGetTransDetail(bnplUrl + "/bnpl/transaction/" + transactionId, null);
                                        ObjectMapper mapper = new ObjectMapper();
                                        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                                        JsonNode root = mapper.readTree(opsStrData);
                                        if (root == null || !root.has("transactionId")){
                                                throw new NoItemExistsException("Transaction refund with transactionId " + transactionId + " not found");
                                        }
                                        TransactionDetailResponse responseRefund = mapJsonAllBNPL(root, paymentMethod);
                                        logger.info("RESPONSE PURCHASE BNPL: "+responseRefund);
                                        return responseRefund;  
                                }
                        } else if ("DD".equalsIgnoreCase(paymentMethod)) {
                                String opsStrData = callApiGetTransDetail(directDebitUrl + "/direct-debit/transaction/" + transactionId, xUserId);
                                ObjectMapper mapper = new ObjectMapper();
                                mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                                JsonNode root = mapper.readTree(opsStrData);
                                if (root == null || !root.has("transaction_id")){
                                        throw new NoItemExistsException("Transaction refund with transactionId " + transactionId + " not found");
                                }
                                TransactionDetailResponse responseRefund = mapJsonAllDirectDebit(root, paymentMethod);
                                logger.info("RESPONSE DIRECT-DEBIT: "+responseRefund);
                                return responseRefund;
                        } else if ("UPOS".equalsIgnoreCase(paymentMethod)) {
                                String key = transactionType +"_"+ paymentChannel;
                                String base64TransId =  Base64.getEncoder().encodeToString(transactionId.getBytes());
                                String opsStrData = null;
                                ObjectMapper mapper = new ObjectMapper();
                                mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                                JsonNode root = null;
                                TransactionDetailResponse responseRefund = null;
                                switch (key) {
                                        case "Purchase_CARD":
                                        case "Void_CARD":
                                                opsStrData = callApiGetTransDetail(uposUrl + "/upos/card-payment-purchase/" + base64TransId, xUserId);
                                                root = mapper.readTree(opsStrData);
                                                if (root == null || !root.has("data")){
                                                        throw new NoItemExistsException("Transaction refund with base64 skey " + base64TransId + " not found");
                                                }
                                                responseRefund = mapJsonAllUpos(root, paymentMethod);
                                                break;
                                        case "Request Refund_CARD":
                                                opsStrData = callApiGetTransDetail(uposUrl + "/upos/card-payment-request-refund/" + base64TransId, xUserId);
                                                root = mapper.readTree(opsStrData);
                                                if (root == null || !root.has("data")){
                                                        throw new NoItemExistsException("Transaction refund with base64 skey " + base64TransId + " not found");
                                                }
                                                responseRefund = mapJsonAllUpos(root, paymentMethod);
                                                break;
                                        case "Refund_CARD":
                                                opsStrData = callApiGetTransDetail(uposUrl + "/upos/card-payment-refund/" + base64TransId, xUserId);
                                                root = mapper.readTree(opsStrData);
                                                if (root == null || !root.has("data")){
                                                        throw new NoItemExistsException("Transaction refund with base64 skey " + base64TransId + " not found");
                                                }
                                                responseRefund = mapJsonAllUpos(root, paymentMethod);
                                                break;
                                        case "Purchase_QR":
                                        case "Purchase_VIETQR":
                                        case "Refund_QR":
                                        case "Refund_VIETQR":
                                        case "Request Refund_QR":
                                        case "Request Refund_VIETQR":
                                                opsStrData = callApiGetTransDetail(uposUrl + "/upos/qr-payment-purchase/" + base64TransId, xUserId);
                                                root = mapper.readTree(opsStrData);
                                                if (root == null || !root.has("data")){
                                                        throw new NoItemExistsException("Transaction refund with base64 skey " + base64TransId + " not found");
                                                }
                                                responseRefund = mapJsonAllUpos(root, paymentMethod);
                                                break;  
                                        case "Purchase_INSTALLMENT":
                                        case "Void_INSTALLMENT":
                                        case "Refund_INSTALLMENT":
                                        case "Request Refund_INSTALLMENT":
                                                opsStrData = callApiGetTransDetail(uposUrl + "/upos/ita-payment-purchase/" + base64TransId, xUserId);
                                                root = mapper.readTree(opsStrData);
                                                if (root == null || !root.has("data")){
                                                        throw new NoItemExistsException("Transaction refund with base64 skey " + base64TransId + " not found");
                                                }
                                                responseRefund = mapJsonAllUpos(root, paymentMethod);
                                                break;
                                        default:
                                                logger.info("UPOS DONT HAVE CASE FOR TRANSACTION_TYPE AND PAYMENT_METHOD"+key);
                                                break;
                                }
                                logger.info("RESPONSE UPOS DETAIL: "+responseRefund);
                                return responseRefund;
                        } else {
                                logger.info("Dont have payment method: "+paymentMethod);
                             return null;   
                        }
                } catch (NoItemExistsException e) {
                        logger.error("Get transaction detail not exists: ", e);
                        throw e;
                } catch (Exception e) {
                        logger.error("Error getTransactionDetail: ", e);
                }finally{
                        logger.info("---------------end getTransactionDetail-----------");
                }
                return null;
        }

         private TransactionDetailResponse mapJsonPurchaseIntenational(JsonNode source, String paymentMethod) {
                try {
                        logger.info("Start mapJsonPurchaseIntenational...");
                        JsonNode acquirerJson = source.path("acquirer");
                        JsonNode amountJson = source.path("amount");
                        JsonNode cardJson = source.path("card");
                        JsonNode avsJson = source.path("avs");

                        OrderInformation orderInformation = OrderInformation.builder()
                                        .orderCreatedTime(convertDate(safeText(source, "transaction_time")))
                                        .orderReference(safeText(source, "order_info"))
                                        .orderCurrency(safeText(amountJson, "currency"))
                                        .build();
                        String amountStr = safeText(source, "amount", "total");
                        if (amountStr != null) {
                                orderInformation.setOrderAmount(new BigDecimal(amountStr));
                        }

                        MerchantInformation merchantInformation = MerchantInformation.builder()
                                        .merchantId(safeText(source, "merchant_id"))
                                        .merchantName(safeText(source, "s_name"))
                                        .build();

                        // paymentMethod logic
                       // String paymentMethod = safeText(source, "transaction_type");
                        if ("QT".equalsIgnoreCase(paymentMethod)) {
                                String itaState = safeText(source, "installment_status");
                                String s_e_source = safeText(source, "source");
                                if (itaState != null && !itaState.isEmpty())
                                        paymentMethod = IConstants.PAYMENT_METHOD_INSTALLMENT;
                                if (s_e_source != null && !"Direct".equalsIgnoreCase(s_e_source))
                                        paymentMethod = s_e_source;
                        }
                        TransactionInformation transactionInformation = TransactionInformation.builder()
                                        .transactionId(safeText(source, "transaction_id"))
                                        .parentTransactionId(safeText(source, "original_transaction_id"))
                                        .transactionType(safeText(source, "transaction_type"))
                                        .paymentMethod(paymentMethod)
                                        .transactionCreatedTime(convertDate(safeText(source, "transaction_time")))
                                        .transactionCompletedTime(convertDate(safeText(source, "transaction_time")))
                                        .transactionCurrency(safeText(amountJson, "currency"))
                                        .transactionStatus(safeText(source, "advance_status"))
                                        .responseCode(safeText(source, "response_code"))
                                        .merchantTransRef(safeText(source, "transaction_reference"))
                                        .installmentStatus(safeText(source, "installment_status"))
                                        .build();

                        String transAmount = safeText(amountJson, "total");
                        if (amountStr != null) {
                                transactionInformation.setTransactionAmount(new BigDecimal(transAmount));
                        }

                        InstallmentDetail installmentDetail = InstallmentDetail.builder()
                                        .priceDifference(BigDecimal.ZERO)
                                        .period(safeText(source, "installment_time"))
                                        .amountMonthly(new BigDecimal(safeText(source, "installment_monthly_amount")))
                                        .build();

                        PaymentMethod paymentMethodInfo = PaymentMethod.builder()
                                        .cardNumber(safeText(cardJson, "card_number"))
                                        .cardBrand(safeText(cardJson,  "card_brand"))
                                        .cardType(safeText(cardJson,  "card_type"))
                                        .cardExpiry(safeText(cardJson, "card_date", "month") + safeText(cardJson, "card_date", "year"))
                                        .issuer(safeText(acquirerJson, "acquirer_name"))
                                        .acquirer(safeText(acquirerJson, "acquirer_id"))
                                        .nameOnCard(safeText(cardJson, "name_on_card"))
                                        .cscResultCode(safeText(source, "s_cscresult_code"))
                                        .authorizationCode(safeText(source, "authentication", "authorisation_code"))
                                        .build();
                        FeeInformation feeInformation = FeeInformation.builder()
                                        .build();
                        AdvanceInformation advanceInformation = AdvanceInformation.builder()
                                        .build();
                        RiskManagement riskManagement = RiskManagement.builder()
                                        .ipAddress(safeText(source, "ip_address"))
                                        .ipProxy(safeText(source, "ip_proxy"))
                                        .ipCountry(safeText(source, "ip_address"))
                                        .binCountry(safeText(source, "bin_country"))
                                        .riskAssessment(safeText(source, "risk_assesment"))
                                        .build();
                        PromotionInformation promotionInformation = PromotionInformation.builder()
                                        .promotionCode(safeText(source, "s_id"))
                                        .promotionName(safeText(source, "s_name"))
                                        .discountCurrency(safeText(source, "qlCurrency"))
                                        .build();
                        if (promotionInformation.getPromotionCode() != null
                                        && !promotionInformation.getPromotionCode().isEmpty()) {
                                promotionInformation.setDiscountAmount(orderInformation.getOrderAmount()
                                                .subtract(transactionInformation.getTransactionAmount()));
                        }

                        TransactionDetailResponse dto = TransactionDetailResponse.builder()
                                        .transactionInformation(transactionInformation)
                                        .orderInformation(orderInformation)
                                        .merchantInformation(merchantInformation)
                                        .paymentMethod(paymentMethodInfo)
                                        .feeInformation(feeInformation)
                                        .advanceInformation(advanceInformation)
                                        .riskManagement(riskManagement)
                                        .promotionInformation(promotionInformation)
                                        .installmentDetail(installmentDetail)
                                        .build();
                        logger.info("End mapJsonPurchaseIntenational...");
                        return dto;

                } catch (Exception e) {
                        logger.error("Error mapJsonPurchaseIntenational: ", e);
                        return null;
                }
        }

        private TransactionDetailResponse mapJsonVoidIntenational(JsonNode source, String paymentMethod) {
                try {
                        logger.info("Start mapJsonVoidIntenational...");
                        JsonNode amountJson = source.path("amount");

                        OrderInformation orderInformation = OrderInformation.builder()
                                        .orderCreatedTime(convertDate(safeText(source, "transaction_time")))
                                        .orderReference(safeText(source, "order_ref"))
                                        .orderCurrency(safeText(amountJson, "currency"))
                                        .build();
                        String amountStr = safeText(source, "amount", "total");
                        if (amountStr != null) {
                                orderInformation.setOrderAmount(new BigDecimal(amountStr));
                        }

                        MerchantInformation merchantInformation = MerchantInformation.builder()
                                        .merchantId(safeText(source, "merchant_id"))
                                        .merchantName(safeText(source, "s_name"))
                                        .build();

                        // paymentMethod logic
                       // String paymentMethod = safeText(source, "transaction_type");
                        if ("QT".equalsIgnoreCase(paymentMethod)) {
                                String itaState = safeText(source, "installment_status");
                                String s_e_source = safeText(source, "source");
                                if (itaState != null && !itaState.isEmpty())
                                        paymentMethod = IConstants.PAYMENT_METHOD_INSTALLMENT;
                                if (s_e_source != null && !"Direct".equalsIgnoreCase(s_e_source))
                                        paymentMethod = s_e_source;
                        }
                        String parentId = safeText(source, "tranaction_id") != null?safeText(source, "tranaction_id"): safeText(source, "transaction_id");
                        TransactionInformation transactionInformation = TransactionInformation.builder()
                                        .transactionId(safeText(source, "id"))
                                        .parentTransactionId(parentId)
                                        .transactionType(safeText(source, "trans_type"))
                                        .paymentMethod(paymentMethod)
                                        .transactionCreatedTime(convertDate(safeText(source, "transaction_time")))
                                        .transactionCompletedTime(convertDate(safeText(source, "transaction_time")))
                                        .transactionCurrency(safeText(amountJson, "currency"))
                                        .transactionStatus(safeText(source, "transaction_status"))
                                        .responseCode(safeText(source, "response_code"))
                                        .merchantTransRef(safeText(source, "trans_ref"))
                                        .installmentStatus(safeText(source, "installment_status"))
                                        .build();

                        String transAmount = safeText(amountJson, "refund_total");
                        if (amountStr != null) {
                                transactionInformation.setTransactionAmount(new BigDecimal(transAmount));
                        }

                        PaymentMethod paymentMethodInfo = PaymentMethod.builder()
                                        .cardNumber(safeText(source, "card_number"))
                                        .cardBrand(safeText(source,  "card_brand"))
                                        .cardType(safeText(source,  "card_type"))
                                        .cardExpiry(safeText(source, "card", "card_date", "year"))
                                        .issuer(safeText(source, "acquirer"))
                                        .acquirer(safeText(source, "acquirer"))
                                        .nameOnCard(safeText(source, "name_on_card"))
                                        .cscResultCode(safeText(source, "s_cscresult_code"))
                                        .authorizationCode(safeText(source, "authentication", "authorisation_code"))
                                        .build();
                        FeeInformation feeInformation = FeeInformation.builder()
                                        .build();
                        AdvanceInformation advanceInformation = AdvanceInformation.builder()
                                        .build();
                        RiskManagement riskManagement = RiskManagement.builder()
                                        .ipAddress(safeText(source, "ip_address"))
                                        .ipProxy(safeText(source, "ip_proxy"))
                                        .ipCountry(safeText(source, "ip_address"))
                                        .binCountry(safeText(source, "bin_country"))
                                        .riskAssessment(safeText(source, "risk_assesment"))
                                        .build();
                        PromotionInformation promotionInformation = PromotionInformation.builder()
                                        .promotionCode(safeText(source, "s_id"))
                                        .promotionName(safeText(source, "s_name"))
                                        .discountCurrency(safeText(source, "qlCurrency"))
                                        .build();
                        if (promotionInformation.getPromotionCode() != null
                                        && !promotionInformation.getPromotionCode().isEmpty()) {
                                promotionInformation.setDiscountAmount(orderInformation.getOrderAmount()
                                                .subtract(transactionInformation.getTransactionAmount()));
                        }

                        TransactionDetailResponse dto = TransactionDetailResponse.builder()
                                        .transactionInformation(transactionInformation)
                                        .orderInformation(orderInformation)
                                        .merchantInformation(merchantInformation)
                                        .paymentMethod(paymentMethodInfo)
                                        .feeInformation(feeInformation)
                                        .advanceInformation(advanceInformation)
                                        .riskManagement(riskManagement)
                                        .promotionInformation(promotionInformation)
                                        .build();
                        logger.info("End mapJsonVoidIntenational...");
                        return dto;

                } catch (Exception e) {
                        logger.error("Error mapJsonVoidIntenational: ", e);
                        return null;
                }
        }

        private TransactionDetailResponse mapJsonRequestRefundIntenational(JsonNode source, String paymentMethod) {
                try {
                        logger.info("Start mapJsonRequestRefundIntenational...");
                        JsonNode acquirerJson = source.path("acquirer");
                        JsonNode amountJson = source.path("amount");
                        JsonNode cardJson = source.path("card");
                        JsonNode avsJson = source.path("avs");

                        OrderInformation orderInformation = OrderInformation.builder()
                                        .orderCreatedTime(convertDate(safeText(source, "transaction_time")))
                                        .orderReference(safeText(source, "order_info"))
                                        .orderCurrency(safeText(amountJson, "currency"))
                                        .build();
                        String amountStr = safeText(source, "amount", "purchase_total");
                        if (amountStr != null) {
                                orderInformation.setOrderAmount(new BigDecimal(amountStr));
                        }

                        MerchantInformation merchantInformation = MerchantInformation.builder()
                                        .merchantId(safeText(source, "merchant_id"))
                                        .merchantName(safeText(source, "s_name"))
                                        .build();

                        // paymentMethod logic
                       // String paymentMethod = safeText(source, "transaction_type");
                        if ("QT".equalsIgnoreCase(paymentMethod)) {
                                String itaState = safeText(source, "installment_status");
                                String s_e_source = safeText(source, "source");
                                if (itaState != null && !itaState.isEmpty())
                                        paymentMethod = IConstants.PAYMENT_METHOD_INSTALLMENT;
                                if (s_e_source != null && !"Direct".equalsIgnoreCase(s_e_source))
                                        paymentMethod = s_e_source;
                        }
                        TransactionInformation transactionInformation = TransactionInformation.builder()
                                        .transactionId(safeText(source, "transaction_id"))
                                        .parentTransactionId(safeText(source, "original_transaction_id"))
                                        .transactionType(safeText(source, "transaction_type"))
                                        .paymentMethod(paymentMethod)
                                        .transactionCreatedTime(convertDate(safeText(source, "transaction_time")))
                                        .transactionCompletedTime(convertDate(safeText(source, "transaction_time")))
                                        .transactionCurrency(safeText(amountJson, "currency"))
                                        .transactionStatus(safeText(source, "transaction_status"))
                                        .responseCode(safeText(source, "response_code"))
                                        .merchantTransRef(safeText(source, "transaction_reference"))
                                        .installmentStatus(safeText(source, "installment_status"))
                                        .build();

                        String transAmount = safeText(amountJson, "total");
                        if (amountStr != null) {
                                transactionInformation.setTransactionAmount(new BigDecimal(transAmount));
                        }

                        PaymentMethod paymentMethodInfo = PaymentMethod.builder()
                                        .cardNumber(safeText(cardJson, "card_number"))
                                        .cardBrand(safeText(cardJson,  "card_brand"))
                                        .cardType(safeText(cardJson,  "card_type"))
                                        .cardExpiry(safeText(cardJson, "card", "card_date", "year"))
                                        .issuer(safeText(acquirerJson, "acquirer_name"))
                                        .acquirer(safeText(acquirerJson, "acquirer_id"))
                                        .nameOnCard(safeText(cardJson, "name_on_card"))
                                        .cscResultCode(safeText(source, "s_cscresult_code"))
                                        .authorizationCode(safeText(source, "authentication", "authorisation_code"))
                                        .build();
                        FeeInformation feeInformation = FeeInformation.builder()
                                        .build();
                        AdvanceInformation advanceInformation = AdvanceInformation.builder()
                                        .build();
                        RiskManagement riskManagement = RiskManagement.builder()
                                        .ipAddress(safeText(source, "ticket_number"))
                                        .ipProxy(safeText(source, "ip_proxy"))
                                        .ipCountry(safeText(source, "ip_address"))
                                        .binCountry(safeText(source, "bin_country"))
                                        .riskAssessment(safeText(source, "risk_assesment"))
                                        .build();
                        PromotionInformation promotionInformation = PromotionInformation.builder()
                                        .promotionCode(safeText(source, "s_id"))
                                        .promotionName(safeText(source, "s_name"))
                                        .discountCurrency(safeText(source, "qlCurrency"))
                                        .build();
                        if (promotionInformation.getPromotionCode() != null
                                        && !promotionInformation.getPromotionCode().isEmpty()) {
                                promotionInformation.setDiscountAmount(orderInformation.getOrderAmount()
                                                .subtract(transactionInformation.getTransactionAmount()));
                        }

                        TransactionDetailResponse dto = TransactionDetailResponse.builder()
                                        .transactionInformation(transactionInformation)
                                        .orderInformation(orderInformation)
                                        .merchantInformation(merchantInformation)
                                        .paymentMethod(paymentMethodInfo)
                                        .feeInformation(feeInformation)
                                        .advanceInformation(advanceInformation)
                                        .riskManagement(riskManagement)
                                        .promotionInformation(promotionInformation)
                                        .build();
                        logger.info("End mapJsonRequestRefundIntenational...");
                        return dto;

                } catch (Exception e) {
                        logger.error("Error mapJsonRequestRefundIntenational: ", e);
                        return null;
                }
        }

        private TransactionDetailResponse mapJsonPurchaseDomastic(JsonNode source, String paymentMethod) {
                try {
                        logger.info("Start mapJsonPurchaseDomastic...");
                        JsonNode acquirerJson = source.path("acquirer");
                        JsonNode amountJson = source.path("amount");
                        JsonNode cardJson = source.path("card");
                        JsonNode avsJson = source.path("avs");

                        OrderInformation orderInformation = OrderInformation.builder()
                                        .orderCreatedTime(convertDate(safeText(source, "transaction_time")))
                                        .orderReference(safeText(source, "order_info"))
                                        .orderCurrency(safeText(amountJson, "currency"))
                                        .build();
                        String amountStr = safeText(source, "amount", "total");
                        if (amountStr != null) {
                                orderInformation.setOrderAmount(new BigDecimal(amountStr));
                        }

                        MerchantInformation merchantInformation = MerchantInformation.builder()
                                        .merchantId(safeText(source, "merchant_id"))
                                        .merchantName(safeText(source, "s_name"))
                                        .build();

                        TransactionInformation transactionInformation = TransactionInformation.builder()
                                        .transactionId(safeText(source, "transaction_id"))
                                        .parentTransactionId(safeText(source, "original_id"))
                                        .transactionType(safeText(source, "transaction_type"))
                                        .paymentMethod(paymentMethod)
                                        .transactionCreatedTime(convertDate(safeText(source, "transaction_time")))
                                        .transactionCompletedTime(convertDate(safeText(source, "transaction_time")))
                                        .transactionCurrency(safeText(amountJson, "currency"))
                                        .transactionStatus(safeText(source, "advance_status"))
                                        .responseCode(safeText(source, "response_code"))
                                        .merchantTransRef(safeText(source, "merchant_transaction_ref"))
                                        .build();

                        String transAmount = safeText(amountJson, "total");
                        if (amountStr != null) {
                                transactionInformation.setTransactionAmount(new BigDecimal(transAmount));
                        }

                        PaymentMethod paymentMethodInfo = PaymentMethod.builder()
                                        .cardNumber(safeText(cardJson, "card_number"))
                                        .cardBrand(safeText(cardJson,  "card_brand"))
                                        .cardType(safeText(cardJson,  "card_type"))
                                        .cardExpiry(safeText(cardJson, "card_date", "month") + safeText(cardJson, "card_date", "year"))
                                        .issuer(safeText(acquirerJson, "acquirer_name"))
                                        .nameOnCard(safeText(cardJson, "card_holder_name"))
                                        .cscResultCode(safeText(source, "s_cscresult_code"))
                                        .authorizationCode(safeText(source, "authentication", "authorisation_code"))
                                        .build();
                        FeeInformation feeInformation = FeeInformation.builder()
                                        .build();
                        AdvanceInformation advanceInformation = AdvanceInformation.builder()
                                        .build();
                        RiskManagement riskManagement = RiskManagement.builder()
                                        .ipAddress(safeText(source, "ip_address"))
                                        .ipProxy(safeText(source, "ip_proxy"))
                                        .ipCountry(safeText(source, "ip_address"))
                                        .binCountry(safeText(source, "bin_country"))
                                        .riskAssessment(safeText(source, "risk_assesment"))
                                        .build();
                        PromotionInformation promotionInformation = PromotionInformation.builder()
                                        .promotionCode(safeText(source, "s_id"))
                                        .promotionName(safeText(source, "s_name"))
                                        .discountCurrency(safeText(source, "qlCurrency"))
                                        .build();
                        if (promotionInformation.getPromotionCode() != null
                                        && !promotionInformation.getPromotionCode().isEmpty()) {
                                promotionInformation.setDiscountAmount(orderInformation.getOrderAmount()
                                                .subtract(transactionInformation.getTransactionAmount()));
                        }

                        TransactionDetailResponse dto = TransactionDetailResponse.builder()
                                        .transactionInformation(transactionInformation)
                                        .orderInformation(orderInformation)
                                        .merchantInformation(merchantInformation)
                                        .paymentMethod(paymentMethodInfo)
                                        .feeInformation(feeInformation)
                                        .advanceInformation(advanceInformation)
                                        .riskManagement(riskManagement)
                                        .promotionInformation(promotionInformation)
                                        .build();
                        logger.info("End mapJsonPurchaseDomastic...");
                        return dto;

                } catch (Exception e) {
                        logger.warn("Error mapJsonPurchaseDomastic: ", e);
                        return null;
                }
        }

        private TransactionDetailResponse mapJsonAllQr(JsonNode source, String paymentMethod) {
                try {
                        logger.info("Start mapJsonAllQr...");
                        JsonNode instrumentJson = source.path("instrument");
                        JsonNode amountJson = source.path("amount");

                        OrderInformation orderInformation = OrderInformation.builder()
                                        .orderCreatedTime(convertDate(safeText(source, "createTime")))
                                        .orderReference(safeText(source, "orderInfo"))
                                        .orderCurrency(safeText(amountJson, "currency"))
                                        .build();
                        String amountStr = safeText(source, "amount", "total");
                        if (amountStr != null) {
                                orderInformation.setOrderAmount(new BigDecimal(amountStr));
                        }

                        MerchantInformation merchantInformation = MerchantInformation.builder()
                                        .merchantId(safeText(source, "merchantId"))
                                        .merchantName(safeText(source, "merchantName"))
                                        .build();

                        TransactionInformation transactionInformation = TransactionInformation.builder()
                                        .transactionId(safeText(source, "transactionId"))
                                        .parentTransactionId(safeText(source, "originalId"))
                                        .transactionType(safeText(source, "transactionType"))
                                        .paymentMethod(paymentMethod)
                                        .transactionCreatedTime(convertDate(safeText(source, "createTime")))
                                        .transactionCompletedTime(convertDate(safeText(source, "expireTime")))
                                        .transactionCurrency(safeText(amountJson, "currency"))
                                        .transactionStatus(safeText(source, "advanceStatus"))
                                        .responseCode(safeText(source, "response_code"))
                                        .merchantTransRef(safeText(source, "merchantTxnRef"))
                                        .build();

                        String transAmount = safeText(amountJson, "total");
                        if (amountStr != null) {
                                transactionInformation.setTransactionAmount(new BigDecimal(transAmount));
                        }

                        PaymentMethod paymentMethodInfo = PaymentMethod.builder()
                                        .cardNumber(safeText(instrumentJson, "number"))
                                        .cardBrand(safeText(instrumentJson,  "brandId"))
                                        .cardType(safeText(instrumentJson,  "type"))
                                        .cardExpiry(safeText(instrumentJson, "card_date", "month"))
                                        .issuer(safeText(instrumentJson, "acqCode"))
                                        .nameOnCard(safeText(instrumentJson, "name"))
                                        .cscResultCode(safeText(source, "s_cscresult_code"))
                                        .authorizationCode(safeText(source, "authentication", "authorisation_code"))
                                        .appName(safeText(source, "appName"))
                                        .build();
                        FeeInformation feeInformation = FeeInformation.builder()
                                        .build();
                        AdvanceInformation advanceInformation = AdvanceInformation.builder()
                                        .build();
                        RiskManagement riskManagement = RiskManagement.builder()
                                        .ipAddress(safeText(source, "ip_address"))
                                        .ipProxy(safeText(source, "ip_proxy"))
                                        .ipCountry(safeText(source, "ip_address"))
                                        .binCountry(safeText(source, "bin_country"))
                                        .riskAssessment(safeText(source, "risk_assesment"))
                                        .build();
                        PromotionInformation promotionInformation = PromotionInformation.builder()
                                        .promotionCode(safeText(source, "s_id"))
                                        .promotionName(safeText(source, "s_name"))
                                        .discountCurrency(safeText(source, "qlCurrency"))
                                        .build();
                        if (promotionInformation.getPromotionCode() != null
                                        && !promotionInformation.getPromotionCode().isEmpty()) {
                                promotionInformation.setDiscountAmount(orderInformation.getOrderAmount()
                                                .subtract(transactionInformation.getTransactionAmount()));
                        }

                        TransactionDetailResponse dto = TransactionDetailResponse.builder()
                                        .transactionInformation(transactionInformation)
                                        .orderInformation(orderInformation)
                                        .merchantInformation(merchantInformation)
                                        .paymentMethod(paymentMethodInfo)
                                        .feeInformation(feeInformation)
                                        .advanceInformation(advanceInformation)
                                        .riskManagement(riskManagement)
                                        .promotionInformation(promotionInformation)
                                        .build();
                        logger.info("End mapJsonAllQr...");
                        return dto;

                } catch (Exception e) {
                        logger.error("Error mapJsonAllQr: ", e);
                        return null;
                }
        }

        private TransactionDetailResponse mapJsonAllVietQr(JsonNode source, String paymentMethod) {
                try {
                        logger.info("Start mapJsonAllVietQr...");
                        JsonNode amountJson = source.path("amount");

                        OrderInformation orderInformation = OrderInformation.builder()
                                        .orderCreatedTime(convertDate(safeText(source, "transaction_time")))
                                        .orderReference(safeText(source, "order_info"))
                                        .orderCurrency(safeText(amountJson, "currency"))
                                        .build();
                        String amountStr = safeText(source, "amount", "total");
                        if (amountStr != null) {
                                orderInformation.setOrderAmount(new BigDecimal(amountStr));
                        }

                        MerchantInformation merchantInformation = MerchantInformation.builder()
                                        .merchantId(safeText(source, "merchant_id"))
                                        .merchantName(safeText(source, "merchant_txn_ref"))
                                        .build();

                        TransactionInformation transactionInformation = TransactionInformation.builder()
                                        .transactionId(safeText(source, "transaction_id"))
                                        .parentTransactionId(safeText(source, "original_id"))
                                        .transactionType(safeText(source, "transaction_type"))
                                        .paymentMethod(paymentMethod)
                                        .transactionCreatedTime(convertDate(safeText(source, "transaction_time")))
                                        .transactionCompletedTime(convertDate(safeText(source, "transaction_time")))
                                        .transactionCurrency(safeText(amountJson, "currency"))
                                        .transactionStatus(safeText(source, "status"))
                                        .responseCode(safeText(source, "response_code"))
                                        .merchantTransRef(safeText(source, "merchant_txn_ref"))
                                        .build();

                        String transAmount = safeText(amountJson, "total");
                        if (amountStr != null) {
                                transactionInformation.setTransactionAmount(new BigDecimal(transAmount));
                        }

                        PaymentMethod paymentMethodInfo = PaymentMethod.builder()
                                        .cardNumber(safeText(source, "number"))
                                        .cardBrand(safeText(source,  "brandId"))
                                        .cardType(safeText(source,  "type"))
                                        .cardExpiry(safeText(source, "card_date", "month"))
                                        .issuer(safeText(source, "acqCode"))
                                        .nameOnCard(safeText(source, "name"))
                                        .cscResultCode(safeText(source, "s_cscresult_code"))
                                        .authorizationCode(safeText(source, "authentication", "authorisation_code"))
                                        .build();
                        FeeInformation feeInformation = FeeInformation.builder()
                                        .build();
                        AdvanceInformation advanceInformation = AdvanceInformation.builder()
                                        .build();
                        RiskManagement riskManagement = RiskManagement.builder()
                                        .ipAddress(safeText(source, "ip_address"))
                                        .ipProxy(safeText(source, "ip_proxy"))
                                        .ipCountry(safeText(source, "ip_address"))
                                        .binCountry(safeText(source, "bin_country"))
                                        .riskAssessment(safeText(source, "risk_assesment"))
                                        .build();
                        PromotionInformation promotionInformation = PromotionInformation.builder()
                                        .promotionCode(safeText(source, "s_id"))
                                        .promotionName(safeText(source, "s_name"))
                                        .discountCurrency(safeText(source, "qlCurrency"))
                                        .build();
                        if (promotionInformation.getPromotionCode() != null
                                        && !promotionInformation.getPromotionCode().isEmpty()) {
                                promotionInformation.setDiscountAmount(orderInformation.getOrderAmount()
                                                .subtract(transactionInformation.getTransactionAmount()));
                        }

                        TransactionDetailResponse dto = TransactionDetailResponse.builder()
                                        .transactionInformation(transactionInformation)
                                        .orderInformation(orderInformation)
                                        .merchantInformation(merchantInformation)
                                        .paymentMethod(paymentMethodInfo)
                                        .feeInformation(feeInformation)
                                        .advanceInformation(advanceInformation)
                                        .riskManagement(riskManagement)
                                        .promotionInformation(promotionInformation)
                                        .build();
                        logger.info("End mapJsonAllVietQr...");
                        return dto;

                } catch (Exception e) {
                        logger.error("Error mapJsonAllVietQr: ", e);
                        return null;
                }
        }

        private TransactionDetailResponse mapJsonAllBNPL(JsonNode source, String paymentMethod) {
                try {
                        logger.info("Start mapJsonAllBNPL...");
                        JsonNode orgAmountJson = source.path("orgAmount");
                        JsonNode fpAmountJson = source.path("fpAmount");
                        JsonNode plAmountJson = source.path("plAmount");
                        JsonNode rfAmountJson = source.path("rfAmount");
                        JsonNode bnplModelJson = source.path("bnplModel");

                        OrderInformation orderInformation = OrderInformation.builder()
                                        .orderCreatedTime(convertDateFormat(safeText(source, "transactionTime"),"MMM d, yyyy, h:mm:ss a"))
                                        .orderReference(safeText(source, "orderInfo"))
                                        .orderCurrency(safeText(orgAmountJson, "currency"))
                                        .build();
                        String amountStr = safeText(source, "nAmount");
                        if (amountStr != null) {
                                orderInformation.setOrderAmount(new BigDecimal(amountStr));
                        }

                        MerchantInformation merchantInformation = MerchantInformation.builder()
                                        .merchantId(safeText(source, "merchantId"))
                                        .merchantName(safeText(source, "merchantTnxRef"))
                                        .build();

                        TransactionInformation transactionInformation = TransactionInformation.builder()
                                        .transactionId(safeText(source, "transactionId"))
                                        .parentTransactionId(safeText(source, "orgTransactionId"))
                                        .transactionType(safeText(source, "transactionType"))
                                        .paymentMethod(paymentMethod)
                                        .transactionCreatedTime(convertDateFormat(safeText(source, "transactionTime"),"MMM d, yyyy, h:mm:ss a"))
                                        .transactionCompletedTime(convertDateFormat(safeText(source, "transactionTime"),"MMM d, yyyy, h:mm:ss a"))
                                        .transactionCurrency(safeText(orgAmountJson, "currency"))
                                        .transactionStatus(safeText(source, "status"))
                                        .responseCode(safeText(source, "response_code"))
                                        .merchantTransRef(safeText(source, "merchantTnxRef"))
                                        .build();

                        String transAmount = safeText(rfAmountJson, "total");
                        if (amountStr != null) {
                                transactionInformation.setTransactionAmount(new BigDecimal(transAmount));
                        }

                        PaymentMethod paymentMethodInfo = PaymentMethod.builder()
                                        .cardNumber(safeText(bnplModelJson, "number"))
                                        .cardBrand(safeText(bnplModelJson,  "provider"))
                                        .cardType(safeText(bnplModelJson,  "provider"))
                                        .cardExpiry(safeText(bnplModelJson, "periodTime"))
                                        .issuer(safeText(bnplModelJson, "acqCode"))
                                        .nameOnCard(safeText(bnplModelJson, "customerName"))
                                        .cscResultCode(safeText(source, "s_cscresult_code"))
                                        .authorizationCode(safeText(source, "authentication", "authorisation_code"))
                                        .settlementAmount(new BigDecimal(safeText(plAmountJson, "total")))
                                        .provider(safeText(source, "provider"))
                                        .build();
                        FeeInformation feeInformation = FeeInformation.builder()
                                        .build();
                        AdvanceInformation advanceInformation = AdvanceInformation.builder()
                                        .build();
                        RiskManagement riskManagement = RiskManagement.builder()
                                        .ipAddress(safeText(source, "ip_address"))
                                        .ipProxy(safeText(source, "ip_proxy"))
                                        .ipCountry(safeText(source, "ip_address"))
                                        .binCountry(safeText(source, "bin_country"))
                                        .riskAssessment(safeText(source, "risk_assesment"))
                                        .build();
                        PromotionInformation promotionInformation = PromotionInformation.builder()
                                        .promotionCode(safeText(source, "s_id"))
                                        .promotionName(safeText(source, "s_name"))
                                        .discountCurrency(safeText(source, "qlCurrency"))
                                        .discountAmount(new BigDecimal(safeText(source, "discount")))
                                        .build();
                        if (promotionInformation.getPromotionCode() != null
                                        && !promotionInformation.getPromotionCode().isEmpty()) {
                                promotionInformation.setDiscountAmount(orderInformation.getOrderAmount()
                                                .subtract(transactionInformation.getTransactionAmount()));
                        }

                        TransactionDetailResponse dto = TransactionDetailResponse.builder()
                                        .transactionInformation(transactionInformation)
                                        .orderInformation(orderInformation)
                                        .merchantInformation(merchantInformation)
                                        .paymentMethod(paymentMethodInfo)
                                        .feeInformation(feeInformation)
                                        .advanceInformation(advanceInformation)
                                        .riskManagement(riskManagement)
                                        .promotionInformation(promotionInformation)
                                        .build();
                        logger.info("End mapJsonAllBNPL...");
                        return dto;

                } catch (Exception e) {
                        logger.error("Error mapJsonAllBNPL: ", e);
                        return null;
                }
        }

        private TransactionDetailResponse mapJsonAllDirectDebit(JsonNode source, String paymentMethod) {
                try {
                        logger.info("Start mapJsonAllDirectDebit...");
                        JsonNode amountJson = source.path("amount");

                        OrderInformation orderInformation = OrderInformation.builder()
                                        .orderCreatedTime(convertDate(safeText(source, "transaction_time")))
                                        .orderReference(safeText(source, "order_info"))
                                        .orderCurrency(safeText(amountJson, "currency"))
                                        .build();
                        String amountStr = safeText(source, "amount", "original_total");
                        if (amountStr != null) {
                                orderInformation.setOrderAmount(new BigDecimal(amountStr));
                        }

                        MerchantInformation merchantInformation = MerchantInformation.builder()
                                        .merchantId(safeText(source, "merchant_id"))
                                        .merchantName(safeText(source, "merchant_transaction_ref"))
                                        .build();

                        TransactionInformation transactionInformation = TransactionInformation.builder()
                                        .transactionId(safeText(source, "transaction_id"))
                                        .parentTransactionId(safeText(source, "original_id"))
                                        .transactionType(safeText(source, "transaction_type"))
                                        .paymentMethod(paymentMethod)
                                        .transactionCreatedTime(convertDate(safeText(source, "transaction_time")))
                                        .transactionCompletedTime(convertDate(safeText(source, "transaction_time")))
                                        .transactionCurrency(safeText(amountJson, "currency"))
                                        .transactionStatus(safeText(source, "advance_status"))
                                        .responseCode(safeText(source, "response_code"))
                                        .merchantTransRef(safeText(source, "merchant_transaction_ref"))
                                        .build();

                        String transAmount = safeText(amountJson, "total");
                        if (amountStr != null) {
                                transactionInformation.setTransactionAmount(new BigDecimal(transAmount));
                        }

                        PaymentMethod paymentMethodInfo = PaymentMethod.builder()
                                        .cardNumber(safeText(source, "card_number"))
                                        .cardBrand(safeText(source,  "brandId"))
                                        .cardType(safeText(source,  "type"))
                                        .cardExpiry(safeText(source, "card_date"))
                                        .issuer(safeText(source, "bank_id"))
                                        .nameOnCard(safeText(source, "customer_name"))
                                        .cscResultCode(safeText(source, "s_cscresult_code"))
                                        .authorizationCode(safeText(source, "authentication", "authorisation_code"))
                                        .build();
                        FeeInformation feeInformation = FeeInformation.builder()
                                        .build();
                        AdvanceInformation advanceInformation = AdvanceInformation.builder()
                                        .build();
                        RiskManagement riskManagement = RiskManagement.builder()
                                        .ipAddress(safeText(source, "ip"))
                                        .ipProxy(safeText(source, "ip_proxy"))
                                        .ipCountry(safeText(source, "ip_address"))
                                        .binCountry(safeText(source, "bin_country"))
                                        .riskAssessment(safeText(source, "risk_assesment"))
                                        .build();
                        PromotionInformation promotionInformation = PromotionInformation.builder()
                                        .promotionCode(safeText(source, "s_id"))
                                        .promotionName(safeText(source, "s_name"))
                                        .discountCurrency(safeText(source, "qlCurrency"))
                                        .build();
                        if (promotionInformation.getPromotionCode() != null
                                        && !promotionInformation.getPromotionCode().isEmpty()) {
                                promotionInformation.setDiscountAmount(orderInformation.getOrderAmount()
                                                .subtract(transactionInformation.getTransactionAmount()));
                        }

                        TransactionDetailResponse dto = TransactionDetailResponse.builder()
                                        .transactionInformation(transactionInformation)
                                        .orderInformation(orderInformation)
                                        .merchantInformation(merchantInformation)
                                        .paymentMethod(paymentMethodInfo)
                                        .feeInformation(feeInformation)
                                        .advanceInformation(advanceInformation)
                                        .riskManagement(riskManagement)
                                        .promotionInformation(promotionInformation)
                                        .build();
                        logger.info("End mapJsonAllDirectDebit...");
                        return dto;

                } catch (Exception e) {
                        logger.warn("Error mapJsonAllDirectDebit: ", e);
                        return null;
                }
        }

        private TransactionDetailResponse mapJsonAllUpos(JsonNode source, String paymentMethod) {
                try {
                        logger.info("Start mapJsonAllUpos...");
                        JsonNode dataJson = source.path("data");

                        OrderInformation orderInformation = OrderInformation.builder()
                                        .orderCreatedTime(convertDateFormat(safeText(dataJson, "dateTransaction"),"dd/MM/yyyy HH:mm:ss"))
                                        .orderReference(safeText(dataJson, "orderRef"))
                                        .orderCurrency(safeText(dataJson, "currency"))
                                        .build();
                        String amountStr = safeText(dataJson, "purchaseAmount");
                        if (amountStr != null) {
                                orderInformation.setOrderAmount(new BigDecimal(amountStr));
                        }

                        MerchantInformation merchantInformation = MerchantInformation.builder()
                                        .merchantId(safeText(dataJson, "merchantId"))
                                        .merchantName(safeText(dataJson, "merchantTransRef"))
                                        .build();

                        TransactionInformation transactionInformation = TransactionInformation.builder()
                                        .transactionId(safeText(dataJson, "transactionId"))
                                        .parentTransactionId(safeText(dataJson, "transactionId"))
                                        .transactionType(safeText(dataJson, "transType"))
                                        .paymentMethod(paymentMethod)
                                        .paymentChannel(safeText(dataJson, "payChannel"))
                                        .transactionCreatedTime(convertDateFormat(safeText(dataJson, "dateTransaction"),"dd/MM/yyyy HH:mm:ss"))
                                        .transactionCompletedTime(convertDateFormat(safeText(dataJson, "dateTransaction"),"dd/MM/yyyy HH:mm:ss"))
                                        .transactionCurrency(safeText(dataJson, "currency"))
                                        .transactionStatus(safeText(dataJson, "transState"))
                                        .responseCode(safeText(dataJson, "responseCode"))
                                        .merchantTransRef(safeText(source, "merchantTransRef"))
                                        .build();

                        String transAmount = safeText(dataJson, "purchaseAmount");
                        if (amountStr != null) {
                                transactionInformation.setTransactionAmount(new BigDecimal(transAmount));
                        }

                        PaymentMethod paymentMethodInfo = PaymentMethod.builder()
                                        .cardNumber(safeText(dataJson, "cardNumber"))
                                        .cardBrand(safeText(dataJson,  "brandId"))
                                        .cardType(safeText(dataJson,  "type"))
                                        .cardExpiry(safeText(dataJson, "insMonth")+"/"+safeText(dataJson, "insYear"))
                                        .issuer(safeText(dataJson, "bank_id"))
                                        .nameOnCard(safeText(dataJson, "cardName"))
                                        .cscResultCode(safeText(dataJson, "s_cscresult_code"))
                                        .authorizationCode(safeText(dataJson, "authentication", "authorisation_code"))
                                        .build();
                        FeeInformation feeInformation = FeeInformation.builder()
                                        .build();
                        AdvanceInformation advanceInformation = AdvanceInformation.builder()
                                        .build();
                        RiskManagement riskManagement = RiskManagement.builder()
                                        .ipAddress(safeText(dataJson, "ip"))
                                        .ipProxy(safeText(dataJson, "ip_proxy"))
                                        .ipCountry(safeText(dataJson, "ip_address"))
                                        .binCountry(safeText(dataJson, "bin_country"))
                                        .riskAssessment(safeText(dataJson, "risk_assesment"))
                                        .build();
                        PromotionInformation promotionInformation = PromotionInformation.builder()
                                        .promotionCode(safeText(dataJson, "s_id"))
                                        .promotionName(safeText(dataJson, "s_name"))
                                        .discountCurrency(safeText(dataJson, "qlCurrency"))
                                        .build();
                        if (promotionInformation.getPromotionCode() != null
                                        && !promotionInformation.getPromotionCode().isEmpty()) {
                                promotionInformation.setDiscountAmount(orderInformation.getOrderAmount()
                                                .subtract(transactionInformation.getTransactionAmount()));
                        }

                        TransactionDetailResponse dto = TransactionDetailResponse.builder()
                                        .transactionInformation(transactionInformation)
                                        .orderInformation(orderInformation)
                                        .merchantInformation(merchantInformation)
                                        .paymentMethod(paymentMethodInfo)
                                        .feeInformation(feeInformation)
                                        .advanceInformation(advanceInformation)
                                        .riskManagement(riskManagement)
                                        .promotionInformation(promotionInformation)
                                        .build();
                        logger.info("End mapJsonAllUpos...");
                        return dto;

                } catch (Exception e) {
                        logger.warn("Error mapJsonAllUpos: ", e);
                        return null;
                }
        }

        private String safeText(JsonNode node, String... path) {
                for (String p : path) {
                        if (node == null)
                                return null;
                        node = node.path(p);
                }
                return node.isMissingNode() ? null : node.asText(null);
        }

        private static String convertDate(String datetimeStr) {
                try {
                        OffsetDateTime odt = OffsetDateTime.parse(datetimeStr, INPUT_FORMATTER);
                        return odt.toLocalDateTime().format(OUTPUT_FORMATTER);
                } catch (DateTimeParseException e) {
                        logger.error("Invalid datetime format: {}", datetimeStr);
                        return null;
                }
        }

        private static String convertDateFormat(String datetimeStr, String inputFormat) {
                try {
                        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(inputFormat, Locale.ENGLISH);
                        LocalDateTime ldt = LocalDateTime.parse(datetimeStr, dateTimeFormatter);
                        return ldt.format(OUTPUT_FORMATTER);
                } catch (DateTimeParseException e) {
                        logger.error("Invalid datetime format: {}", datetimeStr);
                        return null;
                }
        }

        private static String callApiGetTransDetail(String url, String xUserId){
                try {
                        logger.info("---------------start call api getTransactionDetail MA-----------");
                        HttpClient client = HttpClient.newBuilder()
                                .connectTimeout(Duration.ofSeconds(30))
                                .build();
                        HttpRequest request ;
                        if (xUserId != null) {
                           request = HttpRequest.newBuilder()
                                .uri(URI.create(url))
                                .timeout(Duration.ofSeconds(30))
                                .header("Accept", "application/json")
                                .header("X-USER-ID", xUserId)
                                .GET()
                                .build();
                        }else{
                           request = HttpRequest.newBuilder()
                                .uri(URI.create(url))
                                .timeout(Duration.ofSeconds(30))
                                .header("Accept", "application/json")
                                .GET()
                                .build();
                        }
                        logger.info("REQUEST: url=" +url+ " ,xUserId="+xUserId);
                        HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
                        logger.info("RESPONSE TRANSACTION DETAIL API: response code=" +response.statusCode()+ " ,response body="+response.body());
                        if (response.statusCode() == 200) {
                                return response.body();
                        } else {
                                return null;
                        }
                 } catch (Exception e) {
                        logger.error("Error call api get transaction detail: ", e);
                        return null;
                }finally{
                        logger.info("------------end call api getTransactionDetail MA------------");
                }
        }

        @Override
        public Mono<Boolean> checkMerchantIdByUserId(String userId, String merchantTransactionDetail, String paymentMethod) {
                logger.info("------------strart checkMerchantIdByUserId------------");
                logger.info("REQUEST: userid="+userId+" ,merchantTransactionDetail="+merchantTransactionDetail+" ,paymentMethod="+paymentMethod);
                return merchantService.resolveMerchantIdFilter(merchantTransactionDetail)
                        .flatMap(merchantIds -> {
                            if (merchantIds.isEmpty()) {
                                logger.error("No merchantId matched -> returning empty response");
                                return Mono.just(false);
                            }
                            return Mono.just(true);
                        });
        }

        @Override
        public Mono<Map<String, String>> refundTransaction(String transactionId, String paymentMethod, String jsonData, String userId, String realIp) {
                try {
                        logger.info("---------------start call api refundTransaction MA-----------");
                        HttpClient httpClient = HttpClient.newBuilder()
                                .connectTimeout(Duration.ofSeconds(30))
                                .build();
                        String strUrl = null;
                         switch (paymentMethod) {
                                case IConstants.PAYMENT_METHOD_QT, IConstants.PAYMENT_METHOD_SS_PAY, IConstants.PAYMENT_METHOD_GG_PAY, IConstants.PAYMENT_METHOD_AP_PAY, IConstants.PAYMENT_METHOD_INSTALLMENT:
                                        strUrl = internationalUrl + "/international/transaction/" +transactionId;
                                        break;
                                case "ND":
                                        strUrl = domesticUrl + "/domestic/transaction/" +transactionId;
                                        break;
                                case "QR":
                                        strUrl = qrUrl + "/qr/transaction/" +transactionId;
                                        break;
                                case "VIETQR":
                                        strUrl = vietqrUrl + "/vietqr/transaction/" +transactionId;
                                        break;
                                case "BNPL":
                                        strUrl = bnplUrl + "/bnpl/transaction/" +transactionId + "/request-refund";
                                        break;
                                case "DD":
                                        strUrl = directDebitUrl + "/direct-debit/transaction/" +transactionId;
                                        break;
                                default:
                                        break;
                        }
                        logger.info("REQUEST: url=" +strUrl+ " ,xUserId="+userId+" ,realIp="+realIp+" ,jsonData="+jsonData);
                        HttpRequest request = null;
                        if ("BNPL".equalsIgnoreCase(paymentMethod)) {
                            request = HttpRequest.newBuilder()
                                .uri(new URI(strUrl))
                                .timeout(Duration.ofSeconds(30))
                                .header("Content-Type", "application/json")
                                .header("X-USER-ID", userId)
                                .header("X-Real-IP", realIp)
                                .header("X-Request-Id", java.util.UUID.randomUUID().toString())
                                .POST(HttpRequest.BodyPublishers.ofString(jsonData))
                                .build();    
                        } else {
                            request = HttpRequest.newBuilder()
                                .uri(new URI(strUrl))
                                .timeout(Duration.ofSeconds(30))
                                .header("Content-Type", "application/json")
                                .header("X-USER-ID", userId)
                                .header("X-Real-IP", realIp)
                                .header("X-Request-Id", java.util.UUID.randomUUID().toString())
                                .method("PATCH", HttpRequest.BodyPublishers.ofString(jsonData))
                                .build();    
                        }

                        return Mono.fromFuture(httpClient.sendAsync(request, HttpResponse.BodyHandlers.ofString()))
                                .map(response -> {
                                        logger.info("RESPONSE REFUND API: response code=" +response.statusCode()+ " ,response body="+response.body());
                                        if (response.statusCode() == 200 || (response.statusCode() == 201 && "VIETQR".equalsIgnoreCase(paymentMethod))) {
                                                String newTransactionid = "";
                                                Map<String, String> dataResponse = parseRefundResponse(response.body());
                                                if (!dataResponse.isEmpty()) {
                                                        newTransactionid = dataResponse.get("transaction_id");
                                                }

                                                syncTransactionToOpenSearch(transactionId, "", IConstants.TYPE_ACTION_REFUND_REQUEST, userId, realIp, response.body());   
                                                return Map.of("message", "Okie", "new_transaction_id", newTransactionid);
                                        } else {
                                                return Map.of("message", "Fail");
                                        }
                                });

                } catch (Exception e) {
                        logger.error("Error call api refund: ", e);
                        return Mono.just(Map.of("message", "Fail"));
                }finally{
                        logger.info("------------end call api refundTransaction MA------------");
                }
        }

        @Override
        public Mono<Map<String, Object>> approveOrRejectMultipleTransaction(String jsonData, String userId,  String realIp) {
                try {
                        logger.info("---------------start call api approveOrRejectMultipleTransaction MA-----------");
                        
                        ObjectMapper mapper = new ObjectMapper();
                        List<Map<String, Object>> items;
                        try {
                        items = mapper.readValue(jsonData, new TypeReference<>() {});
                        } catch (JsonProcessingException e) {
                        return Mono.error(new RuntimeException("Invalid JSON", e));
                        }

                        return Mono.deferContextual(ctx -> {
                        if (!ctx.hasKey("userId") || !ctx.hasKey("partnerId")) {
                                return Mono.error(new IllegalStateException("Missing userId or partnerId in context"));
                        }

                        // String userId = ctx.get("userId");
                        Long partnerId = ctx.get("partnerId");

                        return Flux.fromIterable(items)
                                .flatMap(item -> {
                                String id = (String) item.get("id");
                                String service = (String) item.get("service");

                                TransactionDetailResponse response = getTransactionDetail(id, service, "Request Refund", null, userId);
                                if (response == null || response.getMerchantInformation() == null || response.getMerchantInformation().getMerchantId() == null) {
                                        logger.error("Transaction ID not found: " + id);
                                        return Mono.just(false);
                                }

                                String merchantId = response.getMerchantInformation().getMerchantId();

                                return checkMerchantIdByUserId(userId, merchantId, service)
                                        .contextWrite(ctx2 -> ctx2.put("userId", userId).put("partnerId", partnerId))
                                        .flatMap(hasPermission -> {
                                        if (!hasPermission) {
                                                logger.error("Permission denied: " + id);
                                                return Mono.just(false);
                                        }

                                        String data;
                                        try {
                                                data = mapper.writeValueAsString(item);
                                        } catch (JsonProcessingException e) {
                                                return Mono.error(new RuntimeException("Invalid JSON", e));
                                        }

                                        return approveOrRejectTransaction(id, service, data, userId, realIp)
                                                .map(result -> {
                                                        if ("Fail".equals(result.get("message"))) {
                                                                logger.error("Approve or reject failed: " + id);
                                                                return false;
                                                        }
                                                        return true;
                                                });
                                        });
                                })
                                .collectList()
                                .map(results -> {
                                long successCount = results.stream().filter(r -> r).count();
                                if (successCount == 0) {
                                        return Map.of("message", "Fail");
                                }
                                
                                if (successCount == items.size()) {
                                        return Map.of("message", "Okie");
                                } else {
                                        return Map.of("message", "Partial", "success", successCount, "total", items.size());
                                }
                                });
                        });
                        

                } catch (Exception e) {
                        logger.error("Error call api approveOrRejectMultipleTransaction: ", e);
                        return Mono.just(Map.of("message", "Fail"));
                }finally{
                        logger.info("------------end call api approveOrRejectMultipleTransaction MA------------");
                }
        }
                        

         @Override
        public Mono<Map<String, String>> approveOrRejectTransaction(String transactionId, String paymentMethod, String jsonData, String userId, String realIp) {
                try {
                        logger.info("---------------start call api approveOrRejectTransaction MA-----------");
                        HttpClient httpClient = HttpClient.newBuilder()
                                .connectTimeout(Duration.ofSeconds(30))
                                .build();
                        String strUrl = null;
                        switch (paymentMethod) {
                                case IConstants.PAYMENT_METHOD_QT, IConstants.PAYMENT_METHOD_SS_PAY, IConstants.PAYMENT_METHOD_GG_PAY, IConstants.PAYMENT_METHOD_AP_PAY, IConstants.PAYMENT_METHOD_INSTALLMENT:
                                        strUrl = internationalUrl + "/international/transaction/refund/" +transactionId;
                                        break;
                                case "ND":
                                        strUrl = domesticUrl + "/domestic/transaction/refund/" +transactionId;
                                        break;
                                case "QR":
                                        strUrl = qrUrl + "/qr/request-refund/" +transactionId;
                                        break;
                                case "VIETQR":
                                        strUrl = vietqrUrl + "/vietqr/transaction/refund/" +transactionId;
                                        break;
                                case "BNPL":
                                        strUrl = bnplUrl + "/bnpl/transaction/" +transactionId+"/change-status";
                                        break;
                                case "DD":
                                        strUrl = directDebitUrl + "/direct-debit/transaction/refund/" +transactionId;
                                        break;
                                default:
                                        break;
                        }
                        logger.info("REQUEST: url=" +strUrl+" ,transactionId"+transactionId+ " ,xUserId="+userId+" ,realIp="+realIp+" ,jsonData="+jsonData);
                        HttpRequest request = null;
                        if ("BNPL".equalsIgnoreCase(paymentMethod)) {
                            request = HttpRequest.newBuilder()
                                .uri(new URI(strUrl))
                                .timeout(Duration.ofSeconds(30))
                                .header("Content-Type", "application/json")
                                .header("X-USER-ID", userId)
                                .header("X-Real-IP", realIp)
                                .header("X-Request-Id", java.util.UUID.randomUUID().toString())
                                .POST(HttpRequest.BodyPublishers.ofString(jsonData))
                                .build();    
                        } else {
                            request = HttpRequest.newBuilder()
                                .uri(new URI(strUrl))
                                .timeout(Duration.ofSeconds(30))
                                .header("Content-Type", "application/json")
                                .header("X-USER-ID", userId)
                                .header("X-Real-IP", realIp)
                                .header("X-Request-Id", java.util.UUID.randomUUID().toString())
                                .method("PATCH", HttpRequest.BodyPublishers.ofString(jsonData))
                                .build();    
                        }

                        return Mono.fromFuture(httpClient.sendAsync(request, HttpResponse.BodyHandlers.ofString()))
                                .map(response -> {
                                        logger.info("RESPONSE REFUND API: response code=" +response.statusCode()+ " ,response body="+response.body());
                                        if (response.statusCode() == 200) {
                                                String path =  mapJsonForKey(jsonData, "path");
                                                String requestId =  mapJsonForKey(jsonData, "id");
                                                switch (path) {
                                                        case "/approve":
                                                                syncTransactionToOpenSearch(transactionId, requestId, IConstants.TYPE_ACTION_REFUND_APPROVE, userId, realIp, response.body());
                                                                break;
                                                        case "/reject":
                                                                syncTransactionToOpenSearch(transactionId, requestId, IConstants.TYPE_ACTION_REFUND_REJECT, userId, realIp, response.body());
                                                                break;
                                                        default:
                                                                break;
                                                }
                                                return Map.of("message", "Okie");
                                        } else if (response.statusCode() == 201 && "VIETQR".equalsIgnoreCase(paymentMethod)){
                                                return Map.of("message", "Okie");
                                        } else {
                                                return Map.of("message", "Fail");
                                        }
                                });

                } catch (Exception e) {
                        logger.error("Error call api approve reject: ", e);
                        return Mono.just(Map.of("message", "Fail"));
                }finally{
                        logger.info("------------end call api approveOrRejectTransaction MA------------");
                }
        }

        @Override
        public Mono<Map<String, String>> captureTransaction(String transactionId, String merchantId, String paymentMethod, String jsonData, String userId, String realIp) {
                try {
                        logger.info("---------------start call api captureTransaction MA-----------");
                        HttpClient httpClient = HttpClient.newBuilder()
                                .connectTimeout(Duration.ofSeconds(30))
                                .version(HttpClient.Version.HTTP_1_1)  // Force HTTP/1.1 to avoid HTTP/2 issues
                                .build();
                        String strUrl = null;
                         switch (paymentMethod) {
                                case IConstants.PAYMENT_METHOD_QT, IConstants.PAYMENT_METHOD_SS_PAY, IConstants.PAYMENT_METHOD_GG_PAY, IConstants.PAYMENT_METHOD_AP_PAY, IConstants.PAYMENT_METHOD_INSTALLMENT:
                                        strUrl = maServiceUrl + "/transaction/international/" +transactionId;
                                        break;
                                default:
                                        logger.info("captureTransaction paymentMethod invalid, transactionId={}, paymentMethod={}", transactionId, paymentMethod);
                                        return Mono.just(Map.of("message", "Fail"));
                        }
                        
                        logger.info("REQUEST: url=" +strUrl+ " ,xUserId="+userId+" ,realIp="+realIp+" ,jsonData="+jsonData);
                
                        HttpRequest request = HttpRequest.newBuilder()
                        .uri(new URI(strUrl))
                        .timeout(Duration.ofSeconds(30))
                        .header("Content-Type", "application/json")
                        .header("X-USER-ID", userId)
                        .header("X-Real-IP", realIp)
                        .header("X-Request-Id", java.util.UUID.randomUUID().toString())
                        .method("PATCH", HttpRequest.BodyPublishers.ofString(jsonData))
                        .build(); 

                        return Mono.fromFuture(httpClient.sendAsync(request, HttpResponse.BodyHandlers.ofString()))
                                .map(response -> {
                                        logger.info("RESPONSE CAPTURE API: response code=" +response.statusCode()+ " ,response body="+response.body());

                                        if (response.statusCode() == 200) {
                                                Map<String, String> parsedResponse = parseCaptureResponse(response.body());
                                                if (parsedResponse.isEmpty()) {
                                                        logger.warn("Parsed response is empty, transactionId: {}", transactionId);
                                                        return Map.of("message", "Fail");
                                                }

                                                if (!parsedResponse.get("status").equals("200")) {
                                                        logger.warn("Capture failed, transactionId: {}", transactionId);
                                                        return Map.of("message", "Fail");
                                                }
                                               
                                                syncTransactionToOpenSearch(transactionId, merchantId, IConstants.TYPE_ACTION_CAPTURE, userId, realIp, response.body());
                                                Map<String, String> dataResponse = parseCaptureResponse(response.body());
                                                String newTransactionid = "";
                                                if (!dataResponse.isEmpty()) {
                                                        newTransactionid = dataResponse.get("new_transaction_id");
                                                }

                                                return Map.of("message", "Okie", "new_transaction_id", newTransactionid);
                                        } else {
                                                return Map.of("message", "Fail");
                                        }
                                });

                } catch (Exception e) {
                        logger.error("Error call api capture: ", e);
                        return Mono.just(Map.of("message", "Fail"));
                }finally{
                        logger.info("------------end call api captureTransaction MA------------");
                }
        }

        @Override
        public Mono<Map<String, String>> voidTransaction(String transactionId, String merchantId, String paymentMethod, String jsonData, String userId, String realIp) {
                try {
                        logger.info("---------------start call api voidTransaction MA-----------");
                        HttpClient httpClient = HttpClient.newBuilder()
                                .connectTimeout(Duration.ofSeconds(30))
                                .version(HttpClient.Version.HTTP_1_1)  // Force HTTP/1.1 to avoid HTTP/2 issues
                                .build();
                        String strUrl = null;
                         switch (paymentMethod) {
                                case IConstants.PAYMENT_METHOD_QT, IConstants.PAYMENT_METHOD_SS_PAY, IConstants.PAYMENT_METHOD_GG_PAY, IConstants.PAYMENT_METHOD_AP_PAY, IConstants.PAYMENT_METHOD_INSTALLMENT:
                                        strUrl = maServiceUrl + "/transaction/international/" +transactionId;
                                        break;
                                default:
                                        logger.info("voidTransaction paymentMethod invalid, transactionId={}, paymentMethod={}", transactionId, paymentMethod);
                                        return Mono.just(Map.of("message", "Fail"));
                        }
                        
                        logger.info("REQUEST: url=" +strUrl+ " ,xUserId="+userId+" ,realIp="+realIp+" ,jsonData="+jsonData);
                
                        HttpRequest request = HttpRequest.newBuilder()
                        .uri(new URI(strUrl))
                        .timeout(Duration.ofSeconds(30))
                        .header("Content-Type", "application/json")
                        .header("X-USER-ID", userId)
                        .header("X-Real-IP", realIp)
                        .header("X-Request-Id", java.util.UUID.randomUUID().toString())
                        .method("PATCH", HttpRequest.BodyPublishers.ofString(jsonData))
                        .build(); 

                        return Mono.fromFuture(httpClient.sendAsync(request, HttpResponse.BodyHandlers.ofString()))
                                .map(response -> {
                                        logger.info("RESPONSE VOID API: response code=" +response.statusCode()+ " ,response body="+response.body());

                                        if (response.statusCode() == 200) {                                               
                                                syncTransactionToOpenSearch(transactionId, merchantId, IConstants.TYPE_ACTION_VOID, userId, realIp, response.body());
                                                Map<String, String> dataResponse = parseVoidResponse(response.body());
                                                String newTransactionid = "";
                                                if (!dataResponse.isEmpty()) {
                                                        newTransactionid = dataResponse.get("new_transaction_id");
                                                }

                                                return Map.of("message", "Okie", "new_transaction_id", newTransactionid);
                                        } else {
                                                return Map.of("message", "Fail");
                                        }
                                });

                } catch (Exception e) {
                        logger.error("Error call api void: ", e);
                        return Mono.just(Map.of("message", "Fail"));
                }finally{
                        logger.info("------------end call api voidTransaction MA------------");
                }
        }

        /**
         * @param transactionId
         * @param optionValue
         * @param type
         * @param userId
         * @param realIp
         * @param responseBody
         * optionValue: Capture, void: merchant_id; refund: old_request_id
         */

        private void syncTransactionToOpenSearch(String transactionId, String optionValue, String type, String userId, String realIp, String responseBody) {
                logger.info("Starting syncTransactionToOpenSearch - transactionId: {}, type: {}", transactionId, type);

                Map<String, String> parsedResponse = Map.of();
                switch (type) {
                        case IConstants.TYPE_ACTION_CAPTURE:
                                parsedResponse = parseCaptureResponse(responseBody);
                                if (parsedResponse.isEmpty()) {
                                        logger.warn("parseCaptureResponse is empty, transactionId: {}, type: {}", transactionId, type);
                                        return;
                                }

                                parsedResponse.put("merchant_id", optionValue);
                                break;
                        case IConstants.TYPE_ACTION_VOID:
                                parsedResponse = parseVoidResponse(responseBody);
                                if (parsedResponse.isEmpty()) {
                                        logger.warn("parseVoidResponse is empty, transactionId: {}, type: {}", transactionId, type);
                                        return;
                                }

                                parsedResponse.put("merchant_id", optionValue);
                                break;
                        case IConstants.TYPE_ACTION_REFUND_REQUEST,
                                IConstants.TYPE_ACTION_REFUND_APPROVE,
                                IConstants.TYPE_ACTION_REFUND_REJECT:
                                parsedResponse = parseRefundResponse(responseBody);
                                if (parsedResponse.isEmpty()) {
                                        logger.warn("parseRefundResponse is empty, transactionId: {}, type: {}", transactionId, type);
                                        return;
                                }

                                parsedResponse.put("old_transaction_id", optionValue);
                                break;
                        default:
                                break;
                }

                try {
                        parsedResponse.put("type", type);
                        // Convert Map to JSON string
                        ObjectMapper mapper = new ObjectMapper();
                        String jsonPayload = mapper.writeValueAsString(parsedResponse);

                        logger.info("Syncing to OpenSearch - payload: {}", jsonPayload);

                        String strUrl = opensearchUrl + "/transaction/sync";
                        HttpClient httpClient = HttpClient.newBuilder()
                                .connectTimeout(Duration.ofSeconds(30))
                                .build();

                        HttpRequest request = HttpRequest.newBuilder()
                                .uri(new URI(strUrl))
                                .timeout(Duration.ofSeconds(30))
                                .header("Content-Type", "application/json")
                                .header("X-USER-ID", userId != null ? userId : "system")
                                .header("X-Real-IP", realIp != null ? realIp : "127.0.0.1")
                                .header("X-Request-Id", java.util.UUID.randomUUID().toString())
                                .POST(HttpRequest.BodyPublishers.ofString(jsonPayload))
                                .build();

                        // Execute request asynchronously
                        httpClient.sendAsync(request, HttpResponse.BodyHandlers.ofString())
                                .thenAccept(response -> {
                                        logger.info("OpenSearch sync response - status: {}, body: {}",
                                                response.statusCode(), response.body());
                                })
                                .exceptionally(throwable -> {
                                        logger.error("Error syncing to OpenSearch: {}", throwable.getMessage(), throwable);
                                        return null;
                                });

                } catch (Exception e) {
                        logger.error("Error in syncTransactionToOpenSearch: {}", e.getMessage(), e);
                }
        }

         @Override
         public Mono<ApiResponse<PagedResponse<RequestRefundResponse>>> getRefundRequestsOld(String fromDate,
                        String toDate, String merchantId, String status, String searchKeyword, int page, int size,
                        String sortField, String sortOrder, String userId) {
                try {
                        logger.info("---------------------start call api getRefundRequestsOld-------------------------");

                        return merchantService.resolveMerchantIdFilter(merchantId)
                                .flatMap(merchantIds -> {
                                        String queryMerchantQT = String.join(",", merchantIds);
                                        Mono<PagedResponse<RequestRefundResponse>> dataMono = getRequestRefund(fromDate, toDate, merchantId, status, searchKeyword, page, size, queryMerchantQT);
                                        Mono<Integer> totalMono = countRequestRefund(fromDate, toDate, merchantId, status, searchKeyword, page, size, queryMerchantQT);

                                        return Mono.zip(dataMono, totalMono)
                                                .map(tuple -> {
                                                        PagedResponse<RequestRefundResponse> data = tuple.getT1();
                                                        int total = tuple.getT2();

                                                        int totalPages = (int) Math.ceil((double) total / size);
                                                        data.setTotalPages(totalPages);

                                                        return ApiResponse.<PagedResponse<RequestRefundResponse>>builder()
                                                                .status("OK")
                                                                .total(total)
                                                                .data(data)
                                                                .build();
                                                });
                                });
                } catch (Exception e) {
                        logger.error("Error getRefundRequestsOld: ", e);
                        return Mono.just(ApiResponse.<PagedResponse<RequestRefundResponse>>builder()
                                        .status("Fail")
                                        .total(0)
                                        .data(null)
                                        .build()); 
                }finally{
                        logger.info("---------------------end call api getRefundRequestsOld-------------------------");
                }
                
         }

        private String replaceRefundStatuses(String status) {
                if (status == null) {
                        return null;
                }
                return status.replace("Waiting for OnePay's Approval", "Waiting for OnePay''s Approval,Waiting for onepays approval,Waiting for OnePAY''s Approval")
                                .replace("Waiting for Merchant's Approval", "Waiting for Approval")
                                .replace("OnePay Rejected", "OnePays rejected,OnePAY Rejected")
                                .replace("OnePay Approved", "OnePays approved,OnePAY Approved,310");
        }

        public Mono<PagedResponse<RequestRefundResponse>> getRequestRefund(
                String fromDate, String toDate,  String merchantId,
                String status, String searchKeyword, int page, int size,
                String queryMerchantIds) {
                try {
                     return oracleJdbcTemplate.execute((Connection con) -> {
                        CallableStatement cs = null;
                        ResultSet rs = null;
                        try {
                                String finalStatus = replaceRefundStatuses(status);
                                cs = con.prepareCall("{call search_request_refund_v3(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)}");
                                 // IN parameters
                                cs.setString(1, "SELECT");
                                cs.setClob(2, new StringReader(queryMerchantIds));
                                cs.setString(3, formatDate(fromDate));
                                cs.setString(4, formatDate(toDate));
                                cs.setString(5, finalStatus);
                                cs.setString(6, searchKeyword);
                                cs.setInt(7, page);
                                cs.setInt(8, size);
                                cs.setInt(9, 0);

                                // OUT params
                                cs.registerOutParameter(10, Types.REF_CURSOR);
                                cs.registerOutParameter(11, Types.NUMERIC);
                                cs.registerOutParameter(12, Types.VARCHAR);

                                cs.execute();

                                rs = (ResultSet) cs.getObject(10);
                                logger.info("Result: {}", cs.getString(12));

                                List<RequestRefundResponse> list = new ArrayList<>();
                                while (rs.next()) {
                                        String state = rs.getString("s_transaction_state");
                                        RequestRefundResponse dto = new RequestRefundResponse();
                                        dto.setCurrency(rs.getString("s_currency"));
                                        dto.setMerchantId(rs.getString("s_merchant_id"));
                                        dto.setDocId(null);
                                        dto.setCreatedDate(rs.getString("d_transaction_date"));
                                        dto.setRequestId(rs.getString("s_transaction_id"));
                                        dto.setParentTransactionId(rs.getString("s_original_id"));
                                        dto.setRequestStatus(IConstants.TRANSACTION_STATUS_MAP.getOrDefault(state, state));
                                        dto.setCreatedBy(rs.getString("S_OPERATOR_ID"));
                                        dto.setPaymentMethod(rs.getString("s_input_type"));
                                        dto.setAmount(rs.getBigDecimal("n_amount"));
                                        dto.setMerchantTransactionRef(rs.getString("S_MERCHANT_TRANS_REF"));
                                        dto.setParentMerchantTransactionRef(rs.getString("S_PARENT_MERCHANT_TRANS_REF"));
                                        list.add(dto);
                                }

                                PagedResponse<RequestRefundResponse> pagedResponse = PagedResponse.<RequestRefundResponse>builder()
                                        .currentPage(page)
                                        .pageSize(size)
                                        .totalPages(1)
                                        .items(list)
                                        .build();

                                return Mono.just(pagedResponse);
                        }finally{
                                if (rs != null) try { rs.close(); } catch (SQLException ignored) {}
                                if (cs != null) try { cs.close(); } catch (SQLException ignored) {}
                        }
                       
                });    
                } catch (Exception e) {
                    logger.error("Error search_request_refund: ", e);
                    return Mono.just(PagedResponse.<RequestRefundResponse>builder()
                                .currentPage(0)
                                .pageSize(0)
                                .totalPages(0)
                                .items(null)
                                .build());    
                }
               
        }

        public Mono<List<RequestRefundResponse>> getRequestRefundDownload(
                String fromDate, String toDate,  String merchantId,
                String status, String searchKeyword, int page, int size,
                String queryMerchantIds) {
                try {
                        return oracleJdbcTemplate.execute((Connection con) -> {
                                CallableStatement cs = null;
                                ResultSet rs = null;
                                try {
                                        String finalStatus = replaceRefundStatuses(status);
                                        cs = con.prepareCall("{call search_request_refund_v3(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)}");
                                        // IN parameters
                                        cs.setString(1, "DOWNLOAD");
                                        cs.setClob(2, new StringReader(queryMerchantIds));
                                        cs.setString(3, formatDate(fromDate));
                                        cs.setString(4, formatDate(toDate));
                                        cs.setString(5, finalStatus);
                                        cs.setString(6, searchKeyword);
                                        cs.setInt(7, page);
                                        cs.setInt(8, size);
                                        cs.setInt(9, 0);

                                        // OUT params
                                        cs.registerOutParameter(10, Types.REF_CURSOR);
                                        cs.registerOutParameter(11, Types.NUMERIC);
                                        cs.registerOutParameter(12, Types.VARCHAR);

                                        cs.execute();

                                        rs = (ResultSet) cs.getObject(10);

                                        List<RequestRefundResponse> list = new ArrayList<>();
                                        while (rs.next()) {
                                                String state = rs.getString("s_transaction_state");
                                                RequestRefundResponse dto = new RequestRefundResponse();
                                                dto.setCurrency(rs.getString("s_currency"));
                                                dto.setMerchantId(rs.getString("s_merchant_id"));
                                                dto.setDocId(null);
                                                dto.setCreatedDate(rs.getString("d_transaction_date"));
                                                dto.setRequestId(rs.getString("s_transaction_id"));
                                                dto.setParentTransactionId(rs.getString("s_original_id"));
                                                dto.setRequestStatus(IConstants.TRANSACTION_STATUS_MAP.getOrDefault(state, state));
                                                dto.setCreatedBy(rs.getString("S_OPERATOR_ID"));
                                                dto.setPaymentMethod(rs.getString("s_input_type"));
                                                dto.setAmount(rs.getBigDecimal("n_amount"));
                                                dto.setMerchantTransactionRef(rs.getString("S_MERCHANT_TRANS_REF"));
                                                dto.setParentMerchantTransactionRef(rs.getString("S_PARENT_MERCHANT_TRANS_REF"));
                                                list.add(dto);
                                        }

                                        return Mono.just(list);
                                }finally{
                                        if (rs != null) try { rs.close(); } catch (SQLException ignored) {}
                                        if (cs != null) try { cs.close(); } catch (SQLException ignored) {}
                                }
                        
                        });    
                } catch (Exception e) {
                    logger.error("Error search_request_refund: ", e);
                    return Mono.just(List.of());  
                }
        }

        public Mono<Integer> countRequestRefund(
                String fromDate, String toDate,  String merchantId,
                String status, String searchKeyword, int page, int size,
                String queryMerchantIds) {
                
                try {
                        return oracleJdbcTemplate.execute((Connection con) -> {
                                CallableStatement cs = null;
                                ResultSet rs = null;
                                try {
                                        String finalStatus = replaceRefundStatuses(status);
                                        cs = con.prepareCall("{call search_request_refund_v3(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)}");
                                        // IN parameters
                                        cs.setString(1, "TOTAL");
                                        cs.setClob(2, new StringReader(queryMerchantIds));
                                        cs.setString(3, formatDate(fromDate));
                                        cs.setString(4, formatDate(toDate));
                                        cs.setString(5, finalStatus);
                                        cs.setString(6, searchKeyword);
                                        cs.setInt(7, page);
                                        cs.setInt(8, size);
                                        cs.setInt(9, 0);

                                        // OUT params
                                        cs.registerOutParameter(10, Types.REF_CURSOR);
                                        cs.registerOutParameter(11, Types.NUMERIC);
                                        cs.registerOutParameter(12, Types.VARCHAR);

                                        cs.execute();
                                        rs = (ResultSet) cs.getObject(10);
                                        int total = 0;
                                        while (rs.next()) {
                                                total = rs.getInt("N_TOTAL");  
                                        }
                                        return Mono.just(total);
                                }finally{
                                        if (rs != null) try { rs.close(); } catch (SQLException ignored) {}
                                        if (cs != null) try { cs.close(); } catch (SQLException ignored) {}
                                }
                              
                        });  
                } catch (Exception e) {
                        logger.error("Error countRequestRefund: ", e);
                         return Mono.just(0);
                }
               
        }

         private static String callApiGetTransactionHistory(String url, String xUserId){
                try {
                        logger.info("---------------start call api getTransactionHistory MA-----------");
                        HttpClient client = HttpClient.newBuilder()
                                .connectTimeout(Duration.ofSeconds(30))
                                .build();
                        HttpRequest request = HttpRequest.newBuilder()
                                .uri(URI.create(url))
                                .timeout(Duration.ofSeconds(30))
                                .header("Accept", "application/json")
                                .header("X-USER-ID", xUserId)
                                .GET()
                                .build();
                        logger.info("REQUEST: url=" +url+ " ,xUserId="+xUserId);
                        HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
                        logger.info("RESPONSE TRANSACTION HISTORY API: response code=" +response.statusCode()+ " ,response body="+response.body());
                        if (response.statusCode() == 200) {
                                return response.body();
                        } else {
                                return null;
                        }
                 } catch (Exception e) {
                        logger.error("Error call api get transaction history: ", e);
                        return null;
                }finally{
                        logger.info("------------end call api getTransactionHistory MA------------");
                }
        }

        private TransactionDTO mapJsonToTransactionDTOForIntenational(JsonNode hit) {
                try {
                        JsonNode source = hit;
                        if (source.isMissingNode())
                                return null;

                        JsonNode amountNode = source.path("amount");

                        String originalTransactionId = safeText(source, "original_transaction_id");
                        if (originalTransactionId == null || originalTransactionId.isEmpty())
                                return null;

                        TransactionDTO dto = TransactionDTO.builder()
                                        .originalTransactionId(originalTransactionId)
                                        .merchantId(safeText(source, "merchant_id"))
                                        .transactionId(safeText(source, "transaction_id"))
                                        .transType(safeText(source, "transaction_type"))
                                        .responseCode(safeText(source, "response_code"))
                                        .responseDescription(safeText(source, "note"))
                                        .transStatus(safeText(source, "advance_status"))
                                        .merchantTransactionRef(safeText(source, "merchant_transaction_ref"))
                                        .currency(safeText(amountNode, "currency"))
                                        .operatorId(safeText(source, "operator_id"))
                                        .dadId(safeText(source, "dadId"))
                                        .build();

                        // amount
                        String amountStr = safeText(amountNode, "total");
                        if (amountStr != null) {
                                dto.setTransAmount(new BigDecimal(amountStr));
                        }

                        // datetime
                        dto.setOrderCreatedTime(
                                        DateValidatorUtil.convertDate(safeText(source, "transaction_time")));
                        dto.setTransCreatedTime(DateValidatorUtil.convertDate(safeText(source, "transaction_time")));
                        dto.setTransCompletedTime(DateValidatorUtil.convertDate(safeText(source, "transaction_time")));

                        return dto;
                } catch (Exception e) {
                        logger.warn("Error mapping transaction DTO: ", e);
                        return null;
                }
        }

        private TransactionDTO mapJsonToTransactionDTOForDomestic(JsonNode hit) {
                try {
                        JsonNode source = hit;
                        if (source.isMissingNode())
                                return null;

                        JsonNode amountNode = source.path("amount");

                        String originalTransactionId = safeText(source, "original_id");
                        if (originalTransactionId == null || originalTransactionId.isEmpty())
                                return null;

                        TransactionDTO dto = TransactionDTO.builder()
                                        .originalTransactionId(originalTransactionId)
                                        .merchantId(safeText(source, "merchant_id"))
                                        .transactionId(safeText(source, "transaction_id"))
                                        .transType(safeText(source, "transaction_type"))
                                        .responseCode(safeText(source, "response_code"))
                                        .responseDescription(safeText(source, "note"))
                                        .transStatus(safeText(source, "advanced_status"))
                                        .merchantTransactionRef(safeText(source, "merchant_transaction_ref"))
                                        .currency(safeText(amountNode, "currency"))
                                        .operatorId(safeText(source, "operator_id"))
                                        .build();

                        // amount
                        String amountStr = safeText(amountNode, "total");
                        if (amountStr != null) {
                                dto.setTransAmount(new BigDecimal(amountStr));
                        }

                        // datetime
                        dto.setOrderCreatedTime(
                                        DateValidatorUtil.convertDate(safeText(source, "transaction_time")));
                        dto.setTransCreatedTime(DateValidatorUtil.convertDate(safeText(source, "transaction_time")));
                        dto.setTransCompletedTime(DateValidatorUtil.convertDate(safeText(source, "transaction_time")));

                        return dto;
                } catch (Exception e) {
                        logger.warn("Error mapping transaction DTO: ", e);
                        return null;
                }
        }

        private TransactionDTO mapJsonToTransactionDTOForQr(JsonNode hit) {
                try {
                        JsonNode source = hit;
                        if (source.isMissingNode())
                                return null;

                        JsonNode amountNode = source.path("amount");

                        String originalTransactionId = safeText(source, "originalId");
                        if (originalTransactionId == null || originalTransactionId.isEmpty())
                                return null;

                        TransactionDTO dto = TransactionDTO.builder()
                                        .originalTransactionId(originalTransactionId)
                                        .merchantId(safeText(source, "merchantId"))
                                        .transactionId(safeText(source, "transactionId"))
                                        .transType(safeText(source, "transactionType"))
                                        .responseCode(safeText(source, "response_code"))
                                        .responseDescription(safeText(source, "description"))
                                        .transStatus(safeText(source, "advancedStatus"))
                                        .merchantTransactionRef(safeText(source, "merchantTxnRef"))
                                        .currency(safeText(amountNode, "currency"))
                                        .operatorId(safeText(source, "operatorId"))
                                        .build();

                        // amount
                        String amountStr = safeText(amountNode, "total");
                        if (amountStr != null) {
                                dto.setTransAmount(new BigDecimal(amountStr));
                        }

                        // datetime
                        dto.setOrderCreatedTime(
                                        DateValidatorUtil.convertDate(safeText(source, "transactionTime")));
                        dto.setTransCreatedTime(DateValidatorUtil.convertDate(safeText(source, "transactionTime")));
                        dto.setTransCompletedTime(DateValidatorUtil.convertDate(safeText(source, "transactionTime")));

                        return dto;
                } catch (Exception e) {
                        logger.warn("Error mapping transaction DTO: ", e);
                        return null;
                }
        }

        private TransactionDTO mapJsonToTransactionDTOForVietQr(JsonNode hit) {
                try {
                        JsonNode source = hit;
                        if (source.isMissingNode())
                                return null;

                        JsonNode amountNode = source.path("amount");

                        String originalTransactionId = safeText(source, "original_id");
                        if (originalTransactionId == null || originalTransactionId.isEmpty())
                                return null;

                        TransactionDTO dto = TransactionDTO.builder()
                                        .originalTransactionId(originalTransactionId)
                                        .merchantId(safeText(source, "merchant_id"))
                                        .transactionId(safeText(source, "transaction_id"))
                                        .transType(safeText(source, "transaction_type"))
                                        .responseCode(safeText(source, "response_code"))
                                        .responseDescription(safeText(source, "note"))
                                        .transStatus(safeText(source, "status"))
                                        .merchantTransactionRef(safeText(source, "merchant_txn_ref"))
                                        .currency(safeText(amountNode, "currency"))
                                        .operatorId(safeText(source, "operator_id"))
                                        .build();

                        // amount
                        String amountStr = safeText(amountNode, "total");
                        if (amountStr != null) {
                                dto.setTransAmount(new BigDecimal(amountStr));
                        }

                        // datetime
                        dto.setOrderCreatedTime(
                                        DateValidatorUtil.convertDate(safeText(source, "transaction_time")));
                        dto.setTransCreatedTime(DateValidatorUtil.convertDate(safeText(source, "transaction_time")));
                        dto.setTransCompletedTime(DateValidatorUtil.convertDate(safeText(source, "transaction_time")));

                        return dto;
                } catch (Exception e) {
                        logger.warn("Error mapping transaction DTO: ", e);
                        return null;
                }
        }

        private TransactionDTO mapJsonToTransactionDTOForDirectDebit(JsonNode hit) {
                try {
                        JsonNode source = hit;
                        if (source.isMissingNode())
                                return null;

                        JsonNode amountNode = source.path("amount");

                        String originalTransactionId = safeText(source, "original_id");
                        if (originalTransactionId == null || originalTransactionId.isEmpty())
                                return null;

                        TransactionDTO dto = TransactionDTO.builder()
                                        .originalTransactionId(originalTransactionId)
                                        .merchantId(safeText(source, "merchant_id"))
                                        .transactionId(safeText(source, "transaction_id"))
                                        .transType(safeText(source, "transaction_type"))
                                        .responseCode(safeText(source, "response_code"))
                                        .responseDescription(safeText(source, "note"))
                                        .transStatus(safeText(source, "advance_status"))
                                        .merchantTransactionRef(safeText(source, "merchant_txn_ref"))
                                        .currency(safeText(amountNode, "currency"))
                                        .operatorId(safeText(source, "operator_id"))
                                        .build();

                        // amount
                        String amountStr = safeText(amountNode, "total");
                        if (amountStr != null) {
                                dto.setTransAmount(new BigDecimal(amountStr));
                        }

                        // datetime
                        dto.setOrderCreatedTime(
                                        DateValidatorUtil.convertDate(safeText(source, "transaction_time")));
                        dto.setTransCreatedTime(DateValidatorUtil.convertDate(safeText(source, "transaction_time")));
                        dto.setTransCompletedTime(DateValidatorUtil.convertDate(safeText(source, "transaction_time")));

                        return dto;
                } catch (Exception e) {
                        logger.error("Error mapping transaction DTO: ", e);
                        return null;
                }
        }

        private TransactionDTO mapJsonToTransactionDTOForBnpl(JsonNode hit) {
                try {
                        JsonNode source = hit;
                        if (source.isMissingNode())
                                return null;

                        JsonNode amountNode = source.path("orgAmount");

                        String originalTransactionId = safeText(source, "transactionOrgId");
                        if (originalTransactionId == null || originalTransactionId.isEmpty())
                                return null;

                        TransactionDTO dto = TransactionDTO.builder()
                                        .originalTransactionId(originalTransactionId)
                                        .merchantId(safeText(source, "merchantId"))
                                        .transactionId(safeText(source, "transactionId"))
                                        .transType(safeText(source, "transactionType"))
                                        .responseCode(safeText(source, "response_code"))
                                        .responseDescription(safeText(source, "description"))
                                        .transStatus(safeText(source, "status"))
                                        .merchantTransactionRef(safeText(source, "merchantTxnRef"))
                                        .currency(safeText(amountNode, "currency"))
                                        .operatorId(safeText(source, "operatorId"))
                                        .build();

                        // amount
                        String amountStr = safeText(amountNode, "total");
                        if (amountStr != null) {
                                dto.setTransAmount(new BigDecimal(amountStr));
                        }

                        // datetime
                        dto.setOrderCreatedTime(
                                        DateValidatorUtil.convertDateV2(safeText(source, "transactionTime")));
                        dto.setTransCreatedTime(DateValidatorUtil.convertDateV2(safeText(source, "transactionTime")));
                        dto.setTransCompletedTime(DateValidatorUtil.convertDateV2(safeText(source, "transactionTime")));

                        return dto;
                } catch (Exception e) {
                        logger.warn("Error mapping transaction DTO: ", e);
                        return null;
                }
        }

        private TransactionDTO mapJsonToTransactionDTOForUpos(JsonNode hit) {
                try {
                        JsonNode source = hit;
                        if (source.isMissingNode())
                                return null;

                        JsonNode amountNode = source.path("amount");

                        String originalTransactionId = safeText(source, "orgTransactionId");
                        if (originalTransactionId == null || originalTransactionId.isEmpty())
                                return null;

                        TransactionDTO dto = TransactionDTO.builder()
                                        .originalTransactionId(originalTransactionId)
                                        .merchantId(safeText(source, "merchant_id"))
                                        .transactionId(safeText(source, "orgTransactionId"))
                                        .transType(safeText(source, "transactionType"))
                                        .responseCode(safeText(source, "response_code"))
                                        .responseDescription(safeText(source, "note"))
                                        .transStatus(safeText(source, "transactionState"))
                                        .merchantTransactionRef(safeText(source, "merchantTnxRef"))
                                        .currency(safeText(amountNode, "currency"))
                                        .operatorId(safeText(source, "operatorId"))
                                        .build();

                        // amount
                        String amountStr = safeText(amountNode, "total");
                        if (amountStr != null) {
                                dto.setTransAmount(new BigDecimal(amountStr));
                        }

                        // datetime
                        dto.setOrderCreatedTime(
                                        DateValidatorUtil.convertDate(safeText(source, "transactionTime")));
                        dto.setTransCreatedTime(DateValidatorUtil.convertDate(safeText(source, "transactionTime")));
                        dto.setTransCompletedTime(DateValidatorUtil.convertDate(safeText(source, "transactionTime")));

                        return dto;
                } catch (Exception e) {
                        logger.error("Error mapping transaction DTO: ", e);
                        return null;
                }
        }

        @Override
        public FileDownloadDto createExcel(DownloadTaskEntity task, String taskType) {
                switch (taskType) {
                        case IConstants.TYPE_TRANSACTION:
                                return createTransactionExcel(task);
                
                        case IConstants.TYPE_REQUEST_REFUND:
                                return createRequestRefundExcel(task);
                        default:
                                logger.error("Unsupported task type: " + taskType);
                                return null;
                }
        }

        private FileDownloadDto createRequestRefundExcel(DownloadTaskEntity task) {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

                try {
                logger.info("Start exporting Excel for taskId={}, fileName={}", task.getId(),
                        task.getFileName());

                // Tạo thư mục lưu file nếu chưa có
                String baseDir = exportFileProperties.getBaseDir();
                File dir = new File(baseDir);
                if (!dir.exists())
                        dir.mkdirs();

                String outputPath = baseDir + task.getFileHashName();

                // Parse requestParams từ task
                Map<String, Object> params = objectMapper.readValue(task.getRequestParams(),
                        new TypeReference<>() {
                        });
                String fromDate = (String) params.get("fromDate");
                String toDate = (String) params.get("toDate");
                String merchantId = (String) params.get("merchantId");
                String status = (String) params.get("status");
                String searchKeyword = (String) params.get("searchKeyword");
                String hl = (String) params.get("hl");
                int page = 0;
                int size = 500;
                List<RequestRefundResponse> allTransactions = new ArrayList<>();
                if (merchantId != null && !merchantId.isEmpty()) {
                        while (true) {
                                try {
                                        Mono<List<RequestRefundResponse>> dataMono = getRequestRefundDownload(fromDate, toDate, merchantId, status, searchKeyword, page, size, merchantId);
                                        List<RequestRefundResponse> transList = dataMono.block();
                                        if (transList == null || transList.isEmpty())
                                                break;
                                        allTransactions.addAll(transList);
                                        if (transList.size() < size)
                                                break;
                                        page++;
                                } catch (Exception e) {
                                        logger.error("Error getTransactionAll: ", e);
                                        break;
                                }
                        }
                }

                String fileTemplate = "";
                switch(hl) {
                        case IConstants.TYPE_LANGUAGE_EN:
                                fileTemplate = "template/EN_Refund_Request_Download_Template.xlsx";
                                break;
                        case IConstants.TYPE_LANGUAGE_VI:
                                fileTemplate = "template/VI_Refund_Request_Download_Template.xlsx";
                                break;
                        default:
                                fileTemplate = "template/EN_Refund_Request_Download_Template.xlsx";
                }

                // Đọc file template từ resources/template/
                try (
                        InputStream is = getClass().getClassLoader()
                                .getResourceAsStream(fileTemplate);
                        Workbook workbook = new XSSFWorkbook(is);
                        FileOutputStream fos = new FileOutputStream(outputPath)) {
                        if (is == null)
                        throw new FileNotFoundException("Template not found in resources/template/");

                        Sheet sheet = workbook.getSheetAt(0);
                        int startRow = 1;

                        logger.info("Writing {} rows to Excel file", allTransactions.size());

                        for (int i = 0; i < allTransactions.size(); i++) {
                                RequestRefundResponse dto = allTransactions.get(i);
                                String requestStatus = dto.getRequestStatus();
                                switch(hl) {
                                        case IConstants.TYPE_LANGUAGE_VI:
                                                requestStatus = IConstants.REFUND_REQUEST_LANGUAGE.getOrDefault(requestStatus, requestStatus);
                                                break;
                                }

                                Row row = sheet.createRow(startRow + i);
                                row.createCell(0).setCellValue(i + 1);
                                row.createCell(1).setCellValue(dto.getMerchantId());
                                row.createCell(2).setCellValue(dto.getMerchantTransactionRef());
                                row.createCell(3).setCellValue(dto.getParentMerchantTransactionRef());
                                row.createCell(4).setCellValue(dto.getAmount() != null ? dto.getAmount().toString(): "");
                                row.createCell(5).setCellValue(dto.getCurrency());
                                row.createCell(6).setCellValue(dto.getCreatedDate().replace("T", " "));
                                row.createCell(7).setCellValue(dto.getCreatedBy());
                                row.createCell(8).setCellValue(requestStatus);
                        }

                        workbook.write(fos);
                }
                // Tạo FileDownloadDto trả về
                File file = new File(outputPath);
                FileDownloadDto dto = new FileDownloadDto();
                dto.setFile_hash_name(task.getFileHashName());
                dto.setFile_name(task.getFileName());
                dto.setFile_path(outputPath);
                dto.setFile_size(file.length());
                dto.setUser(task.getUserId());
                logger.info("Exported {} transactions to {}", allTransactions.size(), outputPath);
                return dto;

                } catch (Exception e) {
                logger.error("Error while exporting Excel for task {}: {}", task.getId(), e.getMessage(), e);
                throw new RuntimeException("Export Excel failed", e);
                }
        }

        private FileDownloadDto createTransactionExcel(DownloadTaskEntity task) {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

                try {
                        logger.info("Start exporting Excel for taskId={}, fileName={}", task.getId(),
                                        task.getFileName());

                        // Tạo thư mục lưu file nếu chưa có
                        String baseDir = exportFileProperties.getBaseDir();
                        File dir = new File(baseDir);
                        if (!dir.exists())
                                dir.mkdirs();

                        String outputPath = baseDir + task.getFileHashName();

                        // Parse requestParams từ task
                        Map<String, Object> params = objectMapper.readValue(task.getRequestParams(),
                                        new TypeReference<>() {
                                        });
                        String fromDate = (String) params.get("fromDate");
                        String toDate = (String) params.get("toDate");
                        String merchantId = (String) params.get("merchantId");
                        // String transId = (String) params.get("transId");
                        String paymentMethod = (String) params.get("paymentMethod");
                        // String sortOrder = (String) params.getOrDefault("sortOrder", "desc");
                        String transactionStatus = (String) params.get("transactionStatus");
                        String transactionType = (String) params.get("transactionType");
                        String searchKeyword = (String) params.get("searchKeyword");
                        String isPromotion = (String) params.get("isPromotion");
                        String searchType = (String) params.get("searchType");
                        String orderSource = (String) params.get("orderSource");
                        String hl = (String) params.get("hl");

                        int page = 0;
                        int size = 500;
                        List<TransactionDTO> allTransactions = new ArrayList<>();
                        if (merchantId != null && !merchantId.isEmpty()) {
                                while (true) {
                                        Mono<List<TransactionDTO>> dataMono = searchGeneralReportDownload(fromDate, toDate, "", "", paymentMethod,
                                                        transactionStatus, transactionType, "", "", isPromotion, orderSource, searchType, searchKeyword, page, size, merchantId, merchantId, merchantId, merchantId, merchantId);
                                        List<TransactionDTO> hits = dataMono.block();
                                        allTransactions.addAll(hits);
                                        if (hits.size() < size) {
                                                break;
                                        }
                                                
                                        page++;
                                }
                                logger.info("Fetched total {} transactions", allTransactions.size());

                        }

                        String fileTemplate = "";
                        switch(hl) {
                                case IConstants.TYPE_LANGUAGE_EN:
                                        fileTemplate = "template/EN_Transaction_Download_Template.xlsx";
                                        break;
                                case IConstants.TYPE_LANGUAGE_VI:
                                        fileTemplate = "template/VI_Transaction_Download_Template.xlsx";
                                        break;
                                default:
                                        fileTemplate = "template/EN_Transaction_Download_Template.xlsx";
                        }
                        
                        // Đọc file template từ resources/template/
                        try (
                                InputStream is = getClass().getClassLoader().getResourceAsStream(fileTemplate);
                                Workbook workbook = new XSSFWorkbook(is);
                                FileOutputStream fos = new FileOutputStream(outputPath)) {
                                if (is == null)
                                        throw new FileNotFoundException("Template not found in resources/template/");

                                Sheet sheet = workbook.getSheetAt(0);
                                int startRow = 1;

                                logger.info("Writing {} rows to Excel file", allTransactions.size());

                                for (int i = 0; i < allTransactions.size(); i++) {
                                        TransactionDTO dto = allTransactions.get(i);
                                        String payMethod = dto.getPaymentMethod();
                                        String paySource = dto.getPaymentSource();
                                        String transType = dto.getTransType();
                                        String transStatus = dto.getTransStatus();
                                        
                                        switch(hl) {
                                                case IConstants.TYPE_LANGUAGE_VI:
                                                        payMethod = IConstants.TRANSACTION_LANGUAGE.getOrDefault(payMethod, payMethod);
                                                        paySource = IConstants.TRANSACTION_LANGUAGE.getOrDefault(paySource, paySource);
                                                        transType = IConstants.TRANSACTION_LANGUAGE.getOrDefault(transType, transType);
                                                        transStatus = IConstants.TRANSACTION_LANGUAGE.getOrDefault(transStatus, transStatus);
                                                        break;
                                                case IConstants.TYPE_LANGUAGE_EN:
                                                        payMethod = IConstants.PAYMENT_METHOD_MAP_LANGUAGE.getOrDefault(payMethod, payMethod);
                                                        paySource = IConstants.PAYMENT_METHOD_MAP_LANGUAGE.getOrDefault(paySource, paySource);
                                                        break;
                                        }
                                        Row row = sheet.createRow(startRow + i);
                                        row.createCell(0).setCellValue(i + 1);
                                        row.createCell(1).setCellValue(dto.getMerchantId());
                                        row.createCell(2).setCellValue(dto.getMerchantTransactionRef());
                                        row.createCell(3).setCellValue(dto.getOrderReference());
                                        row.createCell(4)
                                                        .setCellValue(dto.getTransAmount() != null
                                                                        ? dto.getTransAmount().toString()
                                                                        : "");
                                        row.createCell(5).setCellValue(dto.getCurrency());
                                        row.createCell(6).setCellValue(dto.getTransCreatedTime().replace("T", " "));
                                        row.createCell(7).setCellValue(dto.getTransCreatedTime().replace("T", " "));
                                        row.createCell(8).setCellValue(dto.getTransCreatedTime().replace("T", " "));
                                        row.createCell(9).setCellValue(dto.getOrderSource());
                                        row.createCell(10).setCellValue(payMethod);
                                        row.createCell(11).setCellValue(paySource);
                                        row.createCell(12).setCellValue(dto.getCardNumber());
                                        row.createCell(13).setCellValue(transType);
                                        row.createCell(14).setCellValue(transStatus);
                                }

                                workbook.write(fos);
                        }
                        // Tạo FileDownloadDto trả về
                        File file = new File(outputPath);
                        FileDownloadDto dto = new FileDownloadDto();
                        dto.setFile_hash_name(task.getFileHashName());
                        dto.setFile_name(task.getFileName());
                        dto.setFile_path(outputPath);
                        dto.setFile_size(file.length());
                        dto.setUser(task.getUserId());
                        logger.info("Exported {} transactions to {}", allTransactions.size(), outputPath);
                        return dto;

                } catch (Exception e) {
                        logger.error("Error while exporting Excel for task {}: {}", task.getId(), e.getMessage(), e);
                        throw new RuntimeException("Export Excel failed", e);
                }
        }

        private TransactionDTO mapJsonToTransactionDTO(JsonNode hit) {
                try {
                        JsonNode source = hit.path("_source");
                        if (source.isMissingNode())
                                return null;

                        JsonNode merchantNode = source.path("msp_merchant");
                        JsonNode enrichTxnNode = source.path("enrich_txn");
                        JsonNode paymentNode = source.path("msp_payment");
                        JsonNode invoiceNode = source.path("msp_invoice");

                        String docId = safeText(hit, "_id");
                        if (docId == null || docId.isEmpty())
                                return null;

                        TransactionDTO dto = TransactionDTO.builder()
                                        .docId(docId)
                                        .merchantId(safeText(merchantNode, "s_id"))
                                        .baseId(safeText(enrichTxnNode, "s_base_id"))
                                        .transactionId(safeText(enrichTxnNode, "s_id"))
                                        .orderReference(safeText(invoiceNode, "s_info"))
                                        .cardNumber(safeText(paymentNode, "s_e_card_number"))
                                        .transType(safeText(enrichTxnNode, "s_txn_type"))
                                        .responseCode(safeText(enrichTxnNode, "s_response_code"))
                                        .responseDescription(safeText(enrichTxnNode, "s_e_response_code"))
                                        .transStatus(safeText(enrichTxnNode, "s_state"))
                                        .paymentSource(safeText(paymentNode, "s_e_gate_label"))
                                        .currency(safeText(invoiceNode, "s_currencies"))
                                        .paymentMethod(safeText(paymentNode, "s_e_pay_method"))
                                        .orderSource(safeText(invoiceNode, "s_e_order_source"))
                                        .build();

                        // amount
                        String amountStr = safeText(invoiceNode, "n_amount");
                        if (amountStr != null) {
                                dto.setTransAmount(new BigDecimal(amountStr));
                        }

                        // datetime
                        dto.setOrderCreatedTime(
                                        DateValidatorUtil.convertDate(safeText(enrichTxnNode, "d_original_date")));
                        dto.setTransCreatedTime(DateValidatorUtil.convertDate(safeText(enrichTxnNode, "d_create")));
                        dto.setTransCompletedTime(DateValidatorUtil.convertDate(safeText(enrichTxnNode, "d_update")));

                        return dto;
                } catch (Exception e) {
                        logger.warn("Error mapping transaction DTO: ", e);
                        return null;
                }
        }
        @Override
        public Mono<Boolean> checkPermission(String permission) {
                return merchantService.getPermissionByPartnerIdAndUserId().map(userPermissions -> {
                        return CheckPermissionUtil.checkPermission(userPermissions, permission);
                });
        }

        @Override
        public Mono<Map<String, ActionMessage>> checkPerAndActionByUserId(TransactionDetailResponse detail, List<TransactionHistoryDTO> lstTransactionHistory, BigDecimal amount, String action) {
                String originalTransactionId = detail.getTransactionInformation().getTransactionId();
                try {
                        logger.info("---------------start checkPerAndActionByUserId-----------");
                        return merchantService.getPermissionByPartnerIdAndUserId().flatMap(userPermissions -> {
                                if (action != IConstants.ACTION_APPROVE_REJECT && (lstTransactionHistory == null || lstTransactionHistory.isEmpty())) {
                                        logger.info("lstTransactionHistory is null, transactionId={}", originalTransactionId);
                                        return Mono.just(CheckPermissionUtil.userPermissionDefault());
                                }
                                
                                if (userPermissions.isEmpty()) {
                                        logger.error("No merchantId matched -> returning empty response");
                                        return Mono.just(CheckPermissionUtil.userPermissionDefault());
                                }

                                AcquirerDTO acquirerDTO = acquirerService.getAllAcquirers();
                                if (acquirerDTO == null || acquirerDTO.getList().isEmpty()) {
                                        logger.info("acquirerDTO is null, transactionId={}", originalTransactionId);
                                        return Mono.just(CheckPermissionUtil.userPermissionDefault());
                                }

                                MSPMerchant merchant = null;
                                if (detail.getTransactionInformation().getPaymentMethod().equalsIgnoreCase(IConstants.PAYMENT_METHOD_VIETQR)) {
                                        merchant = getMSPMerchantById(detail.getMerchantInformation().getMerchantId());
                                }


                                Map<String, ActionMessage> actions = new HashMap<>();

                                switch (action) {
                                        case IConstants.ACTION_VOID:
                                                actions.put(IConstants.ACTION_VOID, CheckPermissionUtil.checkPerActionVoid(originalTransactionId, acquirerDTO, lstTransactionHistory, detail, userPermissions));
                                                break;
                                        case IConstants.ACTION_REFUND:
                                                actions.put(IConstants.ACTION_REFUND, CheckPermissionUtil.checkPerActionRefund(originalTransactionId, amount, lstTransactionHistory, detail, merchant, userPermissions));
                                                break;
                                        case IConstants.ACTION_CAPTURE:
                                                actions.put(IConstants.ACTION_CAPTURE, CheckPermissionUtil.checkPerActionCapture(originalTransactionId, amount, lstTransactionHistory, detail, userPermissions));
                                                break;
                                        case IConstants.ACTION_APPROVE_REJECT:
                                                actions.put(IConstants.ACTION_APPROVE_REJECT, CheckPermissionUtil.checkPerActionApproveReject(originalTransactionId, detail, userPermissions));
                                                break;
                                        default:
                                                // all
                                                actions.put(IConstants.ACTION_VOID, CheckPermissionUtil.checkPerActionVoid(originalTransactionId, acquirerDTO, lstTransactionHistory, detail, userPermissions));
                                                actions.put(IConstants.ACTION_REFUND, CheckPermissionUtil.checkPerActionRefund(originalTransactionId, amount, lstTransactionHistory, detail, merchant, userPermissions));
                                                actions.put(IConstants.ACTION_CAPTURE, CheckPermissionUtil.checkPerActionCapture(originalTransactionId, amount, lstTransactionHistory, detail, userPermissions));
                                                actions.put(IConstants.ACTION_APPROVE_REJECT, CheckPermissionUtil.checkPerActionApproveReject(originalTransactionId, detail, userPermissions));
                                }
                                

                                return Mono.just(actions);
                        });
                } catch (Exception e) {
                        logger.info("Error when checkPermissionByUserId: transactionId={}, error={}", originalTransactionId, e);
                        return Mono.just(CheckPermissionUtil.userPermissionDefault());
                }
        }

        private TransactionDetailResponse getAdvanceFeeInfo(TransactionDetailResponse detail) {
                try {
                        logger.info("=============== Start getAdvanceFeeInfo ===================");
                        return oracleJdbcTemplate.execute((Connection con) -> {
                                CallableStatement cs = null;
                                ResultSet rs = null;
                                try {
                                        cs = con.prepareCall("{? = call ONEFIN.get_advance_fee_info(?, ?)}");
                                        cs.registerOutParameter(1, Types.REF_CURSOR);
                                        cs.setString(2, detail.getMerchantInformation().getMerchantId());
                                        cs.setString(3, detail.getTransactionInformation().getMerchantTransRef());

                                        cs.execute();
                                        rs = (ResultSet) cs.getObject(1);
                                        if (rs.next()) {
                                                FeeInformation feeInformation = FeeInformation.builder()
                                                        .transactionProcessingFee(new BigDecimal(rs.getString("FIX_FEE_VND")))
                                                        .cardPaymentFee(new BigDecimal(rs.getString("N_PERCENT_FEE_VND")))
                                                        .itaFee(new BigDecimal(rs.getString("N_FEE_ITA_ADV")))
                                                        .build();

                                                AdvanceInformation advanceInformation = AdvanceInformation.builder()
                                                        .pvNo(rs.getString("S_PAYMENT_VOUCHER"))
                                                        .contractNo(rs.getString("S_CONTRACT_CODE"))
                                                        .transferDate(rs.getString("D_CHECK"))
                                                        .status(rs.getString("S_STATE"))
                                                        .amountAdvance(new BigDecimal(rs.getString("N_AMOUNT_ADV_CURRENT"))) 
                                                        .build();

                                                TransactionDetailResponse dto = TransactionDetailResponse.builder()
                                                        .feeInformation(feeInformation)
                                                        .advanceInformation(advanceInformation)
                                                        .build();

                                                return dto;
                                        }

                                        return null;
                                } finally {
                                        if (rs != null) try { rs.close(); } catch (SQLException ignored) {}
                                        if (cs != null) try { cs.close(); } catch (SQLException ignored) {}
                                }
                        });
                } catch (Exception e) {
                        logger.error("Error getAdvanceFeeInfo: ", e);
                        return null;
                }
        }

        private MSPMerchant getMSPMerchantById(String merchantId) {
                try {
                        logger.info("==================== Start getMerchantById ====================");
                        return oracleJdbcTemplate.execute((Connection con) -> {
                                CallableStatement cs = null;
                                ResultSet rs = null;
                                try {
                                        cs = con.prepareCall("{? = call ONEDATA.get_msp_merchant_by_id(?, ?)}");
                                        cs.registerOutParameter(1, Types.VARCHAR);
                                        cs.registerOutParameter(2, Types.REF_CURSOR);
                                        cs.setString(3, merchantId);

                                        cs.execute();
                                        rs = (ResultSet) cs.getObject(2);
                                        if (rs.next()) {
                                                MSPMerchant mspMerchant = new MSPMerchant();
                                                mspMerchant.setSId(rs.getString("s_id"));
                                                mspMerchant.setSState(rs.getString("s_state"));
                                                mspMerchant.setSData(rs.getString("s_data"));
                                                return mspMerchant;
                                        }

                                        return null;
                                } finally {
                                        if (rs != null) try { rs.close(); } catch (SQLException ignored) {}
                                        if (cs != null) try { cs.close(); } catch (SQLException ignored) {}
                                }
                        });
                } catch (Exception e) {
                        logger.error("Error getMerchantById: ", e);
                        return null;
                }
        }

        @Override
        public List<TransactionHistoryDTO> getTransactionHistory(String originalTransactionId, String paymentMethod,
                        String userId) {
                try {
                        logger.info("---------------start call getTransactionHistory-----------");
                        logger.info("REQUEST: originalTransactionId="+originalTransactionId+" ,paymentMethod="+paymentMethod+" ,xUserId="+userId);
                        List<TransactionHistoryDTO> data = new ArrayList<>();
                        if (IConstants.PAYMENT_METHOD_QT.equalsIgnoreCase(paymentMethod) || 
                                IConstants.PAYMENT_METHOD_SS_PAY.equalsIgnoreCase(paymentMethod) || 
                                IConstants.PAYMENT_METHOD_GG_PAY.equalsIgnoreCase(paymentMethod) || 
                                IConstants.PAYMENT_METHOD_AP_PAY.equalsIgnoreCase(paymentMethod) || 
                                IConstants.PAYMENT_METHOD_INSTALLMENT.equalsIgnoreCase(paymentMethod)) {
                                String opsStrData = callApiGetTransactionHistory(internationalUrl + "/international/transaction/" + originalTransactionId + "/history", userId);
                                logger.info("RESPONSE TRANSACTION HISTORY INTERNATIONAL: "+opsStrData);
                                ObjectMapper mapper = new ObjectMapper();
                                mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                                JsonNode root = mapper.readTree(opsStrData);
                                if (root == null){
                                        throw new NoItemExistsException("Transaction history with originalTransactionId " + originalTransactionId + " not found");
                                }
                                JsonNode transactions = root.path("transactions");
                                if (transactions.isArray()) {
                                        for (JsonNode tx : transactions) {
                                                TransactionDTO dto = mapJsonToTransactionDTOForIntenational(tx);
                                                if (dto != null) {
                                                        TransactionHistoryDTO dtoHis = TransactionHistoryDTO.builder()
                                                                                        .transactionId(dto.getTransactionId())
                                                                                        .date(dto.getTransCreatedTime())
                                                                                        .amount(dto.getTransAmount())
                                                                                        .currency(dto.getCurrency())
                                                                                        .description(dto.getResponseDescription())
                                                                                        .transactionType(dto.getTransType())
                                                                                        .status(dto.getTransStatus())
                                                                                        .merchantTransRef(dto.getMerchantTransactionRef())
                                                                                        .operatorId(dto.getOperatorId())
                                                                                        .originalTransactionId(dto.getOriginalTransactionId())
                                                                                        .dadId(dto.getDadId())
                                                                                        .build();
                                                        data.add(dtoHis);
                                                }
                                        }
                                }
                        } else if ("ND".equalsIgnoreCase(paymentMethod)) {
                                String opsStrData = callApiGetTransactionHistory(domesticUrl + "/domestic/transaction/" + originalTransactionId + "/history", userId);
                                logger.info("RESPONSE TRANSACTION HISTORY DOMESTIC: "+opsStrData);
                                ObjectMapper mapper = new ObjectMapper();
                                mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                                JsonNode root = mapper.readTree(opsStrData);
                                if (root == null){
                                        throw new NoItemExistsException("Transaction history with originalTransactionId " + originalTransactionId + " not found");
                                }
                                JsonNode transactions = root.path("transactions");
                                if (transactions.isArray()) {
                                        for (JsonNode tx : transactions) {
                                                TransactionDTO dto = mapJsonToTransactionDTOForDomestic(tx);
                                                if (dto != null) {
                                                        TransactionHistoryDTO dtoHis = TransactionHistoryDTO.builder()
                                                                                        .transactionId(dto.getTransactionId())
                                                                                        .date(dto.getTransCreatedTime())
                                                                                        .amount(dto.getTransAmount())
                                                                                        .currency(dto.getCurrency())
                                                                                        .description(dto.getResponseDescription())
                                                                                        .transactionType(dto.getTransType())
                                                                                        .status(dto.getTransStatus())
                                                                                        .merchantTransRef(dto.getMerchantTransactionRef())
                                                                                        .operatorId(dto.getOperatorId())
                                                                                        .originalTransactionId(dto.getOriginalTransactionId())
                                                                                        .build();
                                                        data.add(dtoHis);
                                                }
                                        }
                                }
                        } else if ("QR".equalsIgnoreCase(paymentMethod)) {
                                String opsStrData = callApiGetTransactionHistory(qrUrl + "/qr/transaction/" + originalTransactionId + "/history", userId);
                                logger.info("RESPONSE TRANSACTION HISTORY QR: "+opsStrData);
                                ObjectMapper mapper = new ObjectMapper();
                                mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                                JsonNode root = mapper.readTree(opsStrData);
                                if (root == null){
                                        throw new NoItemExistsException("Transaction history with originalTransactionId " + originalTransactionId + " not found");
                                }
                                JsonNode transactions = root.path("transactions");
                                if (transactions.isArray()) {
                                        for (JsonNode tx : transactions) {
                                                TransactionDTO dto = mapJsonToTransactionDTOForQr(tx);
                                                if (dto != null) {
                                                        TransactionHistoryDTO dtoHis = TransactionHistoryDTO.builder()
                                                                                        .transactionId(dto.getTransactionId())
                                                                                        .date(dto.getTransCreatedTime())
                                                                                        .amount(dto.getTransAmount())
                                                                                        .currency(dto.getCurrency())
                                                                                        .description(dto.getResponseDescription())
                                                                                        .transactionType(dto.getTransType())
                                                                                        .status(dto.getTransStatus())
                                                                                        .merchantTransRef(dto.getMerchantTransactionRef())
                                                                                        .operatorId(dto.getOperatorId())
                                                                                        .originalTransactionId(dto.getOriginalTransactionId())
                                                                                        .build();
                                                        data.add(dtoHis);
                                                }
                                        }
                                }
                        } else if ("VIETQR".equalsIgnoreCase(paymentMethod)) {
                                String opsStrData = callApiGetTransactionHistory(vietqrUrl + "/vietqr/transaction/" + originalTransactionId + "/history", userId);
                                logger.info("RESPONSE TRANSACTION HISTORY VIETQR: "+opsStrData);
                                ObjectMapper mapper = new ObjectMapper();
                                mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                                JsonNode root = mapper.readTree(opsStrData);
                                if (root == null){
                                        throw new NoItemExistsException("Transaction history with originalTransactionId " + originalTransactionId + " not found");
                                }
                                JsonNode transactions = root.path("transactions");
                                if (transactions.isArray()) {
                                        for (JsonNode tx : transactions) {
                                                TransactionDTO dto = mapJsonToTransactionDTOForVietQr(tx);
                                                if (dto != null) {
                                                        TransactionHistoryDTO dtoHis = TransactionHistoryDTO.builder()
                                                                                        .transactionId(dto.getTransactionId())
                                                                                        .date(dto.getTransCreatedTime())
                                                                                        .amount(dto.getTransAmount())
                                                                                        .currency(dto.getCurrency())
                                                                                        .description(dto.getResponseDescription())
                                                                                        .transactionType(dto.getTransType())
                                                                                        .status(dto.getTransStatus())
                                                                                        .merchantTransRef(dto.getMerchantTransactionRef())
                                                                                        .operatorId(dto.getOperatorId())
                                                                                        .originalTransactionId(dto.getOriginalTransactionId())
                                                                                        .build();
                                                        data.add(dtoHis);
                                                }
                                        }
                                }
                        } else if ("BNPL".equalsIgnoreCase(paymentMethod)) {
                                String opsStrData = callApiGetTransactionHistory(bnplUrl + "/bnpl/transaction/" + originalTransactionId + "/history" + "?transaction_id=" + originalTransactionId, userId);
                                logger.info("RESPONSE TRANSACTION HISTORY BNPL: "+opsStrData);
                                ObjectMapper mapper = new ObjectMapper();
                                mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                                JsonNode root = mapper.readTree(opsStrData);
                                if (root == null){
                                        throw new NoItemExistsException("Transaction history with originalTransactionId " + originalTransactionId + " not found");
                                }
                                JsonNode transactions = root.path("list");
                                if (transactions.isArray()) {
                                        for (JsonNode tx : transactions) {
                                                TransactionDTO dto = mapJsonToTransactionDTOForBnpl(tx);
                                                if (dto != null) {
                                                        TransactionHistoryDTO dtoHis = TransactionHistoryDTO.builder()
                                                                                        .transactionId(dto.getTransactionId())
                                                                                        .date(dto.getTransCreatedTime())
                                                                                        .amount(dto.getTransAmount())
                                                                                        .currency(dto.getCurrency())
                                                                                        .description(dto.getResponseDescription())
                                                                                        .transactionType(dto.getTransType())
                                                                                        .status(dto.getTransStatus())
                                                                                        .merchantTransRef(dto.getMerchantTransactionRef())
                                                                                        .operatorId(dto.getOperatorId())
                                                                                        .originalTransactionId(dto.getOriginalTransactionId())
                                                                                        .build();
                                                        data.add(dtoHis);
                                                }
                                        }
                                }
                        } else if ("DD".equalsIgnoreCase(paymentMethod)) {
                                String opsStrData = callApiGetTransactionHistory(directDebitUrl + "/direct-debit/transaction/" + originalTransactionId + "/history", userId);
                                logger.info("RESPONSE TRANSACTION HISTORY DIRECT DEBIT: "+opsStrData);
                                ObjectMapper mapper = new ObjectMapper();
                                mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                                JsonNode root = mapper.readTree(opsStrData);
                                if (root == null){
                                        throw new NoItemExistsException("Transaction history with originalTransactionId " + originalTransactionId + " not found");
                                }
                                JsonNode transactions = root.path("transactions");
                                if (transactions.isArray()) {
                                        for (JsonNode tx : transactions) {
                                                TransactionDTO dto = mapJsonToTransactionDTOForDirectDebit(tx);
                                                if (dto != null) {
                                                        TransactionHistoryDTO dtoHis = TransactionHistoryDTO.builder()
                                                                                        .transactionId(dto.getTransactionId())
                                                                                        .date(dto.getTransCreatedTime())
                                                                                        .amount(dto.getTransAmount())
                                                                                        .currency(dto.getCurrency())
                                                                                        .description(dto.getResponseDescription())
                                                                                        .transactionType(dto.getTransType())
                                                                                        .status(dto.getTransStatus())
                                                                                        .merchantTransRef(dto.getMerchantTransactionRef())
                                                                                        .operatorId(dto.getOperatorId())
                                                                                        .originalTransactionId(dto.getOriginalTransactionId())
                                                                                        .build();
                                                        data.add(dtoHis);
                                                }
                                        }
                                }
                        } else if ("UPOS".equalsIgnoreCase(paymentMethod)) {
                                String transactionId = originalTransactionId.split(";")[0];
                                String opsStrData = callApiGetTransactionHistory(uposUrl + "/upos/transaction/" + transactionId + "/history", userId);
                                logger.info("RESPONSE TRANSACTION HISTORY UPOS: "+opsStrData);
                                ObjectMapper mapper = new ObjectMapper();
                                mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                                JsonNode root = mapper.readTree(opsStrData);
                                if (root == null){
                                        throw new NoItemExistsException("Transaction history with originalTransactionId " + transactionId + " not found");
                                }
                                JsonNode transactions = root.path("list");
                                if (transactions.isArray()) {
                                        for (JsonNode tx : transactions) {
                                                TransactionDTO dto = mapJsonToTransactionDTOForUpos(tx);
                                                if (dto != null) {
                                                        TransactionHistoryDTO dtoHis = TransactionHistoryDTO.builder()
                                                                                        .transactionId(dto.getTransactionId())
                                                                                        .date(dto.getTransCreatedTime())
                                                                                        .amount(dto.getTransAmount())
                                                                                        .currency(dto.getCurrency())
                                                                                        .description(dto.getResponseDescription())
                                                                                        .transactionType(dto.getTransType())
                                                                                        .status(dto.getTransStatus())
                                                                                        .merchantTransRef(dto.getMerchantTransactionRef())
                                                                                        .operatorId(dto.getOperatorId())
                                                                                        .originalTransactionId(dto.getOriginalTransactionId())
                                                                                        .build();
                                                        data.add(dtoHis);
                                                }
                                        }
                                }
                        }else{
                            logger.error("dont have paymethod: ", paymentMethod);    
                        }

                       return normalizeTransactionHistoryStatuses(data);  
                } catch (Exception e) {
                        logger.error("Error getTransactionHistory: ", e);
                        return null;   
                }finally{
                        logger.info("------------end call getTransactionHistory------------");
                }
        }

        public Mono<PagedResponse<UposListResponse>> getTransactionUpos(
                String fromDate, String toDate,  String merchantId, String tid, String transactionId, String orderReference,
                String merchantTransRef, String cardNumber, String approvalCode, String paymentChannel, String cardType,
                String transactionType, String transactionState, String installmentStatus, int page, int size) {
                try {
                     return oracleJdbcTemplate.execute((Connection con) -> {
                        CallableStatement cs = null;
                        ResultSet rs = null;
                        ResultSet nTotal = null;
                        long  total = 0;
                        try {
                                cs = con.prepareCall("{call transaction_search_upos(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)}");
                                // OUT params
                                cs.registerOutParameter(1, Types.REF_CURSOR);
                                cs.registerOutParameter(2, Types.REF_CURSOR);
                                cs.registerOutParameter(3, Types.NUMERIC);
                                cs.registerOutParameter(4, Types.VARCHAR);
                                // IN parameters
                                cs.setString(5, "SELECT");
                                cs.setString(6, formatDate(fromDate));
                                cs.setString(7, formatDate(toDate));
                                cs.setString(8, merchantId);
                                cs.setString(9, tid);
                                cs.setString(10, transactionId);
                                cs.setString(11, orderReference);
                                cs.setString(12, merchantTransRef);
                                cs.setString(13, cardNumber);
                                cs.setString(14, approvalCode);
                                cs.setString(15, paymentChannel);
                                cs.setString(16, cardType);
                                cs.setString(17, transactionType);
                                cs.setString(18, transactionState);
                                cs.setString(19, installmentStatus);
                                cs.setInt(20, page);
                                cs.setInt(21, size);

                                cs.execute();

                                rs = (ResultSet) cs.getObject(1);
                                nTotal = (ResultSet) cs.getObject(2);

                                List<UposListResponse> list = new ArrayList<>();
                                while (rs.next()) {
                                        UposListResponse dto = new UposListResponse();
                                        dto.setMerchantId(rs.getString("S_MERCHANT_ID"));
                                        dto.setTid(rs.getString("S_TERMINAL_ID"));
                                        dto.setTransactionId(rs.getString("s_transaction_id"));
                                        dto.setDate(rs.getString("D_TRANSACTION"));
                                        dto.setMerchantTransRef(rs.getString("S_MERCHANT_TRANS_REF"));
                                        dto.setPaymentChannel(rs.getString("S_PAY_CHANNEL"));
                                        dto.setCardNumber(rs.getString("S_CARD_NUMBER"));
                                        dto.setAmount(rs.getString("N_AMOUNT"));
                                        dto.setCurrency(rs.getString("S_CURRENCY"));
                                        dto.setTransactionType(rs.getString("S_TRANS_TYPE"));
                                        dto.setResponseCode(rs.getString("S_RESPONSE_CODE"));
                                        dto.setTransactionState(rs.getString("S_TRANS_STATE"));
                                        dto.setInstallmentStatus(rs.getString("S_ITA_STATE"));
                                        dto.setSKey(rs.getString("S_KEY"));
                                        list.add(dto);
                                }

                                while (nTotal.next()) {
                                        total = nTotal.getLong("TOTAL_DATA");
                                }

                                PagedResponse<UposListResponse> pagedResponse = PagedResponse.<UposListResponse>builder()
                                        .currentPage(page)
                                        .pageSize(size)
                                        .totalPages(1)
                                        .items(list)
                                        .total(total)
                                        .build();

                                return Mono.just(pagedResponse);
                        }finally{
                                if (rs != null) try { rs.close(); } catch (SQLException ignored) {}
                                if (cs != null) try { cs.close(); } catch (SQLException ignored) {}
                        }
                       
                });    
                } catch (Exception e) {
                    logger.error("Error getTransactionUpos: ", e);
                    return Mono.just(PagedResponse.<UposListResponse>builder()
                                .currentPage(0)
                                .pageSize(0)
                                .totalPages(0)
                                .items(null)
                                .build());    
                }
               
        }

        @Override
        public Mono<ApiResponse<PagedResponse<UposListResponse>>> getUposTransactions(String fromDate, String toDate,
                        String merchantId, String tid, String transactionId, String orderReference,
                        String merchantTransRef, String cardNumber, String approvalCode, String paymentChannel,
                        String cardType, String transactionType, String transactionState, String installmentStatus,
                        int page, int size, String xUserId) {
                
                 try {
                        logger.info("---------------------start call api getUposTransactions-------------------------");

                        return merchantService.resolveMerchantIdFilter(merchantId)
                                .flatMap(merchantIds -> {
                                        String queryMerchantUpos = String.join(",", merchantIds);

                                        Mono<PagedResponse<UposListResponse>> dataMono = getTransactionUpos(fromDate, toDate, queryMerchantUpos, tid, transactionId, orderReference,
                                                 merchantTransRef, cardNumber, approvalCode, paymentChannel, cardType, transactionType, transactionState, installmentStatus,
                                                 page, size);

                                        return dataMono.map(data -> {
                                                if (data == null || data.getItems() == null || data.getItems().isEmpty()) {
                                                        return ApiResponse.<PagedResponse<UposListResponse>>builder()
                                                                .status("OK")
                                                                .total(0)
                                                                .data(null)
                                                                .build();
                                                }

                                                long total = data.getTotal();
                                                int totalPages = (int) Math.ceil((double) total / size);
                                                data.setTotalPages(totalPages);

                                                return ApiResponse.<PagedResponse<UposListResponse>>builder()
                                                        .status("OK")
                                                        .total(total)
                                                        .data(data)
                                                        .build();
                                        });
                                });
                } catch (Exception e) {
                        logger.error("Error getUposTransactions MA: ", e);
                        return Mono.just(ApiResponse.<PagedResponse<UposListResponse>>builder()
                                        .status("Fail")
                                        .total(0)
                                        .data(null)
                                        .build()); 
                }finally{
                        logger.info("---------------------end call api getUposTransactions-------------------------");
                }
        }

        private Map<String, String> parseVoidResponse(String responseBody) {
                try {
                        if (responseBody == null || responseBody.trim().isEmpty()) {
                                logger.warn("Response body is empty or null");
                                return Map.of();
                        }

                        ObjectMapper mapper = new ObjectMapper();
                        JsonNode rootNode = mapper.readTree(responseBody);
                        // Extract merchant_transaction_ref
                        String merchantTransactionRef = rootNode.path("merchant_transaction_ref").asText(null);
                        String newTransactionId = rootNode.path("transaction_id").asText(null);


                        // Build result map - always include both fields
                        Map<String, String> result = new HashMap<>();
                        result.put("merchant_transaction_ref", merchantTransactionRef);
                        result.put("new_transaction_id", newTransactionId);

                        logger.info("Parsed void response result: {}", result);
                        return result;

                } catch (Exception e) {
                        logger.error("Error parsing void response: {}", e.getMessage(), e);
                        return Map.of();
                }
        }

        private Map<String, String> parseCaptureResponse(String responseBody) {
                try {
                        if (responseBody == null || responseBody.trim().isEmpty()) {
                                logger.warn("Response body is empty or null");
                                return Map.of();
                        }

                        ObjectMapper mapper = new ObjectMapper();
                        JsonNode rootNode = mapper.readTree(responseBody);
                        JsonNode mapNode = rootNode.path("map");
                        // Extract transaction_id
                        Number status = mapNode.path("status").asInt();

                        // Extract data field
                        String merchantTransactionRef = mapNode.path("merchant_transaction_ref").asText(null);
                        String newTransactionId = mapNode.path("transaction_id").asText(null);

                        logger.info("Parsed capture response - status: {}, merchant_transaction_ref: {}, new_transaction_id: {}", status, merchantTransactionRef, newTransactionId);

                        // Build result map - always include both fields
                        Map<String, String> result = new HashMap<>();
                        result.put("status", status.toString());
                        result.put("merchant_transaction_ref", merchantTransactionRef);
                        result.put("new_transaction_id", newTransactionId);

                        logger.info("Parsed capture response result: {}", result);
                        return result;

                } catch (Exception e) {
                        logger.error("Error parsing capture response: {}", e.getMessage(), e);
                        return Map.of();
                }
        }

        /**
         * Parse refund response body to extract key fields
         * @param responseBody JSON response body
         * @return Map containing transaction_id and refund_contract_type
         */
        private Map<String, String> parseRefundResponse(String responseBody) {
                try {
                        if (responseBody == null || responseBody.trim().isEmpty()) {
                                logger.warn("Response body is empty or null");
                                return Map.of();
                        }

                        ObjectMapper mapper = new ObjectMapper();
                        JsonNode rootNode = mapper.readTree(responseBody);

                        // Extract transaction_id
                        String transactionId = rootNode.path("transaction_id").asText(null);

                        // Extract data field
                        String dataField = rootNode.path("data").asText(null);

                        logger.info("Parsed refund response - transaction_id: {}, data: {}", transactionId, dataField);

                        // Parse data field to get refund_contract_type
                        String refundContractType = parseRefundContractType(dataField);

                        logger.info("Extracted refund_contract_type: {}", refundContractType);

                        // Build result map - always include both fields
                        Map<String, String> result = new HashMap<>();
                        result.put("transaction_id", transactionId);
                        result.put("refund_contract_type", refundContractType);

                        logger.info("Parsed refund response result: {}", result);
                        return result;

                } catch (Exception e) {
                        logger.error("Error parsing refund response: {}", e.getMessage(), e);
                        return Map.of();
                }
        }

        /**
         * Parse data field to extract refund_contract_type
         * @param dataField JSON string from data field
         * @return refund_contract_type value or null if not found
         */
        private String parseRefundContractType(String dataField) {
                try {
                        if (dataField == null || dataField.trim().isEmpty()) {
                                logger.debug("Data field is empty or null");
                                return null;
                        }

                        ObjectMapper mapper = new ObjectMapper();
                        JsonNode dataNode = mapper.readTree(dataField);

                        // Navigate to msp.refund_contract_type
                        JsonNode mspNode = dataNode.path("msp");
                        if (mspNode.isMissingNode()) {
                                logger.debug("msp node not found in data field");
                                return null;
                        }

                        String refundContractType = mspNode.path("refund_contract_type").asText(null);

                        return refundContractType;

                } catch (Exception e) {
                        logger.error("Error parsing refund_contract_type from data field: {}", e.getMessage(), e);
                        return null;
                }
        }

        private String mapJsonForKey(String jsonData, String key) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(jsonData);
           return  jsonNode.get(key).asText();
        } catch (Exception e) {
            logger.warn("service not found: {}", jsonData);
            return null;
        }
    }

}
