package vn.onepay.transaction.service.impl;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import vn.onepay.transaction.exception.NoItemExistsException;
import reactor.core.publisher.Mono;
import vn.onepay.transaction.config.ExportFileProperties;
import vn.onepay.transaction.constant.IConstants;
import vn.onepay.transaction.dto.ApiResponse;
import vn.onepay.transaction.dto.FileDownloadDto;
import vn.onepay.transaction.dto.PagedResponse;
import vn.onepay.transaction.dto.RequestRefundDetail;
import vn.onepay.transaction.dto.RequestRefundResponse;
import vn.onepay.transaction.entity.DownloadTaskEntity;
import vn.onepay.transaction.opensearch.OpenSearchQueryBuilder;
import vn.onepay.transaction.repository.TransactionOpenSearchRepository;
import vn.onepay.transaction.service.MerchantService;
import vn.onepay.transaction.service.RequestRefundService;
import vn.onepay.transaction.dto.OrderInformation;
import vn.onepay.transaction.dto.MerchantInformation;
import vn.onepay.transaction.dto.RefundInfomation;
import vn.onepay.transaction.dto.PaymentMethod;

@Service
public class RequestRefundServiceImpl implements RequestRefundService {
    private static final Logger logger = LoggerFactory.getLogger(TransactionServiceImpl.class);
    @Autowired
    private TransactionOpenSearchRepository repository;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private ExportFileProperties exportFileProperties;

    @Override
    public Mono<ApiResponse<PagedResponse<RequestRefundResponse>>> getRefundRequests(
            String fromDate, String toDate,
            String merchantId, String status, String searchKeyword,
            int page, int size,
            String sortField, String sortOrder) {

        return merchantService.resolveMerchantIdFilter(merchantId)
                .flatMap(merchantIdFilter -> {
                    if (merchantIdFilter.isEmpty()) {
                        logger.warn("No merchantId matched -> returning empty response");
                        PagedResponse<RequestRefundResponse> emptyPagedResponse = PagedResponse
                                .<RequestRefundResponse>builder()
                                .currentPage(page)
                                .pageSize(size)
                                .totalPages(0)
                                .items(Collections.emptyList())
                                .build();
                        return Mono.just(ApiResponse.success(0, emptyPagedResponse));
                    }

                    Map<String, Object> conditions = new HashMap<>();
                    conditions.put("fromDate", fromDate);
                    conditions.put("toDate", toDate);
                    conditions.put("merchantId", String.join(",", merchantIdFilter));
                    conditions.put("searchKeyword", searchKeyword);
                    conditions.put("status", status);
                    conditions.put("page", page);
                    conditions.put("pageSize", size);
                    conditions.put("sortField", sortField);
                    conditions.put("sortOrder", sortOrder);
                    conditions.put("type", IConstants.TYPE_REQUEST_REFUND);
                    try {
                        logger.info("Start service getRefundRequests with conditions: {}", conditions);
                        String opsQuery = OpenSearchQueryBuilder.buildQuery(conditions);
                        String opsStrData = repository.getTransactionList(opsQuery);
                        ObjectMapper mapper = new ObjectMapper();
                        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                        JsonNode root = mapper.readTree(opsStrData);
                        JsonNode hits = root.path("hits").path("hits");
                        int totalHits = root.path("hits").path("total").path("value").asInt(0);
                        int totalPage = (int) Math.ceil((double) totalHits / size);
                        logger.info("Received response from OpenSearch - totalHits: {}", totalHits);

                        List<RequestRefundResponse> transList = new ArrayList<>();
                        for (JsonNode hit : hits) {
                            RequestRefundResponse requestRefundResponse = mapJsonToRequestRefundResponse(hit);
                            if (requestRefundResponse != null)
                                transList.add(requestRefundResponse);
                        }
                        logger.info("Completed mapping {} request refund", transList.size());
                        logger.info("End service getRefundRequests - totalHits: {}, size: {}", totalHits,
                                transList.size());
                        PagedResponse<RequestRefundResponse> pagedResponse = PagedResponse
                                .<RequestRefundResponse>builder()
                                .currentPage(page)
                                .pageSize(size)
                                .totalPages(totalPage)
                                .items(transList)
                                .build();

                        return Mono.just(ApiResponse.success(totalHits, pagedResponse));
                    } catch (Exception e) {
                        logger.error("Error getRefundRequests: ", e);
                        return Mono.just(ApiResponse.error(""));
                    }
                });
    }

    private RequestRefundResponse mapJsonToRequestRefundResponse(JsonNode hit) {
        try {
            JsonNode source = hit.path("_source");
            JsonNode enrich_txn = source.path("enrich_txn");
            JsonNode msp_refund_request = source.path("msp_refund_request");

            String docId = safeText(hit, "_id");
            if (docId == null || docId.isEmpty())
                return null;
            RequestRefundResponse requestRefundResponse = RequestRefundResponse.builder()
                    .docId(docId)
                    .merchantId(safeText(source, "msp_merchant", "s_id"))
                    .requestId(safeText(enrich_txn, "s_id"))
                    .requestStatus(safeText(enrich_txn, "s_state"))
                    .currency(safeText(source, "msp_invoice", "s_currencies"))
                    .parentTransactionId(safeText(msp_refund_request, "s_parent_id"))
                    .createdBy(safeText(enrich_txn, "s_operator_id"))
                    .build();

            // amount
            String amountStr = safeText(enrich_txn, "n_amount");
            if (amountStr != null) {
                requestRefundResponse.setAmount(new BigDecimal(amountStr));
            }
            // datetime
            requestRefundResponse.setCreatedDate(convertDate(safeText(enrich_txn, "d_create")));
            return requestRefundResponse;
        } catch (Exception e) {
            logger.warn("Error mapping RequestRefundResponse: ", e);
            return null;
        }
    }

    private String safeText(JsonNode node, String... path) {
        for (String p : path) {
            if (node == null)
                return null;
            node = node.path(p);
        }
        return node.isMissingNode() ? null : node.asText(null);
    }

    private static String convertDate(String datetimeStr) {
        try {
            return LocalDateTime.parse(datetimeStr, DateTimeFormatter.ISO_DATE_TIME).toString();
        } catch (DateTimeParseException e) {
            logger.warn("Invalid datetime format: {}", datetimeStr);
            return null;
        }

    }

    @Override
    public RequestRefundDetail getRefundRequestDetail(String docId) {
        logger.info("Start service getRefundRequestDetail with docId: {}", docId);
        try {
            String opsStrData = repository.getTransactionDetailByDocId(docId);

            ObjectMapper mapper = new ObjectMapper();
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            JsonNode root = mapper.readTree(opsStrData);

            String isFound = safeText(root, "found");
            if (isFound.equals("false"))
                throw new NoItemExistsException("Transaction with docId " + docId + " not found");

            RequestRefundDetail response = mapJsonToRequestRefundDetail(root.path("_source"));
            return response;
        } catch (NoItemExistsException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error getRefundRequestDetail", e);
        }

        return null;
    }

    private RequestRefundDetail mapJsonToRequestRefundDetail(JsonNode source) {
        try {
            logger.info("Start map res json to RequestRefundDetail...");
            JsonNode enrich = source.path("enrich_txn");
            JsonNode payment = source.path("msp_payment");
            JsonNode invoice = source.path("msp_invoice");
            JsonNode merchant = source.path("msp_merchant");
            JsonNode msp_refund_request = source.path("msp_refund_request");
            JsonNode oc_transaction = source.path("oc_transaction");
            OrderInformation orderInformation = OrderInformation.builder()
                    .orderCreatedTime(convertDate(safeText(invoice, "d_create")))
                    .orderReference(safeText(invoice, "s_info"))
                    .orderCurrency(safeText(invoice, "s_currencies"))
                    .build();
            String amountStr = safeText(source, "msp_invoice", "n_amount");
            if (amountStr != null) {
                orderInformation.setOrderAmount(new BigDecimal(amountStr));
            }
            MerchantInformation merchantInformation = MerchantInformation.builder()
                    .merchantId(safeText(merchant, "s_id"))
                    .merchantName(safeText(merchant, "s_name"))
                    .build();

            RefundInfomation refundInfomation = RefundInfomation.builder()
                    .refundRequestId(safeText(msp_refund_request, "n_id"))
                    .requestStatus(safeText(enrich, "s_state"))
                    .parentTransactionId(safeText(msp_refund_request, "s_parent_id"))
                    .createdDate(convertDate(safeText(msp_refund_request, "d_create")))
                    .transactionType("Request Refund")
                    .currency(safeText(msp_refund_request, "s_currency"))
                    .paymentMethod(safeText(payment, "s_e_pay_method"))
                    .createdBy(safeText(enrich, "s_operator_id"))
                    .responseCode(safeText(enrich, "s_response_code"))
                    .build();

            String refundAmount = safeText(msp_refund_request, "n_amount");
            if (refundAmount != null) {
                refundInfomation.setRefundAmount(new BigDecimal(refundAmount));
            }
            PaymentMethod paymentMethod2 = PaymentMethod.builder()
                    .cardNumber(safeText(payment, "s_e_card_number"))
                    .cardBrand(safeText(payment, "s_e_card"))
                    .cardType(safeText(payment, "s_data", "instrument", "s_type"))
                    .issuer(safeText(payment, "s_data", "instrument", "s_issuer"))
                    .authorizationCode(safeText(payment, "s_data", "instrument", "authorisation_code"))
                    .cscResultCode(safeText(payment, "s_data", "csc_result_code"))
                    .cardExpiry(safeText(payment, "s_data", "card_exp"))
                    .commercialCard(safeText(payment, "s_data", "commercial_card"))
                    .dialectCscResultCode(safeText(oc_transaction, "s_e_cscresult_code"))
                    .commercialCardIndicator(safeText(payment, "s_data", "commercial_card_indicator"))
                    .nameOnCard(safeText(payment, "s_ins_name"))
                    .build();

            return RequestRefundDetail.builder()
                    .orderInformation(orderInformation)
                    .merchantInformation(merchantInformation)
                    .refundInfomation(refundInfomation)
                    .paymentMethod(paymentMethod2)
                    .build();
        } catch (Exception e) {
            logger.warn("Error mapping RequestRefundDetail: ", e);
            return null;
        }
    }

    @Override
    public FileDownloadDto createExcel(DownloadTaskEntity task) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        try {
            logger.info("Start exporting Excel for taskId={}, fileName={}", task.getId(),
                    task.getFileName());

            // Tạo thư mục lưu file nếu chưa có
            String baseDir = exportFileProperties.getBaseDir();
            File dir = new File(baseDir);
            if (!dir.exists())
                dir.mkdirs();

            String outputPath = baseDir + task.getFileHashName();

            // Parse requestParams từ task
            Map<String, Object> params = objectMapper.readValue(task.getRequestParams(),
                    new TypeReference<>() {
                    });
            String fromDate = (String) params.get("fromDate");
            String toDate = (String) params.get("toDate");
            String merchantId = (String) params.get("merchantId");
            String status = (String) params.get("status");
            String searchKeyword = (String) params.get("searchKeyword");
            int page = 0;
            int size = 10000;
            List<RequestRefundResponse> allTransactions = new ArrayList<>();
            if (merchantId != null && !merchantId.isEmpty()) {
                while (true) {
                    Map<String, Object> conditions = new HashMap<>();
                    conditions.put("fromDate", fromDate);
                    conditions.put("toDate", toDate);
                    conditions.put("merchantId", merchantId);
                    conditions.put("searchKeyword", searchKeyword);
                    conditions.put("status", status);
                    conditions.put("page", page);
                    conditions.put("pageSize", size);
                    conditions.put("sortOrder", "desc");
                    conditions.put("type", IConstants.TYPE_REQUEST_REFUND);
                    

                    String opsQuery = OpenSearchQueryBuilder.buildQuery(conditions);
                    String opsStrData = repository.getTransactionList(opsQuery);

                    JsonNode root = objectMapper.readTree(opsStrData);
                    JsonNode hits = root.path("hits").path("hits");

                    if (!hits.isArray() || hits.size() == 0)
                        break;

                    for (JsonNode hit : hits) {
                        RequestRefundResponse dto = mapJsonToRequestRefundResponse(hit);
                        if (dto != null) {
                            allTransactions.add(dto);
                        }
                    }

                    if (hits.size() < size)
                        break;
                    page++;
                }
                logger.info("Fetched total {} transactions", allTransactions.size());

            }
            // Đọc file template từ resources/template/
            try (
                    InputStream is = getClass().getClassLoader()
                            .getResourceAsStream("template/RequestRefundTemplate.xlsx");
                    Workbook workbook = new XSSFWorkbook(is);
                    FileOutputStream fos = new FileOutputStream(outputPath)) {
                if (is == null)
                    throw new FileNotFoundException("Template not found in resources/template/");

                Sheet sheet = workbook.getSheetAt(0);
                int startRow = 4;

                ZonedDateTime zFrom = Instant.parse(fromDate).atZone(ZoneId.systemDefault());
                ZonedDateTime zTo = Instant.parse(toDate).atZone(ZoneId.systemDefault());
                // Định dạng theo "yyyy-MM-dd HH:mm:ss"
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

                String fromDateTime = zFrom.format(formatter);
                String toDateTime = zTo.format(formatter);
                Row rowDate = sheet.createRow(1);
                rowDate.createCell(2).setCellValue("From: " + fromDateTime);
                rowDate.createCell(4).setCellValue("To: " + toDateTime);

                logger.info("Writing {} rows to Excel file", allTransactions.size());

                for (int i = 0; i < allTransactions.size(); i++) {
                    RequestRefundResponse dto = allTransactions.get(i);
                    Row row = sheet.createRow(startRow + i);
                    row.createCell(0).setCellValue(i + 1);
                    row.createCell(1).setCellValue(dto.getMerchantId());
                    row.createCell(2).setCellValue(dto.getRequestId());
                    row.createCell(3).setCellValue(dto.getParentTransactionId());
                    row.createCell(4).setCellValue(dto.getAmount() != null ? dto.getAmount().toString(): "");
                    row.createCell(5).setCellValue(dto.getCurrency());
                    row.createCell(6).setCellValue(dto.getCreatedDate().replace("T", " "));
                    row.createCell(7).setCellValue(dto.getCreatedBy());
                    row.createCell(8).setCellValue(dto.getRequestStatus());
                }

                workbook.write(fos);
            }
            // Tạo FileDownloadDto trả về
            File file = new File(outputPath);
            FileDownloadDto dto = new FileDownloadDto();
            dto.setFile_hash_name(task.getFileHashName());
            dto.setFile_name(task.getFileName());
            dto.setFile_path(outputPath);
            dto.setFile_size(file.length());
            dto.setUser(task.getUserId());
            logger.info("Exported {} transactions to {}", allTransactions.size(), outputPath);
            return dto;

        } catch (Exception e) {
            logger.error("Error while exporting Excel for task {}: {}", task.getId(), e.getMessage(), e);
            throw new RuntimeException("Export Excel failed", e);
        }
    }
}
