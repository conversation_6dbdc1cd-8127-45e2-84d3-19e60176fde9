package vn.onepay.transaction.service.impl;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.r2dbc.core.DatabaseClient;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;
import vn.onepay.transaction.constant.IConstants;
import vn.onepay.transaction.entity.DownloadTaskEntity;
import vn.onepay.transaction.service.DownloadTaskService;

@Service
@RequiredArgsConstructor
public class DownloadTaskServiceImpl implements DownloadTaskService {

    private final DatabaseClient databaseClient;
    private final ObjectMapper objectMapper;
    private final KafkaTemplate<String, UUID> kafkaTemplate;

    private static final Logger logger = LoggerFactory.getLogger(DownloadTaskServiceImpl.class);

    @Override
    public Mono<DownloadTaskEntity> createTask(String taskType, Map<String, Object> params) {
        return Mono.fromCallable(() -> {
                    UUID taskId = UUID.randomUUID();
                    String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
                    String fileName = taskType + "_" + timestamp + ".xlsx";
                    String fileHashName = taskId.toString();
                    String paramJson = objectMapper.writeValueAsString(params);

                    DownloadTaskEntity entity = DownloadTaskEntity.builder()
                            .id(taskId)
                            .taskType(taskType)
                            .status(IConstants.STATUS_PENDING)
                            .fileName(fileName)
                            .fileHashName(fileHashName)
                            .requestParams(paramJson)
                            .createdAt(LocalDateTime.now())
                            .build();

                    return entity;
                })
                .flatMap(task -> {
                    String query = """
                        INSERT INTO download_task (id, task_type, status, file_name, file_hash_name, request_params, created_at)
                        VALUES (:id, :taskType, :status, :fileName, :fileHashName, :requestParams, :createdAt)
                    """;
                    return databaseClient.sql(query)
                            .bind("id", task.getId())
                            .bind("taskType", task.getTaskType())
                            .bind("status", task.getStatus())
                            .bind("fileName", task.getFileName())
                            .bind("fileHashName", task.getFileHashName())
                            .bind("requestParams", task.getRequestParams())
                            .bind("createdAt", task.getCreatedAt())
                            .fetch()
                            .rowsUpdated()
                            .flatMap(rows -> {
                                if (rows > 0) {
                                    logger.info("Successfully created task: {}", task.getId());
                                    // Gửi Kafka tại đây sau khi DB insert thành công
                                    kafkaTemplate.send(IConstants.DOWNLOAD_TASK_TOPIC, task.getId());
                                    return Mono.just(task);
                                } else {
                                    return Mono.error(new RuntimeException("Failed to insert task into database"));
                                }
                            });
                })
                .doOnError(error -> logger.error("Error creating task: {}", error.getMessage(), error));
    }
}
