package vn.onepay.transaction.service.impl;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.r2dbc.core.DatabaseClient;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ServerWebExchange;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import vn.onepay.transaction.constant.IConstants;
import vn.onepay.transaction.dto.FileDownloadDto;
import vn.onepay.transaction.entity.DownloadTaskEntity;
import vn.onepay.transaction.repository.FileDownloadRepository;
import vn.onepay.transaction.service.DownloadTaskService;
import vn.onepay.transaction.service.MerchantService;

@Service
@RequiredArgsConstructor
public class DownloadTaskServiceImpl implements DownloadTaskService {

    private final ObjectMapper objectMapper;
    private final KafkaTemplate<String, UUID> kafkaTemplate;

    private static final Logger logger = LoggerFactory.getLogger(DownloadTaskServiceImpl.class);
    private final DatabaseClient postgresClient;
    private final MerchantService merchantService;
    private final FileDownloadRepository fileDownloadRepository;

    @Override
    public Mono<DownloadTaskEntity> createTask(ServerWebExchange exchange,String taskType, Map<String, Object> params) {
        String merchantId = (String) params.get("merchantId");
        String userId = exchange.getRequest().getHeaders().get("X-User-Id").get(0);
        return merchantService.resolveMerchantIdFilter(merchantId)
                .flatMap(merchantIdFilter -> {
                    params.put("merchantId", String.join(",", merchantIdFilter));
                    return Mono.fromCallable(() -> buildDownloadTask(taskType, params,userId))
                            .flatMap(task -> insertPostgres(task)
                                    .then(insertOracle(task))
                                    .then(sendTaskToKafka(task.getId()))
                                    .thenReturn(task));
                })
                .doOnError(error -> logger.error("Error creating task: {}", error.getMessage(), error));
    }

    private DownloadTaskEntity buildDownloadTask(String taskType, Map<String, Object> params,String userId)
            throws JsonProcessingException {
        UUID taskId = UUID.randomUUID();
        String taskIdStr = taskId.toString().replace("-", "");
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String fileName = timestamp + "_" + taskType;
        String fileHashName = taskIdStr;
        String paramJson = objectMapper.writeValueAsString(params);

        return DownloadTaskEntity.builder()
                .id(taskId)
                .taskType(taskType)
                .status(IConstants.STATUS_PENDING)
                .fileName(fileName)
                .fileHashName(fileHashName)
                .requestParams(paramJson)
                .createdAt(LocalDateTime.now())
                .userId(userId)
                .build();
    }

    private Mono<Void> insertPostgres(DownloadTaskEntity task) {
        String query = """
                    INSERT INTO download_task (id, task_type, status, file_name, file_hash_name, request_params, created_at,user_id)
                    VALUES (:id, :taskType, :status, :fileName, :fileHashName, :requestParams, :createdAt,:userId)
                """;

        return postgresClient.sql(query)
                .bind("id", task.getId())
                .bind("taskType", task.getTaskType())
                .bind("status", task.getStatus())
                .bind("fileName", task.getFileName())
                .bind("fileHashName", task.getFileHashName())
                .bind("requestParams", task.getRequestParams())
                .bind("createdAt", task.getCreatedAt())
                .bind("userId", task.getUserId())
                .fetch()
                .rowsUpdated()
                .flatMap(rows -> {
                    if (rows > 0) {
                        logger.info("Successfully inserted task {}", task.getId());
                        return Mono.empty();
                    } else {
                        return Mono.error(new RuntimeException("Failed to insert task into PostgreSQL"));
                    }
                });
    }

    public Mono<Void> sendTaskToKafka(UUID taskId) {
        return Mono.fromRunnable(() -> kafkaTemplate.send(IConstants.DOWNLOAD_TASK_TOPIC, taskId))
                .subscribeOn(Schedulers.boundedElastic())
                .then();
    }

    public Mono<Void> insertOracle(DownloadTaskEntity task) {
        return Mono.fromCallable(() -> {
            LocalDateTime expired = LocalDateTime.now().plusHours(24);
            Timestamp expiredTimestamp = Timestamp.valueOf(expired);
            FileDownloadDto dto = new FileDownloadDto();
            dto.setFile_hash_name(task.getFileHashName());
            dto.setUser("657"); // chưa có user, set defaut admin
            dto.setFile_name(task.getFileName());
            dto.setExt("csv");
            dto.setFile_type(task.getTaskType());
            dto.setStatus("created");
            dto.setExpired_date(expiredTimestamp);
            fileDownloadRepository.insert(dto);
            return true;
        }).subscribeOn(Schedulers.boundedElastic()).then();
    }
}
