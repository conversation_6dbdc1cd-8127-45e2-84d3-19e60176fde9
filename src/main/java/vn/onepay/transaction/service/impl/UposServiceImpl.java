package vn.onepay.transaction.service.impl;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.r2dbc.core.DatabaseClient;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import vn.onepay.transaction.config.ExportFileProperties;
import vn.onepay.transaction.constant.IConstants;
import vn.onepay.transaction.dto.ApiResponse;
import vn.onepay.transaction.dto.FileDownloadDto;
import vn.onepay.transaction.dto.PagedResponse;
import vn.onepay.transaction.dto.ReportResponse;
import vn.onepay.transaction.dto.UposDetailResponse;
import vn.onepay.transaction.dto.UposListResponse;
import vn.onepay.transaction.dto.UposReportResponse;
import vn.onepay.transaction.entity.DownloadTaskEntity;
import vn.onepay.transaction.entity.UposReportEntity;
import vn.onepay.transaction.exception.ForbiddenException;
import vn.onepay.transaction.exception.NoItemExistsException;
import vn.onepay.transaction.opensearch.OpenSearchQueryBuilder;
import vn.onepay.transaction.repository.TransactionOpenSearchRepository;
import vn.onepay.transaction.service.MerchantService;
import vn.onepay.transaction.service.UposService;
import vn.onepay.transaction.util.DateValidatorUtil;

@Service
@RequiredArgsConstructor
public class UposServiceImpl implements UposService {
    private static final Logger logger = LoggerFactory.getLogger(UposServiceImpl.class);
    private static final ObjectMapper objectMapper = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    private final MerchantService merchantService;
    private final TransactionOpenSearchRepository repository;
    @Autowired
    private DatabaseClient databaseClient;
    @Autowired
    private ExportFileProperties exportFileProperties;
    @Override
    public Mono<ApiResponse<PagedResponse<UposListResponse>>> getUposTransactions(String fromDate, String toDate,
            String merchantId, String tid, String transactionId, String orderReference, String merchantTransRef,
            String cardNumber, String approvalCode, String paymentChannel, String cardType, String transactionType,
            String transactionState, String installmentStatus, int page, int size) {
        return merchantService.resolveMerchantIdFilter(merchantId)
                .flatMap(merchantIdFilter -> {
                    if (merchantIdFilter.isEmpty()) {
                        logger.warn("No merchantId matched -> returning empty response");
                        PagedResponse<UposListResponse> emptyPagedResponse = PagedResponse
                                .<UposListResponse>builder()
                                .currentPage(page)
                                .pageSize(size)
                                .totalPages(0)
                                .total(0L)
                                .items(Collections.emptyList())
                                .build();
                        return Mono.just(ApiResponse.success(0, emptyPagedResponse));
                    }
                    Map<String, Object> conditions = new HashMap<>();
                    conditions.put("fromDate", fromDate);
                    conditions.put("toDate", toDate);
                    conditions.put("merchantId", String.join(",", merchantIdFilter));
                    conditions.put("tid", tid);
                    conditions.put("transId", transactionId);
                    conditions.put("page", page);
                    conditions.put("pageSize", size);
                    conditions.put("orderReference", orderReference);
                    conditions.put("merchantTransRef", merchantTransRef);
                    conditions.put("cardNumber", cardNumber);
                    conditions.put("cardType", cardType);
                    conditions.put("approvalCode", approvalCode);
                    conditions.put("paymentChannel", paymentChannel);
                    conditions.put("transactionType", transactionType);
                    conditions.put("transactionStatus", transactionState);
                    conditions.put("installmentStatus", installmentStatus);
                    conditions.put("sortOrder", "desc");
                    conditions.put("type", IConstants.TYPE_UPOS);
                    conditions.put("merchantChannel", "UPOS"); // điều kiện là giao dịch upos
                    logger.info("Start service getUposTransactions with conditions: {}", conditions);
                    return Mono.fromCallable(() -> {
                        String opsQuery = OpenSearchQueryBuilder.buildQuery(conditions);
                        String opsStrData = repository.getTransactionList(opsQuery);
                        JsonNode root = objectMapper.readTree(opsStrData);

                        JsonNode hits = root.path("hits").path("hits");
                        int totalHits = root.path("hits").path("total").path("value").asInt(0);
                        int totalPage = (int) Math.ceil((double) totalHits / size);

                        List<UposListResponse> transList = StreamSupport
                                .stream(hits.spliterator(), false)
                                .map(this::mapJsonToUposTransactionDTO)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());

                        logger.info("Mapped {} transactions", transList.size());

                        PagedResponse<UposListResponse> pagedResponse = PagedResponse
                                .<UposListResponse>builder()
                                .currentPage(page)
                                .pageSize(size)
                                .totalPages(totalPage)
                                .total((long) totalHits)
                                .items(transList)
                                .build();

                        return ApiResponse.success(totalHits, pagedResponse);
                    }).subscribeOn(Schedulers.boundedElastic())
                            .onErrorResume(e -> {
                                logger.error("Error getUposTransactions: ", e);
                                return Mono.just(ApiResponse.error("Error getUposTransactions"));
                            });
                });
    }

    private UposListResponse mapJsonToUposTransactionDTO(JsonNode hit) {
        try {
            JsonNode source = hit.path("_source");
            if (source.isMissingNode())
                return null;

            JsonNode merchantNode = source.path("msp_merchant");
            JsonNode enrichTxnNode = source.path("enrich_txn");
            JsonNode paymentNode = source.path("msp_payment");
            JsonNode invoiceNode = source.path("msp_invoice");

            String docId = safeText(hit, "_id");
            if (docId == null || docId.isEmpty())
                return null;

            UposListResponse dto = new UposListResponse();
            dto.setDocId(docId);
            dto.setBaseId(safeText(enrichTxnNode, "s_base_id"));
            dto.setMerchantId(safeText(merchantNode, "s_id"));
            dto.setTid(safeText(paymentNode, "s_data", "tid"));
            dto.setTransactionId(safeText(enrichTxnNode, "s_id"));
            dto.setOrderReference(safeText(invoiceNode, "s_info"));
            dto.setMerchantTransRef(safeText(enrichTxnNode, "s_txn_ref"));
            String paymentChannel = safeText(paymentNode, "s_e_pay_method");
            String itaId = safeText(paymentNode, "s_ita_id");
            if(itaId != null && !itaId.isEmpty()){
                paymentChannel = IConstants.PAYMENT_METHOD_INSTALLMENT;
            }
            dto.setPaymentChannel(paymentChannel);
            dto.setCardNumber(safeText(paymentNode, "s_e_card_number"));
            dto.setDate(DateValidatorUtil.convertDate(safeText(enrichTxnNode, "d_create")));
            dto.setTransactionType(safeText(enrichTxnNode, "s_txn_type"));
            dto.setCurrency(safeText(invoiceNode, "s_currencies"));
            dto.setResponseCode(safeText(enrichTxnNode, "s_e_response_code"));
            dto.setTransactionState(safeText(enrichTxnNode, "s_state"));
            dto.setInstallmentStatus(safeText(paymentNode, "s_e_ita_state"));
            // amount
            String amountStr = safeText(enrichTxnNode, "n_amount");
            if (amountStr != null) {
                dto.setAmount(amountStr);
            }

            return dto;
        } catch (Exception e) {
            logger.warn("Error mapping transaction DTO: ", e);
            return null;
        }
    }

    private String safeText(JsonNode node, String... path) {
        for (String p : path) {
            if (node == null)
                return null;
            node = node.path(p);
        }
        return node.isMissingNode() ? null : node.asText(null);
    }

    @Override
    public Mono<UposDetailResponse> getUposDetail(String docId) {
        logger.info("Start service getTransactionDetail with docId: {}", docId);

        return merchantService.resolveMerchantIdFilter(null)
                .flatMap(merchantIdFilter -> Mono.fromCallable(() -> {
                    String opsStrData = repository.getTransactionDetailByDocId(docId);

                    ObjectMapper mapper = new ObjectMapper();
                    mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                    JsonNode root = mapper.readTree(opsStrData);

                    String isFound = safeText(root, "found");
                    if ("false".equals(isFound)) {
                        throw new NoItemExistsException(
                                "Transaction with docId " + docId + " not found");
                    }

                    JsonNode source = root.path("_source");
                    String merchantIdInDoc = safeText(source, "msp_merchant", "s_id");

                    // Kiểm tra merchantId trong document có nằm trong danh sách filter không
                    if (!merchantIdFilter.contains(merchantIdInDoc)) {
                        logger.warn("merchantId {} in docId {} not allowed by filter -> return forbidden",
                                merchantIdInDoc, docId);
                        throw new ForbiddenException("Access denied");
                    }

                    return mapJsonToUposDetailResponse(source);
                }).subscribeOn(Schedulers.boundedElastic()));
    }

    private UposDetailResponse mapJsonToUposDetailResponse(JsonNode source) {
        try {
            logger.info("Start map res json to UposDetailResponse...");
            UposDetailResponse dto = new UposDetailResponse();

            JsonNode merchantNode = source.path("msp_merchant");
            JsonNode enrichTxnNode = source.path("enrich_txn");
            JsonNode paymentNode = source.path("msp_payment");
            JsonNode invoiceNode = source.path("msp_invoice");
            JsonNode oc_transaction = source.path("oc_transaction");

            dto.setMerchantId(safeText(merchantNode, "s_id"));
            dto.setTid(safeText(paymentNode, "s_data", "tid"));
            dto.setTransactionId(safeText(enrichTxnNode, "s_id"));
            dto.setOrderReference(safeText(invoiceNode, "s_info"));
            dto.setMerchantTransRef(safeText(enrichTxnNode, "s_txn_ref"));
            dto.setDate(DateValidatorUtil.convertDate(safeText(enrichTxnNode, "d_create")));
            dto.setTransactionType(safeText(enrichTxnNode, "s_txn_type"));
            dto.setCurrency(safeText(invoiceNode, "s_currencies"));
            dto.setResponseCode(safeText(enrichTxnNode, "s_e_response_code"));
            dto.setTransactionState(safeText(enrichTxnNode, "s_state"));
            String amountPaymentStr = safeText(paymentNode, "n_amount");
            dto.setPurchaseAmount(amountPaymentStr != null ? new BigDecimal(amountPaymentStr) : null);

            // paymentChannel     
            String paymentChannel = safeText(paymentNode, "s_e_pay_method");
            String itaId = safeText(paymentNode, "s_ita_id");
            if(itaId != null && !itaId.isEmpty()){
                paymentChannel = IConstants.PAYMENT_METHOD_INSTALLMENT;
            }
            dto.setPaymentChannel(paymentChannel);
            // Only Installment & card
            dto.setApprovalCode(safeText(paymentNode, "s_data", "authorize_id"));
            dto.setCardNumber(safeText(paymentNode, "s_e_card_number"));
            dto.setCardType(safeText(paymentNode, "s_ins_brand_id"));
            dto.setIssuer(safeText(paymentNode, "s_e_issuer"));
            dto.setCardExpiry(safeText(source, "oc_transaction", "s_card_exp"));
            dto.setNameOnCard(safeText(paymentNode, "s_ins_name"));
            dto.setIpAddress(safeText(oc_transaction, "s_ticket_number"));
            dto.setIpProxy(safeText(oc_transaction, "s_ip_proxy"));
            dto.setIpCountry(safeText(oc_transaction, "s_ip_country"));
            dto.setBinCountry(safeText(source, "of_bin_base", "s_ip_country"));
            dto.setRiskAssessment(safeText(source, "fraud_scan", "n_fraud_status_for_mer_alert"));

            // Only Installment
            String periodStr = safeText(paymentNode, "s_ita_time");
            String itaFee = safeText(paymentNode, "n_ita_fee_amount");

            dto.setInstallmentPeriod(periodStr);
            dto.setInstallmentStatus(safeText(paymentNode, "s_e_ita_state"));
            dto.setInstallmentFee(itaFee != null ? new BigDecimal(itaFee) : null);
            dto.setBank(safeText(source, "msp_ita", "s_name"));

            if (periodStr != null && !periodStr.isEmpty() && amountPaymentStr != null) { // tính số tiền phải trả hàng
                                                                                         // tháng
                BigDecimal amount = new BigDecimal(amountPaymentStr);
                BigDecimal period = new BigDecimal(periodStr);
                BigDecimal monthlyAmount = amount.divide(period, 2, RoundingMode.HALF_UP);
                dto.setMonthlyInstallmentAmount(monthlyAmount);
            }

            // Only QR
            dto.setAppName(safeText(paymentNode, "s_e_card"));
            dto.setAccountNumber(safeText(paymentNode, "s_e_card_number"));
            dto.setAccountName(safeText(paymentNode, "s_ins_name"));
            logger.info("Map UposDetailResponse success!!");
            return dto;
        } catch (Exception e) {
            logger.warn("Error mapping upos detail: ", e);
            return null;
        }
    }

    @Override
    public Mono<ApiResponse<ReportResponse<UposReportResponse>>> getUposReport(String from, String to,
            String merchantId, String interval, String currency, String tid, int page, int size,String paymentChannel) {
        // TODO Auto-generated method stub
        String fromDate = normalizeDate(from);
        String toDate = normalizeDate(to);
        String groupBy = switch (interval.toUpperCase()) {
            case "DAILY" -> "d_transaction_day";
            case "WEEKLY" -> "TO_CHAR(d_transaction_day, 'IYYY-\"W\"IW')";
            case "MONTHLY" -> "TO_CHAR(d_transaction_day, 'YYYY-MM')";
            default -> throw new IllegalArgumentException("Invalid interval: " + interval);
        };
        return merchantService.resolveMerchantIdFilter(merchantId)
                .flatMap(merchantIds -> {
                    if (merchantIds.isEmpty()) {
                        logger.error("No merchantId matched -> returning empty response");
                        return Mono.just(ApiResponse.success(0, null));
                    }
                    StringBuilder sqlBuilder = buildQuerySelectReport(tid,paymentChannel, groupBy);
                    sqlBuilder.append("   LIMIT :limit OFFSET :offset"); // paging
                    DatabaseClient.GenericExecuteSpec spec = databaseClient.sql(sqlBuilder.toString())
                            .bind("fromDate", LocalDate.parse(fromDate))
                            .bind("toDate", LocalDate.parse(toDate))
                            .bind("currency", currency)
                            .bind("merchantIds", merchantIds)
                            .bind("limit", size)
                            .bind("offset", page * size);
                    if (tid != null && !tid.isBlank()) {
                        spec = spec.bind("tid", tid);
                    }
                    if (paymentChannel != null && !paymentChannel.isBlank()) {
                        spec = spec.bind("paymentChannel", paymentChannel);
                    }
                    Mono<List<UposReportResponse>> resultList = spec
                            .map((row, meta) -> {
                                return new UposReportResponse(
                                        row.get("date", String.class),
                                        row.get("merchant_id", String.class),
                                        row.get("tid", String.class),
                                        row.get("paymentChannel", String.class),
                                        row.get("cardType", String.class),
                                        row.get("currency", String.class),
                                        row.get("num_purchase_trans", Long.class),
                                        row.get("num_refund_void_trans", Long.class),
                                        row.get("total_purchase_amount", BigDecimal.class),
                                        row.get("total_refund", BigDecimal.class));
                            })
                            .all()
                            .collectList();

                    Mono<Long> countMono = buildReportCountQuery(
                            fromDate, toDate, merchantIds, tid, currency, groupBy,paymentChannel);
                    return Mono.zip(resultList, countMono)
                            .map(tuple -> {
                                List<UposReportResponse> items = tuple.getT1();
                                long total = tuple.getT2();
                                int totalPages = (int) Math.ceil((double) total / size);

                                // Tính tổng các trường
                                long totalPurchase = 0;
                                long totalRefundVoid = 0;
                                BigDecimal totalOriginalPurchase = BigDecimal.ZERO;
                                BigDecimal totalRefund = BigDecimal.ZERO;
                                for (UposReportResponse dto : items) {
                                    totalPurchase += Optional.ofNullable(dto.getPurchaseCount()).orElse(0L);
                                    totalRefundVoid += Optional.ofNullable(dto.getRefundVoidCount()).orElse(0L);
                                    totalOriginalPurchase = totalOriginalPurchase
                                            .add(Optional.ofNullable(dto.getTotalPurchase())
                                                    .orElse(BigDecimal.ZERO));
                                    totalRefund = totalRefund
                                            .add(Optional.ofNullable(dto.getTotalRefundVoid()).orElse(BigDecimal.ZERO));
                                }
                                // Tạo bản ghi tổng

                                String summaryDate = fromDate + " - " + toDate;
                                UposReportResponse totalDto = new UposReportResponse(summaryDate,
                                        null, null, null, null, currency, totalPurchase, totalRefundVoid,
                                        totalOriginalPurchase, totalRefund);

                                PagedResponse<UposReportResponse> pagedResponse = PagedResponse
                                        .<UposReportResponse>builder()
                                        .currentPage(page)
                                        .pageSize(size)
                                        .totalPages(totalPages)
                                        .total(total)
                                        .items(items)
                                        .build();

                                return ApiResponse.success(total, new ReportResponse<UposReportResponse>(pagedResponse, totalDto));
                            })
                            .onErrorResume(e -> {
                                logger.error("Error getUposReports: ", e);
                                return Mono.just(ApiResponse.error("Error getUposReports"));
                            });
                });
    }

    private Mono<Long> buildReportCountQuery(
            String fromDate,
            String toDate,
            List<String> merchantIds,
            String tid,
            String currency,
            String groupBy,
            String paymentChannel) {

        StringBuilder sqlBuilder = new StringBuilder("""
                    SELECT COUNT(*) AS cnt FROM (
                        SELECT 1 FROM upos_report
                        WHERE d_transaction_day BETWEEN :fromDate AND :toDate
                """);

        if (!merchantIds.isEmpty()) {
            sqlBuilder.append(" AND merchant_id IN (");
            for (int i = 0; i < merchantIds.size(); i++) {
                sqlBuilder.append(":merchant").append(i);
                if (i < merchantIds.size() - 1)
                    sqlBuilder.append(", ");
            }
            sqlBuilder.append(")");
        }

        if (tid != null && !tid.isBlank()) {
            sqlBuilder.append(" AND tid = :tid");
        }

        if (paymentChannel != null && !paymentChannel.isBlank()) {
            sqlBuilder.append(" AND paymentChannel = :paymentChannel");
        }

        sqlBuilder.append("""
                    AND (:currency IS NULL OR currency = :currency)
                    GROUP BY %s, merchant_id, cardType, currency
                ) sub
                """.formatted(groupBy));

        DatabaseClient.GenericExecuteSpec spec = databaseClient.sql(sqlBuilder.toString())
                .bind("fromDate", LocalDate.parse(fromDate))
                .bind("toDate", LocalDate.parse(toDate))
                .bind("currency", currency);

        for (int i = 0; i < merchantIds.size(); i++) {
            spec = spec.bind("merchant" + i, merchantIds.get(i));
        }

        if (tid != null && !tid.isBlank()) {
            spec = spec.bind("tid", tid);
        }
        if (paymentChannel != null && !paymentChannel.isBlank()) {
            spec = spec.bind("paymentChannel", paymentChannel);
        }

        return spec.map((row, meta) -> row.get("cnt", Long.class)).one().defaultIfEmpty(0L);
    }

    private static StringBuilder buildQuerySelectReport(String tid,String paymentChannel, String groupBy) {
        StringBuilder sqlBuilder = new StringBuilder(
                """
                            SELECT
                                %s AS date,
                                merchant_id,
                                tid,
                                paymentChannel,
                                cardType,
                                currency,
                                SUM(CASE WHEN transaction_type = 'Purchase' THEN total_trans ELSE 0 END) AS num_purchase_trans,
                                SUM(CASE WHEN transaction_type IN (
                                        'Refund',
                                        'Refund Capture',
                                        'Refund Dispute',
                                        'Void Purchase',
                                        'Void Authorize',
                                        'Void Capture',
                                        'Void Refund',
                                        'Void Refund Capture'
                                    ) THEN total_trans
                                    ELSE 0 END) AS num_refund_void_trans,
                               SUM(CASE WHEN transaction_type = 'Purchase' THEN amount ELSE 0 END) AS total_purchase_amount,
                                SUM(CASE WHEN transaction_type IN (
                                        'Refund',
                                        'Refund Capture',
                                        'Refund Dispute',
                                        'Void Purchase',
                                        'Void Authorize',
                                        'Void Capture',
                                        'Void Refund',
                                        'Void Refund Capture'
                                    ) THEN amount ELSE 0 END) AS total_refund
                            FROM upos_report
                            WHERE d_transaction_day BETWEEN :fromDate AND :toDate
                                AND merchant_id IN (:merchantIds)
                        """
                        .formatted(groupBy));

        if (tid != null && !tid.isBlank()) {
            sqlBuilder.append(" AND tid = :tid");
        }
        if (paymentChannel != null && !paymentChannel.isBlank()) {
            sqlBuilder.append(" AND paymentChannel = :paymentChannel");
        }
        sqlBuilder.append("""
                    AND (:currency IS NULL OR currency = :currency)
                    GROUP BY %s, merchant_id, cardType, currency,paymentchannel,tid
                    ORDER BY %s DESC
                """.formatted(groupBy, groupBy));
        return sqlBuilder;
    }

    public static String normalizeDate(String fromDate) {
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        try {
            LocalDate date = LocalDate.parse(fromDate, inputFormatter);
            return outputFormatter.format(date);
        } catch (DateTimeParseException e) {
            // Không đúng định dạng dd-MM-yyyy => giữ nguyên
            return fromDate;
        }
    }


    @Override
    public FileDownloadDto createExcel(DownloadTaskEntity task) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            logger.info("Start exporting Excel for taskId={}, fileName={}", task.getId(), task.getFileName());

            // Tạo thư mục lưu file nếu chưa có
            String baseDir = exportFileProperties.getBaseDir();
            File dir = new File(baseDir);
            if (!dir.exists())
                dir.mkdirs();

            String outputPath = baseDir + task.getFileHashName();
            // Parse requestParams từ task
            Map<String, Object> params = objectMapper.readValue(task.getRequestParams(), new TypeReference<>() {
            });
            String fromDate = (String) params.get("fromDate");
            String toDate = (String) params.get("toDate");
            String merchantId = (String) params.get("merchantId");
            String transactionId = (String) params.get("transactionId");
            String transactionType = (String) params.get("transactionType");
            String cardNumber = (String) params.get("cardNumber");
            String merchantTransRef = (String) params.get("merchantTransRef");
            String tid = (String) params.get("tid");
            String orderReference = (String) params.get("orderReference");
            String cardType = (String) params.get("cardType");
            String approvalCode = (String) params.get("approvalCode");
            String paymentChannel = (String) params.get("paymentChannel");
            String transactionState = (String) params.get("transactionState");
            String installmentStatus = (String) params.get("installmentStatus");
            int page = 0;
            int size = 10000;

            List<UposListResponse> allTransactions = new ArrayList<>();
            if (merchantId != null && !merchantId.isEmpty()) {
                while (true) {
                    Map<String, Object> conditions = new HashMap<>();
                    conditions.put("fromDate", fromDate);
                    conditions.put("toDate", toDate);
                    conditions.put("merchantId", merchantId);
                    conditions.put("tid", tid);
                    conditions.put("transId", transactionId);
                    conditions.put("page", page);
                    conditions.put("pageSize", size);
                    conditions.put("orderReference", orderReference);
                    conditions.put("merchantTransRef", merchantTransRef);
                    conditions.put("cardNumber", cardNumber);
                    conditions.put("cardType", cardType);
                    conditions.put("approvalCode", approvalCode);
                    conditions.put("paymentChannel", paymentChannel);
                    conditions.put("transactionType", transactionType);
                    conditions.put("transactionStatus", transactionState);
                    conditions.put("installmentStatus", installmentStatus);
                    conditions.put("sortOrder", "desc");
                    conditions.put("type", IConstants.TYPE_UPOS);
                    conditions.put("merchantChannel", "UPOS");
                    String opsQuery = OpenSearchQueryBuilder.buildQuery(conditions);
                    String opsStrData = repository.getTransactionList(opsQuery);

                    JsonNode root = objectMapper.readTree(opsStrData);
                    JsonNode hits = root.path("hits").path("hits");

                    if (!hits.isArray() || hits.size() == 0)
                        break;

                    for (JsonNode hit : hits) {
                        UposListResponse dto = mapJsonToUposTransactionDTO(hit);
                        if (dto != null) {
                            allTransactions.add(dto);
                        }
                    }

                    if (hits.size() < size)
                        break;
                    page++;
                }
                logger.info("Fetched total {} transactions", allTransactions.size());

            }
            try (
                    InputStream is = getClass().getClassLoader()
                            .getResourceAsStream("template/UposTemplate.xlsx");
                    Workbook workbook = new XSSFWorkbook(is);
                    FileOutputStream fos = new FileOutputStream(outputPath)) {
                if (is == null)
                    throw new FileNotFoundException("Template not found in resources/template/");

                Sheet sheet = workbook.getSheetAt(0);
                int startRow = 4;

                ZonedDateTime zFrom = Instant.parse(fromDate).atZone(ZoneId.systemDefault());
                ZonedDateTime zTo = Instant.parse(toDate).atZone(ZoneId.systemDefault());
                // Định dạng theo "yyyy-MM-dd HH:mm:ss"
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

                String fromDateTime = zFrom.format(formatter);
                String toDateTime = zTo.format(formatter);
                Row rowDate = sheet.createRow(1);
                rowDate.createCell(6).setCellValue("From: " + fromDateTime);
                rowDate.createCell(8).setCellValue("To: " + toDateTime);
                logger.info("Writing {} rows to Excel file", allTransactions.size());

                for (int i = 0; i < allTransactions.size(); i++) {
                    UposListResponse dto = allTransactions.get(i);
                    Row row = sheet.createRow(startRow + i);
                    row.createCell(0).setCellValue(i + 1);
                    row.createCell(1).setCellValue(dto.getMerchantId());
                    row.createCell(2).setCellValue(dto.getDate().replace("T", ""));
                    row.createCell(3).setCellValue(dto.getTid());
                    row.createCell(4).setCellValue(dto.getTransactionId());
                    row.createCell(5).setCellValue(dto.getOrderReference());
                    row.createCell(6).setCellValue(dto.getMerchantTransRef());
                    row.createCell(7).setCellValue(dto.getPaymentChannel());
                    row.createCell(8).setCellValue(dto.getCardNumber());
                    row.createCell(9).setCellValue(dto.getAmount());
                    row.createCell(10).setCellValue(dto.getCurrency());
                    row.createCell(11).setCellValue(dto.getResponseCode());
                    row.createCell(12).setCellValue(dto.getTransactionState());
                    row.createCell(13).setCellValue(dto.getInstallmentStatus());
                }

                workbook.write(fos);
            }

            // Tạo FileDownloadDto trả về
            File file = new File(outputPath);
            FileDownloadDto dto = new FileDownloadDto();
            dto.setFile_hash_name(task.getFileHashName());
            dto.setFile_name(task.getFileName());
            dto.setFile_path(outputPath);
            dto.setFile_size(file.length());
            dto.setUser(task.getUserId());
            logger.info("Exported {} transactions to {}", allTransactions.size(), outputPath);
            return dto;
        } catch (Exception e) {
            logger.error("Error while exporting Excel for task {}: {}", task.getId(), e.getMessage(), e);
            throw new RuntimeException("Export Excel failed", e);
        }
    }



    //Sync daily upos report
     @Override
    public Mono<Void> insertDailyUposReport(String fromDateStr, String toDateStr) {
        LocalDateTime fromDate = LocalDate.parse(fromDateStr).atStartOfDay();
        LocalDateTime toDate = LocalDate.parse(toDateStr).atTime(LocalTime.MAX);
        logger.info("Starting insertDailyUposReport from {} to {}", fromDate, toDate);

        DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME;
        String from = fromDate.format(formatter);
        String to = toDate.format(formatter);
        String baseQuery = IConstants.baseUposConditionJson.formatted(from, to);

        return fetchUposReportFromOpenSearch(baseQuery, null)
                .flatMap(reports -> {
                    logger.info("Fetched {} reports from the first OpenSearch request", reports.size());
                    return insertReportsIntoDatabase(reports)
                            .then(fetchNextPageReports(baseQuery, reports));
                })
                .doOnError(e -> logger.error("Error occurred during insertDailyUposReport", e))
                .then();
    }

    private Mono<Void> fetchNextPageReports(String baseQuery, List<UposReportEntity> previousReports) {
        String afterKeyJson = getAfterKeyFromReports(previousReports);
        if (afterKeyJson != null) {
            try {
                JsonNode afterKeyNode = new ObjectMapper().readTree(afterKeyJson);
                String queryWithAfterKey = OpenSearchQueryBuilder.buildQueryWithAfterKey(baseQuery, afterKeyNode);
                logger.info("Built query with afterKey: {}", afterKeyJson);

                return fetchUposReportFromOpenSearch(queryWithAfterKey, afterKeyJson)
                        .flatMap(nextReports -> {
                            logger.info("Fetched {} reports in next page", nextReports.size());
                            return insertReportsIntoDatabase(nextReports)
                                    .then(fetchNextPageReports(baseQuery, nextReports));
                        });
            } catch (Exception e) {
                logger.error("Failed to parse afterKey JSON: {}", afterKeyJson, e);
                throw new RuntimeException("Failed to parse afterKey JSON: " + afterKeyJson, e);
            }
        } else {
            logger.info("No afterKey found. All pages fetched.");
            return Mono.empty();
        }
    }

    private Mono<List<UposReportEntity>> fetchUposReportFromOpenSearch(String query, String afterKey) {
        return Mono.fromCallable(() -> {
            try {
                logger.info("Sending OpenSearch request{}", afterKey != null ? " with afterKey: " + afterKey : "");
                String response = repository.getTransactionReport(query);
                logger.info("Received response from OpenSearch");
                return convertToDTO(response);
            } catch (Exception e) {
                logger.error("Failed to fetch upos report from OpenSearch", e);
                throw new RuntimeException("Failed to fetch upos report from OpenSearch", e);
            }
        });
    }

    private List<UposReportEntity> convertToDTO(String response) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            JsonNode rootNode = mapper.readTree(response);
            JsonNode buckets = rootNode.path("aggregations").path("byComposite").path("buckets");
            List<UposReportEntity> reports = new ArrayList<>();

            for (JsonNode bucket : buckets) {
                UposReportEntity entity = new UposReportEntity();
                entity.setMerchantId(bucket.path("key").path("merchantId").asText());

                long epochMillis = bucket.path("key").path("transactionDay").asLong();
                LocalDate transactionDay = Instant.ofEpochMilli(epochMillis)
                        .atZone(ZoneId.of("Asia/Ho_Chi_Minh")).toLocalDate();
                entity.setDTransactionDay(transactionDay);
                entity.setTotalTrans(bucket.path("doc_count").asLong());
                entity.setCurrency(bucket.path("key").path("currency").asText());
                entity.setTransactionType(bucket.path("key").path("txnType").asText());
                entity.setPaymentChannel(bucket.path("key").path("paymentChannel").asText());
                entity.setTid(bucket.path("key").path("tid").asText());
                entity.setCardType(bucket.path("key").path("cardType").asText());
                JsonNode amountNode = bucket.path("amount").path("value");
                Long amount = amountNode.isNull() ? 0L : amountNode.asLong();
                entity.setAmount(BigDecimal.valueOf(amount));
                reports.add(entity);
            }

            logger.info("Converted OpenSearch response into {} report entities", reports.size());
            return reports;
        } catch (Exception e) {
            logger.error("Error converting OpenSearch response to DTO", e);
            throw new RuntimeException("Error converting OpenSearch response to DTO", e);
        }
    }
    public Mono<Void> insertReportsIntoDatabase(List<UposReportEntity> reports) {
        if (reports.isEmpty()) {
            logger.info("No reports to insert into database");
            return Mono.empty();
        }

        logger.info("Inserting {} reports into the database", reports.size());
        return Flux.fromIterable(reports)
                .flatMap(this::upsertReport, 10) // chạy tối đa 10 connections
                .then()
                .doOnError(e -> logger.error("Error occurred during database insertion", e));

    }

    private Mono<Void> upsertReport(UposReportEntity report) {
        logger.debug("Upserting report: {}", report);

        String upsertSql = "INSERT INTO upos_report (d_transaction_day, merchant_id, tid, paymentChannel, transaction_type, cardType, currency,amount,created_date,updated_date,total_trans) "
                + "VALUES (:d_transaction_day, :merchant_id, :tid, :paymentChannel, :transaction_type, :cardType, :currency,:amount,:created_date,:updated_date,:total_trans) "
                + "ON CONFLICT (d_transaction_day, merchant_id,tid, transaction_type, currency,cardType,paymentChannel) "
                + "DO UPDATE SET amount = :amount, updated_date = :updated_date, total_trans = :total_trans";

        return databaseClient.sql(upsertSql)
                .bind("d_transaction_day", report.getDTransactionDay())
                .bind("merchant_id", report.getMerchantId())
                .bind("transaction_type", report.getTransactionType())
                .bind("currency", report.getCurrency())
                .bind("cardType", report.getCardType())
                .bind("created_date", LocalDateTime.now())
                .bind("updated_date", LocalDateTime.now())
                .bind("total_trans", report.getTotalTrans())
                .bind("amount", report.getAmount())
                .bind("tid", report.getTid())
                .bind("paymentChannel", report.getPaymentChannel())
                .then()
                .doOnError(e -> logger.error("Error upserting report: {}", report, e)); // now valid
    }

    private String getAfterKeyFromReports(List<UposReportEntity> reports) {
        if (reports.isEmpty())
            return null;

        UposReportEntity last = reports.get(reports.size() - 1);
        Map<String, Object> afterKey = new LinkedHashMap<>();
        afterKey.put("transactionDay",
                last.getDTransactionDay().atStartOfDay(ZoneId.of("Asia/Ho_Chi_Minh")).toInstant().toEpochMilli());
        afterKey.put("merchantId", last.getMerchantId());
        afterKey.put("currency", last.getCurrency());
        afterKey.put("txnType", last.getTransactionType());
        afterKey.put("paymentChannel", last.getPaymentChannel());
        afterKey.put("cardType", last.getCardType());
        afterKey.put("tid", last.getTid());

        try {
            String afterKeyStr = new ObjectMapper().writeValueAsString(afterKey);
            logger.info("Generated afterKey from last report: {}", afterKeyStr);
            return afterKeyStr;
        } catch (JsonProcessingException e) {
            logger.error("Failed to serialize afterKey", e);
            throw new RuntimeException("Failed to serialize afterKey", e);
        }
    }

    //export excel upos report
    @Override
    public FileDownloadDto createReportExcel(DownloadTaskEntity task) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            logger.info("Start exporting Excel for taskId={}, fileName={}", task.getId(), task.getFileName());

            // Tạo thư mục lưu file nếu chưa có
            String baseDir = exportFileProperties.getBaseDir();
            File dir = new File(baseDir);
            if (!dir.exists())
                dir.mkdirs();

            String outputPath = baseDir + task.getFileHashName();
            // Parse requestParams từ task
            Map<String, Object> params = objectMapper.readValue(task.getRequestParams(), new TypeReference<>() {
            });
            String fromDate = normalizeDate((String) params.get("fromDate"));
            String toDate = normalizeDate((String) params.get("toDate"));
            String merchantId = (String) params.get("merchantId");
            String paymentChannel = (String) params.get("paymentChannel");
            String interval = (String) params.get("interval");
            String currency = (String) params.get("currency");
            String tid = (String) params.get("tid");
            String groupBy = switch (interval.toUpperCase()) {
                case "DAILY" -> "d_transaction_day";
                case "WEEKLY" -> "TO_CHAR(d_transaction_day, 'IYYY-\"W\"IW')";
                case "MONTHLY" -> "TO_CHAR(d_transaction_day, 'YYYY-MM')";
                default -> throw new IllegalArgumentException("Invalid interval: " + interval);
            };
            List<String> merchantIds = parseCsvToList((String) params.get("merchantId"));
            List<UposReportResponse> allTransactions = new ArrayList<>();
            if (merchantId != null && !merchantId.isEmpty()) {
                StringBuilder sqlBuilder = buildQuerySelectReport(tid, paymentChannel, groupBy);
                DatabaseClient.GenericExecuteSpec spec = databaseClient.sql(sqlBuilder.toString())
                            .bind("fromDate", LocalDate.parse(fromDate))
                            .bind("toDate", LocalDate.parse(toDate))
                            .bind("currency", currency)
                            .bind("merchantIds", merchantIds);
                    if (tid != null && !tid.isBlank()) {
                        spec = spec.bind("tid", tid);
                    }
                    if (paymentChannel != null && !paymentChannel.isBlank()) {
                        spec = spec.bind("paymentChannel", paymentChannel);
                    }
                allTransactions = spec
                        .map((row, meta) -> {
                            return new UposReportResponse(
                                row.get("date", String.class),
                                row.get("merchant_id", String.class),
                                row.get("tid", String.class),
                                row.get("paymentChannel", String.class),
                                row.get("cardType", String.class),
                                row.get("currency", String.class),
                                row.get("num_purchase_trans", Long.class),
                                row.get("num_refund_void_trans", Long.class),
                                row.get("total_purchase_amount", BigDecimal.class),
                                row.get("total_refund", BigDecimal.class));
                    })
                        .all()
                        .collectList().block();
                try (InputStream is = getClass().getClassLoader()
                        .getResourceAsStream("template/UposReportTemplate.xlsx");
                        Workbook workbook = new XSSFWorkbook(is);
                        FileOutputStream fos = new FileOutputStream(outputPath)) {
                    if (is == null)
                        throw new FileNotFoundException("Template not found in resources/template/");
                    Sheet sheet = workbook.getSheetAt(0);
                    int startRow = 4;

                    Row rowDate = sheet.createRow(1);
                    rowDate.createCell(5).setCellValue("From: " + fromDate);
                    rowDate.createCell(7).setCellValue("To: " + toDate);

                    for (int i = 0; i < allTransactions.size(); i++) {
                        UposReportResponse dto = allTransactions.get(i);
                        Row row = sheet.getRow(startRow + i);
                        if (row == null) {
                            row = sheet.createRow(startRow + i);
                        }
                        row.getCell(0).setCellValue(i + 1);
                        row.getCell(1).setCellValue(dto.getMerchantId());
                        row.getCell(2).setCellValue(dto.getDate());
                        row.getCell(3).setCellValue(dto.getTid());
                        row.getCell(4).setCellValue(dto.getPaymentChannel());
                        row.getCell(5).setCellValue(dto.getCardType());
                        row.getCell(6).setCellValue(dto.getPurchaseCount().toString());
                        row.getCell(7).setCellValue(dto.getRefundVoidCount().toString());
                        row.getCell(8).setCellValue(dto.getTotalPurchase().toString());
                        row.getCell(9).setCellValue(dto.getTotalRefundVoid().toString());
                        row.getCell(10).setCellValue(currency);
                    }
                    workbook.write(fos);
                }
            }
           // Tạo FileDownloadDto trả về
           File file = new File(outputPath);
           FileDownloadDto dto = new FileDownloadDto();
           dto.setFile_hash_name(task.getFileHashName());
           dto.setFile_name(task.getFileName());
           dto.setFile_path(outputPath);
           dto.setFile_size(file.length());
           logger.info("Exported {} transactions to {}", allTransactions.size(), outputPath);
           return dto;

        } catch (Exception e) {
            logger.error("Error while exporting Excel for task {}: {}", task.getId(), e.getMessage(), e);
            throw new RuntimeException("Export Excel failed", e);
        }
    }
    private List<String> parseCsvToList(String csv) {
        if (!StringUtils.hasText(csv))
            return List.of();
        return Arrays.stream(csv.split(","))
                .map(String::trim)
                .filter(StringUtils::hasText)
                .toList();
    }
}
