package vn.onepay.transaction.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ServerWebExchange;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import vn.onepay.transaction.constant.IConstants;
import vn.onepay.transaction.dto.AdvanceInformation;
import vn.onepay.transaction.dto.ApiResponse;
import vn.onepay.transaction.dto.FeeInformation;
import vn.onepay.transaction.dto.MerchantInformation;
import vn.onepay.transaction.dto.OrderInformation;
import vn.onepay.transaction.dto.PagedResponse;
import vn.onepay.transaction.dto.PaymentMethod;
import vn.onepay.transaction.dto.PromotionInformation;
import vn.onepay.transaction.dto.RiskManagement;
import vn.onepay.transaction.dto.TransactionDTO;
import vn.onepay.transaction.dto.TransactionDetailResponse;
import vn.onepay.transaction.dto.TransactionHistoryDTO;
import vn.onepay.transaction.dto.TransactionInformation;
import vn.onepay.transaction.dto.TransactionPromotionDTO;
import vn.onepay.transaction.dto.TransactionPromotionDetailResponse;
import vn.onepay.transaction.entity.DownloadTaskEntity;
import vn.onepay.transaction.exception.NoItemExistsException;
import vn.onepay.transaction.opensearch.OpenSearchQueryBuilder;
import vn.onepay.transaction.repository.TransactionOpenSearchRepository;
import vn.onepay.transaction.service.MerchantService;
import vn.onepay.transaction.service.TransactionService;

@Service
public class TransactionServiceImpl implements TransactionService {
        private static final Logger logger = LoggerFactory.getLogger(TransactionServiceImpl.class);
        private static final DateTimeFormatter INPUT_FORMATTER = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
        private static final DateTimeFormatter OUTPUT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
        private static final ObjectMapper objectMapper = new ObjectMapper()
                        .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        @Autowired
        private TransactionOpenSearchRepository repository;

        @Autowired
        private MerchantService merchantService;

        @Override
        public TransactionDetailResponse getTransactionDetail(String docId) {
                logger.info("Start service getTransactionDetail with docId: {}", docId);
                try {
                        String opsStrData = repository.getTransactionDetailByDocId(docId);

                        ObjectMapper mapper = new ObjectMapper();
                        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                        JsonNode root = mapper.readTree(opsStrData);

                        String isFound = safeText(root, "found");
                        if (isFound.equals("false"))
                                throw new NoItemExistsException("Transaction with docId " + docId + " not found");

                        TransactionDetailResponse response = mapJsonToTransactionDetailResponse(root.path("_source"));
                        return response;
                } catch (NoItemExistsException e) {
                        throw e;
                } catch (Exception e) {
                        logger.error("Error getTransactionDetail", e);
                }

                return null;
        }

        @Override
        public Mono<ApiResponse<PagedResponse<TransactionDTO>>> getTransactions(ServerWebExchange exchange,
                        String fromDate, String toDate,
                        String merchantId,
                        String transId, String paymentMethod, String transactionStatus, String transactionType,
                        String searchType, String searchKeyword,
                        int page, int size, String sortField,
                        String sortOrder, Boolean isPromotion) {

                return merchantService.resolveMerchantIdFilter(exchange, merchantId)
                                .flatMap(merchantIdFilter -> {
                                        if (merchantIdFilter.isEmpty()) {
                                                logger.warn("No merchantId matched -> returning empty response");

                                                PagedResponse<TransactionDTO> emptyPagedResponse = PagedResponse
                                                                .<TransactionDTO>builder()
                                                                .currentPage(page)
                                                                .pageSize(size)
                                                                .totalPages(0)
                                                                .total(0L)
                                                                .items(Collections.emptyList())
                                                                .build();

                                                ApiResponse<PagedResponse<TransactionDTO>> emptyResponse = ApiResponse
                                                                .<PagedResponse<TransactionDTO>>builder()
                                                                .status("success")
                                                                .total(0)
                                                                .data(emptyPagedResponse)
                                                                .build();

                                                return Mono.just(emptyResponse);
                                        }
                                        Map<String, Object> conditions = new HashMap<>();
                                        conditions.put("fromDate", fromDate);
                                        conditions.put("toDate", toDate);
                                        conditions.put("merchantId", String.join(",", merchantIdFilter));
                                        conditions.put("transactionStatus", transactionStatus);
                                        conditions.put("transactionType", transactionType);
                                        conditions.put("page", page);
                                        conditions.put("pageSize", size);
                                        conditions.put("sortOrder", sortOrder);
                                        conditions.put("transId", transId);
                                        conditions.put("paymentMethod", paymentMethod);
                                        conditions.put("searchKeyword", searchKeyword);
                                        conditions.put("searchType", searchType);
                                        conditions.put("serviceName", IConstants.TYPE_TRANSACTION);
                                        if (Boolean.TRUE.equals(isPromotion)) {
                                                conditions.put("Promotion", "Promotion");
                                        }

                                        logger.info("Start service getTransactionList with conditions: {}", conditions);

                                        return Mono.fromCallable(() -> {
                                                String opsQuery = OpenSearchQueryBuilder.buildQuery(conditions);
                                                String opsStrData = repository.getTransactionList(opsQuery);
                                                JsonNode root = objectMapper.readTree(opsStrData);

                                                JsonNode hits = root.path("hits").path("hits");
                                                int totalHits = root.path("hits").path("total").path("value").asInt(0);
                                                int totalPage = (int) Math.ceil((double) totalHits / size);

                                                List<TransactionDTO> transList = StreamSupport
                                                                .stream(hits.spliterator(), false)
                                                                .map(this::mapJsonToTransactionDTO)
                                                                .filter(Objects::nonNull)
                                                                .collect(Collectors.toList());

                                                logger.info("Mapped {} transactions", transList.size());

                                                PagedResponse<TransactionDTO> pagedResponse = PagedResponse
                                                                .<TransactionDTO>builder()
                                                                .currentPage(page)
                                                                .pageSize(size)
                                                                .totalPages(totalPage)
                                                                .total((long) totalHits)
                                                                .items(transList)
                                                                .build();

                                                return ApiResponse.<PagedResponse<TransactionDTO>>builder()
                                                                .status("success")
                                                                .total(totalHits)
                                                                .data(pagedResponse)
                                                                .build();
                                        }).subscribeOn(Schedulers.boundedElastic())
                                                        .onErrorResume(e -> {
                                                                logger.error("Error getListTransaction: ", e);
                                                                return Mono.just(ApiResponse
                                                                                .<PagedResponse<TransactionDTO>>builder()
                                                                                .status("fail")
                                                                                .total(0)
                                                                                .data(null)
                                                                                .build());
                                                        });
                                });
        }

        @Override
        public List<TransactionHistoryDTO> getTransactionHistory(String baseId) {
                List<TransactionHistoryDTO> data = new ArrayList<>();
                try {
                        logger.info("Start service getTransactionHistory with baseId: {}", baseId);
                        String opsQuery = OpenSearchQueryBuilder.buildQueryHistory(baseId);
                        String opsStrData = repository.getTransactionList(opsQuery);

                        ObjectMapper mapper = new ObjectMapper();
                        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                        JsonNode root = mapper.readTree(opsStrData);
                        JsonNode hits = root.path("hits").path("hits");
                        int totalHits = root.path("hits").path("total").path("value").asInt(0);
                        logger.info("Received response from OpenSearch - totalHits: {}", totalHits);
                        for (JsonNode hit : hits) {
                                TransactionDTO dto = mapJsonToTransactionDTO(hit);
                                if (dto != null) {
                                        TransactionHistoryDTO dtoHis = TransactionHistoryDTO.builder()
                                                        .transactionId(dto.getTransactionId())
                                                        .date(dto.getTransCreatedTime())
                                                        .amount(dto.getTransAmount())
                                                        .currency(dto.getCurrency())
                                                        .description(dto.getResponseDescription())
                                                        .transactionType(dto.getTransType())
                                                        .status(dto.getTransStatus())
                                                        .build();
                                        data.add(dtoHis);
                                }
                        }
                        if (totalHits < 1)
                                throw new NoItemExistsException(
                                                "Transaction with baseId: " + baseId + " not found!");

                } catch (NoItemExistsException e) {
                        throw e;
                } catch (Exception e) {
                        logger.error("Error getListTransaction: ", e);
                }
                return data;
        }

        private String safeText(JsonNode node, String... path) {
                for (String p : path) {
                        if (node == null)
                                return null;
                        node = node.path(p);
                }
                return node.isMissingNode() ? null : node.asText(null);
        }

        private static String convertDate(String datetimeStr) {
                try {
                        OffsetDateTime odt = OffsetDateTime.parse(datetimeStr, INPUT_FORMATTER);
                        return odt.toLocalDateTime().format(OUTPUT_FORMATTER);
                } catch (DateTimeParseException e) {
                        logger.warn("Invalid datetime format: {}", datetimeStr);
                        return null;
                }
        }

        private TransactionDTO mapJsonToTransactionDTO(JsonNode hit) {
                try {
                        JsonNode source = hit.path("_source");
                        if (source.isMissingNode())
                                return null;

                        JsonNode merchantNode = source.path("msp_merchant");
                        JsonNode enrichTxnNode = source.path("enrich_txn");
                        JsonNode paymentNode = source.path("msp_payment");
                        JsonNode invoiceNode = source.path("msp_invoice");

                        String docId = safeText(hit, "_id");
                        if (docId == null || docId.isEmpty())
                                return null;

                        TransactionDTO dto = TransactionDTO.builder()
                                        .docId(docId)
                                        .merchantId(safeText(merchantNode, "s_id"))
                                        .baseId(safeText(enrichTxnNode, "s_base_id"))
                                        .transactionId(safeText(enrichTxnNode, "s_id"))
                                        .orderReference(safeText(enrichTxnNode, "s_txn_ref"))
                                        .cardNumber(safeText(paymentNode, "s_e_card_number"))
                                        .transType(safeText(enrichTxnNode, "s_txn_type"))
                                        .responseCode(safeText(enrichTxnNode, "s_response_code"))
                                        .responseDescription(safeText(enrichTxnNode, "s_e_response_code"))
                                        .transStatus(safeText(enrichTxnNode, "s_state"))
                                        .paymentSource(safeText(paymentNode, "s_e_gate_label"))
                                        .currency(safeText(invoiceNode, "s_currencies"))
                                        // .orderSource()
                                        .build();

                        // paymentMethod logic
                        String paymentMethod = safeText(paymentNode, "s_e_gate_label");
                        if ("International".equalsIgnoreCase(paymentMethod)) {
                                String itaState = safeText(paymentNode, "s_ita_state");
                                String eSource = safeText(paymentNode, "s_e_source");
                                if (itaState != null && !itaState.isEmpty())
                                        paymentMethod = IConstants.PAYMENT_METHOD_INSTALLMENT;
                                if (eSource != null && !"Direct".equalsIgnoreCase(eSource))
                                        paymentMethod = eSource;
                        }
                        dto.setPaymentMethod(paymentMethod);

                        // amount
                        String amountStr = safeText(invoiceNode, "n_amount");
                        if (amountStr != null) {
                                dto.setTransAmount(new BigDecimal(amountStr));
                        }

                        // datetime
                        dto.setOrderCreatedTime(convertDate(safeText(enrichTxnNode, "d_original_date")));
                        dto.setTransCreatedTime(convertDate(safeText(enrichTxnNode, "d_create")));
                        dto.setTransCompletedTime(convertDate(safeText(enrichTxnNode, "d_update")));

                        return dto;
                } catch (Exception e) {
                        logger.warn("Error mapping transaction DTO: ", e);
                        return null;
                }
        }

        private TransactionPromotionDTO mapJsonToTransactionPromotionDTO(JsonNode hit) {
                try {
                        JsonNode source = hit.path("_source");
                        if (source.isMissingNode())
                                return null;

                        JsonNode merchantNode = source.path("msp_merchant");
                        JsonNode enrichTxnNode = source.path("enrich_txn");
                        JsonNode paymentNode = source.path("msp_payment");
                        JsonNode invoiceNode = source.path("msp_invoice");
                        JsonNode promotion = source.path("onepr_pr");
                        String docId = safeText(hit, "_id");
                        if (docId == null || docId.isEmpty())
                                return null;

                        TransactionPromotionDTO dto = TransactionPromotionDTO.builder()
                                        .docId(docId)
                                        .merchantId(safeText(merchantNode, "s_id"))
                                        .baseId(safeText(enrichTxnNode, "s_base_id"))
                                        .transactionId(safeText(enrichTxnNode, "s_id"))
                                        .orderReference(safeText(enrichTxnNode, "s_txn_ref"))
                                        .cardNumber(safeText(paymentNode, "s_e_card_number"))
                                        .transactionType(safeText(enrichTxnNode, "s_txn_type"))
                                        .responseCode(safeText(enrichTxnNode, "s_response_code"))
                                        .responseDescription(safeText(enrichTxnNode, "s_e_response_code"))
                                        .transactionStatus(safeText(enrichTxnNode, "s_state"))
                                        .currency(safeText(invoiceNode, "s_currencies"))
                                        .promotionCode(safeText(promotion, "s_id"))
                                        .promotionName(safeText(promotion, "s_name"))
                                        .build();

                        // paymentMethod logic
                        String paymentMethod = safeText(paymentNode, "s_e_gate_label");
                        if ("International".equalsIgnoreCase(paymentMethod)) {
                                String itaState = safeText(paymentNode, "s_ita_state");
                                String eSource = safeText(paymentNode, "s_e_source");
                                if (itaState != null && !itaState.isEmpty())
                                        paymentMethod = IConstants.PAYMENT_METHOD_INSTALLMENT;
                                if (eSource != null && !"Direct".equalsIgnoreCase(eSource))
                                        paymentMethod = eSource;
                        }
                        dto.setPaymentMethod(paymentMethod);

                        // amount
                        String amountStr = safeText(enrichTxnNode, "n_original_amount");
                        if (amountStr != null)
                                dto.setOrgAmount(new BigDecimal(amountStr));

                        String paymentAmount = safeText(paymentNode, "n_amount");
                        if (paymentAmount != null)
                                dto.setPaymentAmount(new BigDecimal(paymentAmount));

                        // datetime
                        dto.setCreatedDate(convertDate(safeText(enrichTxnNode, "d_create")));

                        if (dto.getPromotionCode() == null)
                                dto.setPromotionCode(safeText(paymentNode, "s_pr_id"));
                        return dto;
                } catch (Exception e) {
                        logger.warn("Error mapping TransactionPromotionDTO: ", e);
                        return null;
                }
        }

        private TransactionDetailResponse mapJsonToTransactionDetailResponse(JsonNode source) {
                try {
                        logger.info("Start map res json to TransactionDetailResponse...");
                        JsonNode enrich = source.path("enrich_txn");
                        JsonNode payment = source.path("msp_payment");
                        JsonNode invoice = source.path("msp_invoice");
                        JsonNode merchant = source.path("msp_merchant");
                        JsonNode promotion = source.path("onepr_pr");
                        // JsonNode partner = source.path("partner");
                        JsonNode oc_transaction = source.path("oc_transaction");
                        OrderInformation orderInformation = OrderInformation.builder()
                                        .orderCreatedTime(convertDate(safeText(invoice, "d_create")))
                                        .orderReference(safeText(enrich, "s_txn_ref"))
                                        .orderCurrency(safeText(invoice, "s_currencies"))
                                        .build();
                        String amountStr = safeText(source, "msp_invoice", "n_amount");
                        if (amountStr != null) {
                                orderInformation.setOrderAmount(new BigDecimal(amountStr));
                        }

                        MerchantInformation merchantInformation = MerchantInformation.builder()
                                        .merchantId(safeText(merchant, "s_id"))
                                        .merchantName(safeText(merchant, "s_name"))
                                        .build();

                        // paymentMethod logic
                        String paymentMethod = safeText(payment, "s_e_gate_label");
                        if ("International".equalsIgnoreCase(paymentMethod)) {
                                String itaState = safeText(payment, "s_ita_state");
                                String s_e_source = safeText(payment, "s_e_source");
                                if (itaState != null && !itaState.isEmpty())
                                        paymentMethod = IConstants.PAYMENT_METHOD_INSTALLMENT;
                                if (s_e_source != null && !"Direct".equalsIgnoreCase(s_e_source))
                                        paymentMethod = s_e_source;
                        }
                        TransactionInformation transactionInformation = TransactionInformation.builder()
                                        .transactionId(safeText(enrich, "s_base_id"))
                                        .parentTransactionId(safeText(enrich, "s_original_id"))
                                        .transactionType(safeText(enrich, "s_txn_type"))
                                        .paymentMethod(paymentMethod)
                                        .transactionCreatedTime(convertDate(safeText(enrich, "d_create")))
                                        .transactionCompletedTime(convertDate(safeText(enrich, "d_update")))
                                        .transactionCurrency(safeText(payment, "s_currency"))
                                        .transactionStatus(safeText(enrich, "s_state"))
                                        .responseCode(safeText(enrich, "s_e_response_code"))
                                        .build();

                        String transAmount = safeText(payment, "n_amount");
                        if (amountStr != null) {
                                transactionInformation.setTransactionAmount(new BigDecimal(transAmount));
                        }

                        PaymentMethod paymentMethodInfo = PaymentMethod.builder()
                                        .cardNumber(safeText(payment, "s_e_card_number"))
                                        .cardBrand(safeText(payment, "s_data", "instrument", "s_card_type"))
                                        .cardType(safeText(payment, "s_data", "instrument", "s_type"))
                                        .cardExpiry(safeText(payment, "s_data", "card_exp"))
                                        .issuer(safeText(payment, "s_data", "instrument", "s_issuer"))
                                        .nameOnCard(safeText(payment, "s_ins_name"))
                                        .cscResultCode(safeText(oc_transaction, "s_cscresult_code"))
                                        .authorizationCode(safeText(oc_transaction, "s_authorisation_code"))
                                        .build();
                        FeeInformation feeInformation = FeeInformation.builder()
                                        .build();
                        AdvanceInformation advanceInformation = AdvanceInformation.builder()
                                        .build();
                        RiskManagement riskManagement = RiskManagement.builder()
                                        .ipAddress(safeText(oc_transaction, "s_ticket_number"))
                                        .ipProxy(safeText(oc_transaction, "s_ip_proxy"))
                                        .ipCountry(safeText(oc_transaction, "s_ip_country"))
                                        .binCountry(safeText(source, "of_bin_base", "s_ip_country"))
                                        .riskAssessment(safeText(source, "fraud_scan", "n_fraud_status_for_mer_alert"))
                                        .build();
                        PromotionInformation promotionInformation = PromotionInformation.builder()
                                        .promotionCode(safeText(promotion, "s_id"))
                                        .promotionName(safeText(promotion, "s_name"))
                                        .discountCurrency(safeText(invoice, "s_currencies"))
                                        .build();
                        if (promotionInformation.getPromotionCode() != null
                                        && !promotionInformation.getPromotionCode().isEmpty()) {
                                promotionInformation.setDiscountAmount(orderInformation.getOrderAmount()
                                                .subtract(transactionInformation.getTransactionAmount()));
                        }

                        TransactionDetailResponse dto = TransactionDetailResponse.builder()
                                        .transactionInformation(transactionInformation)
                                        .orderInformation(orderInformation)
                                        .merchantInformation(merchantInformation)
                                        .paymentMethod(paymentMethodInfo)
                                        .feeInformation(feeInformation)
                                        .advanceInformation(advanceInformation)
                                        .riskManagement(riskManagement)
                                        .promotionInformation(promotionInformation)
                                        .build();
                        logger.info("Map TransactionDetailResponse success!!");
                        return dto;

                } catch (Exception e) {
                        logger.warn("Error mapping transaction detail: ", e);
                        return null;
                }
        }

        @Override
        public String createExcel(DownloadTaskEntity task) {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

                try {
                        // Tạo thư mục lưu file nếu chưa có
                        String baseDir = "./exports/";
                        File dir = new File(baseDir);
                        if (!dir.exists())
                                dir.mkdirs();

                        String outputPath = baseDir + task.getFileName();

                        // Parse requestParams từ task
                        Map<String, Object> params = objectMapper.readValue(task.getRequestParams(),
                                        new TypeReference<>() {
                                        });
                        String fromDate = (String) params.get("fromDate");
                        String toDate = (String) params.get("toDate");
                        String merchantId = (String) params.get("merchantId");
                        String transId = (String) params.get("transId");
                        String paymentMethod = (String) params.get("paymentMethod");
                        String sortField = (String) params.getOrDefault("sortField", "createdDate");
                        String sortOrder = (String) params.getOrDefault("sortOrder", "desc");
                        String transactionStatus = (String) params.get("transactionStatus");
                        String transactionType = (String) params.get("transactionType");
                        String searchKeyword = (String) params.get("searchKeyword");
                        Boolean isPromotion = (Boolean) params.get("isPromotion");
                        String searchType = (String) params.get("searchType");
                        int page = 0;
                        int size = 50000;
                        List<TransactionDTO> allTransactions = new ArrayList<>();

                        while (true) {
                                Map<String, Object> conditions = new HashMap<>();
                                conditions.put("fromDate", fromDate);
                                conditions.put("toDate", toDate);
                                conditions.put("merchantId", merchantId);
                                conditions.put("transactionStatus", transactionStatus);
                                conditions.put("transactionType", transactionType);
                                conditions.put("page", page);
                                conditions.put("pageSize", size);
                                conditions.put("sortOrder", sortOrder);
                                conditions.put("transId", transId);
                                conditions.put("paymentMethod", paymentMethod);
                                conditions.put("searchKeyword", searchKeyword);
                                conditions.put("searchType", searchType);
                                if (isPromotion != null && isPromotion)
                                        conditions.put("Promotion", "Promotion");

                                String opsQuery = OpenSearchQueryBuilder.buildQuery(conditions);
                                String opsStrData = repository.getTransactionList(opsQuery);

                                JsonNode root = objectMapper.readTree(opsStrData);
                                JsonNode hits = root.path("hits").path("hits");

                                if (!hits.isArray() || hits.size() == 0)
                                        break;

                                for (JsonNode hit : hits) {
                                        TransactionDTO dto = mapJsonToTransactionDTO(hit);
                                        if (dto != null) {
                                                allTransactions.add(dto);
                                        }
                                }

                                if (hits.size() < size)
                                        break;
                                page++;
                        }

                        // Đọc file template từ resources/template/
                        try (
                                        InputStream is = getClass().getClassLoader()
                                                        .getResourceAsStream("template/TransactionTemplate.xlsx");
                                        Workbook workbook = new XSSFWorkbook(is);
                                        FileOutputStream fos = new FileOutputStream(outputPath)) {
                                if (is == null)
                                        throw new FileNotFoundException("Template not found in resources/template/");

                                Sheet sheet = workbook.getSheetAt(0);
                                int startRow = 4;

                                ZonedDateTime zFrom = Instant.parse(fromDate).atZone(ZoneId.systemDefault());
                                ZonedDateTime zTo = Instant.parse(toDate).atZone(ZoneId.systemDefault());
                                // Định dạng theo "yyyy-MM-dd HH:mm:ss"
                                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

                                String fromDateTime = zFrom.format(formatter);
                                String toDateTime = zTo.format(formatter);
                                Row rowDate = sheet.createRow(1);
                                rowDate.createCell(7).setCellValue(fromDateTime);
                                rowDate.createCell(9).setCellValue(toDateTime);

                                for (int i = 0; i < allTransactions.size(); i++) {
                                        TransactionDTO dto = allTransactions.get(i);
                                        Row row = sheet.createRow(startRow + i);
                                        row.createCell(0).setCellValue(i + 1);
                                        row.createCell(1).setCellValue(dto.getMerchantId());
                                        row.createCell(2).setCellValue(dto.getTransactionId());
                                        row.createCell(3).setCellValue(dto.getOrderReference());
                                        row.createCell(4)
                                                        .setCellValue(dto.getTransAmount() != null
                                                                        ? dto.getTransAmount().toString()
                                                                        : "");
                                        row.createCell(5).setCellValue(dto.getCurrency());
                                        row.createCell(6).setCellValue(dto.getOrderCreatedTime().replace("T", " "));
                                        row.createCell(7).setCellValue(dto.getTransCreatedTime().replace("T", " "));
                                        row.createCell(8).setCellValue(dto.getTransCompletedTime().replace("T", " "));
                                        row.createCell(9).setCellValue(dto.getOrderSource());
                                        row.createCell(10).setCellValue(dto.getPaymentMethod());
                                        row.createCell(11).setCellValue(dto.getPaymentSource());
                                        row.createCell(12).setCellValue(dto.getCardNumber());
                                        row.createCell(13).setCellValue(dto.getTransType());
                                }

                                workbook.write(fos);
                        }

                        logger.info("Exported {} transactions to {}", allTransactions.size(), outputPath);
                        return outputPath;

                } catch (Exception e) {
                        logger.error("Error while exporting Excel for task {}: {}", task.getId(), e.getMessage(), e);
                        throw new RuntimeException("Export Excel failed", e);
                }
        }

        private TransactionPromotionDetailResponse mapJsonToTransactionPromotionDetailResponse(JsonNode source) {
                try {
                        logger.info("Start map res json to TransactionPromotionDetailResponse...");
                        JsonNode merchantNode = source.path("msp_merchant");
                        JsonNode enrichTxnNode = source.path("enrich_txn");
                        JsonNode paymentNode = source.path("msp_payment");
                        JsonNode invoiceNode = source.path("msp_invoice");
                        JsonNode promotion = source.path("onepr_pr");
                        // JsonNode partner = source.path("partner");
                        JsonNode oc_transaction = source.path("oc_transaction");
                        TransactionPromotionDetailResponse dto = TransactionPromotionDetailResponse.builder()
                                        .merchantId(safeText(merchantNode, "s_id"))
                                        .transId(safeText(enrichTxnNode, "s_id"))
                                        .merTransactionId(safeText(enrichTxnNode, "s_txn_ref"))
                                        .promoCode(safeText(promotion, "s_id"))
                                        .promoName(safeText(promotion, "s_name"))
                                        .responseCode(safeText(enrichTxnNode, "s_e_response_code"))
                                        .transactionStatus(safeText(enrichTxnNode, "s_state"))
                                        .cardNumber(safeText(paymentNode, "s_e_card_number"))
                                        .cardType(safeText(paymentNode, "s_e_card"))
                                        .cardExp(safeText(paymentNode, "s_data", "card_exp"))
                                        .cscResultCode(safeText(oc_transaction, "s_cscresult_code"))
                                        .tokenExp(safeText(paymentNode, "s_data", "instrument",
                                                        "s_application_expiration_date"))
                                        .tokenNumber(safeText(paymentNode, "s_data", "instrument",
                                                        "s_application_primary_account_number"))
                                        .deviceId(safeText(paymentNode, "s_data", "instrument",
                                                        "s_device_manufacturer_identifier"))
                                        .tokenType(safeText(paymentNode, "s_data", "instrument", "s_type"))
                                        .zip(safeText(oc_transaction, "s_zip_postal_code"))
                                        .address(safeText(oc_transaction, "s_state_province"))
                                        .city(safeText(oc_transaction, "s_city_town"))
                                        .address(safeText(oc_transaction, "s_address"))
                                        .transactionType(safeText(enrichTxnNode, "s_txn_type"))
                                        .build();
                        // paymentMethod logic
                        String paymentMethod = safeText(paymentNode, "s_e_gate_label");
                        if ("International".equalsIgnoreCase(paymentMethod)) {
                                String itaState = safeText(paymentNode, "s_ita_state");
                                String eSource = safeText(paymentNode, "s_e_source");
                                if (itaState != null && !itaState.isEmpty())
                                        paymentMethod = IConstants.PAYMENT_METHOD_INSTALLMENT;
                                if (eSource != null && !"Direct".equalsIgnoreCase(eSource))
                                        paymentMethod = eSource;
                        }
                        dto.setPaymentMethod(paymentMethod);
                        // amount
                        String amountStr = safeText(enrichTxnNode, "n_original_amount");
                        if (amountStr != null)
                                dto.setOrgAmount(new BigDecimal(amountStr));

                        String paymentAmount = safeText(paymentNode, "n_amount");
                        if (paymentAmount != null)
                                dto.setPaymentAmount(new BigDecimal(paymentAmount));

                        // datetime
                        dto.setCreatedDate(convertDate(safeText(enrichTxnNode, "d_create")));
                        logger.info("Map TransactionPromotionDetailResponse success!!");
                        return dto;

                } catch (Exception e) {
                        logger.warn("Error mapping TransactionPromotionDetailResponse: ", e);
                        return null;
                }
        }

        @Override
        public Mono<ApiResponse<PagedResponse<TransactionPromotionDTO>>> getTransactionsPromotion(
                        ServerWebExchange exchange, String fromDate,
                        String toDate, String merchantId, String transId, String merTransactionId, String cardNumber,
                        String authCode, String promoCode, String promoName, String paymentMethod,
                        String transactionType, String transactionStatus, int page, int size, String sortField,
                        String sortOrder) {

                return merchantService.resolveMerchantIdFilter(exchange, merchantId)
                                .flatMap(merchantIdFilter -> {
                                        if (merchantIdFilter.isEmpty()) {
                                                logger.warn("No merchantId matched -> returning empty response");

                                                PagedResponse<TransactionPromotionDTO> emptyPagedResponse = PagedResponse
                                                                .<TransactionPromotionDTO>builder()
                                                                .currentPage(page)
                                                                .pageSize(size)
                                                                .totalPages(0)
                                                                .total(0L)
                                                                .items(Collections.emptyList())
                                                                .build();

                                                ApiResponse<PagedResponse<TransactionPromotionDTO>> emptyResponse = ApiResponse
                                                                .<PagedResponse<TransactionPromotionDTO>>builder()
                                                                .status("success")
                                                                .total(0)
                                                                .data(emptyPagedResponse)
                                                                .build();

                                                return Mono.just(emptyResponse);
                                        }
                                        Map<String, Object> conditions = new HashMap<>();
                                        conditions.put("fromDate", fromDate);
                                        conditions.put("toDate", toDate);
                                        conditions.put("merchantId", String.join(",", merchantIdFilter));
                                        conditions.put("transactionStatus", transactionStatus);
                                        conditions.put("transactionType", transactionType);
                                        conditions.put("page", page);
                                        conditions.put("pageSize", size);
                                        conditions.put("sortOrder", sortOrder);
                                        conditions.put("transId", transId);
                                        conditions.put("paymentMethod", paymentMethod);
                                        conditions.put("merTransactionId", merTransactionId);
                                        conditions.put("cardNumber", cardNumber);
                                        conditions.put("authCode", authCode);
                                        conditions.put("promoCode", promoCode);
                                        conditions.put("promoName", promoName);
                                        conditions.put("Promotion", "Promotion");
                                        conditions.put("searchPromotion", "1");
                                        logger.info("Start service getTransactionsPromotion with conditions: {}",
                                                        conditions);

                                        return Mono.fromCallable(() -> {
                                                String opsQuery = OpenSearchQueryBuilder.buildQuery(conditions);
                                                String opsStrData = repository.getTransactionList(opsQuery); // blocking
                                                JsonNode root = objectMapper.readTree(opsStrData);

                                                JsonNode hits = root.path("hits").path("hits");
                                                int totalHits = root.path("hits").path("total").path("value").asInt(0);
                                                int totalPage = (int) Math.ceil((double) totalHits / size);

                                                List<TransactionPromotionDTO> transList = StreamSupport
                                                                .stream(hits.spliterator(), false)
                                                                .map(this::mapJsonToTransactionPromotionDTO)
                                                                .filter(Objects::nonNull)
                                                                .collect(Collectors.toList());

                                                logger.info("Mapped {} transactions", transList.size());

                                                PagedResponse<TransactionPromotionDTO> pagedResponse = PagedResponse
                                                                .<TransactionPromotionDTO>builder()
                                                                .currentPage(page)
                                                                .pageSize(size)
                                                                .totalPages(totalPage)
                                                                .total((long) transList.size())
                                                                .items(transList)
                                                                .build();

                                                return ApiResponse.<PagedResponse<TransactionPromotionDTO>>builder()
                                                                .status("success")
                                                                .total(totalHits)
                                                                .data(pagedResponse)
                                                                .build();
                                        })
                                                        .subscribeOn(Schedulers.boundedElastic()) // vì có I/O + parse
                                                                                                  // JSON
                                                        .onErrorResume(e -> {
                                                                logger.error("Error getTransactionsPromotion: ", e);
                                                                return Mono.just(ApiResponse
                                                                                .<PagedResponse<TransactionPromotionDTO>>builder()
                                                                                .status("fail")
                                                                                .total(0)
                                                                                .data(null)
                                                                                .build());
                                                        });
                                });
        }

        @Override
        public TransactionPromotionDetailResponse getTransactionPromotionDetail(String docId) {
                logger.info("Start service getTransactionPromotionDetail with docId: {}", docId);
                try {
                        String opsStrData = repository.getTransactionDetailByDocId(docId);

                        ObjectMapper mapper = new ObjectMapper();
                        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                        JsonNode root = mapper.readTree(opsStrData);

                        String isFound = safeText(root, "found");
                        if (isFound.equals("false"))
                                throw new NoItemExistsException("Transaction with docId " + docId + " not found");

                        TransactionPromotionDetailResponse response = mapJsonToTransactionPromotionDetailResponse(
                                        root.path("_source"));
                        return response;
                } catch (NoItemExistsException e) {
                        throw e;
                } catch (Exception e) {
                        logger.error("Error getTransactionPromotionDetail", e);
                }

                return null;
        }

}
