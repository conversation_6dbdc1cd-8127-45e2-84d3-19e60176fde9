package vn.onepay.transaction.service;

import org.springframework.web.server.ServerWebExchange;

import reactor.core.publisher.Mono;
import vn.onepay.transaction.dto.ApiResponse;
import vn.onepay.transaction.dto.PagedResponse;
import vn.onepay.transaction.dto.RequestRefundDetail;
import vn.onepay.transaction.dto.RequestRefundResponse;

public interface RequestRefundService {
    RequestRefundDetail getRefundRequestDetail(String docId);
    Mono<ApiResponse<PagedResponse<RequestRefundResponse>>> getRefundRequests(ServerWebExchange exchange,String fromDate, String toDate,
            String merchantId,String status,String searchKeyword ,int page, int size,
            String sortField, String sortOrder);
}