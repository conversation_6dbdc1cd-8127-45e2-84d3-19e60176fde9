package vn.onepay.transaction.service;

import reactor.core.publisher.Mono;
import vn.onepay.transaction.dto.ApiResponse;
import vn.onepay.transaction.dto.FileDownloadDto;
import vn.onepay.transaction.dto.PagedResponse;
import vn.onepay.transaction.dto.RequestRefundDetail;
import vn.onepay.transaction.dto.RequestRefundResponse;
import vn.onepay.transaction.entity.DownloadTaskEntity;

public interface RequestRefundService {
    RequestRefundDetail getRefundRequestDetail(String docId);

    Mono<ApiResponse<PagedResponse<RequestRefundResponse>>> getRefundRequests(String fromDate, String toDate,
            String merchantId, String status, String searchKeyword, int page, int size,
            String sortField, String sortOrder);

    FileDownloadDto createExcel(DownloadTaskEntity task);
}