package vn.onepay.transaction.service;

import org.springframework.web.server.ServerWebExchange;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import vn.onepay.transaction.constant.IntervalType;
import vn.onepay.transaction.dto.DashboardSummaryDTO;
import vn.onepay.transaction.dto.RevenueMerchantDTO;
import vn.onepay.transaction.dto.RevenueTrendDTO;

public interface DashboardService {
    Mono<DashboardSummaryDTO>  getSummary(ServerWebExchange exchange,String fromDate, String toDate,String currency,String merchantId);
    Flux<RevenueTrendDTO> getRevenueTrend(ServerWebExchange exchange,String fromDate, String toDate, String currency, String merchantId, IntervalType intervalType);
    Flux<RevenueMerchantDTO>  getRevenueByMerhantId(ServerWebExchange exchange,String fromDate, String toDate,String currency,String merchantId);
}
