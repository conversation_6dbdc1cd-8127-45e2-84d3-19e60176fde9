package vn.onepay.transaction.service;

import org.springframework.web.server.ServerWebExchange;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import vn.onepay.transaction.constant.IntervalType;
import vn.onepay.transaction.dto.DashboardSummaryDTO;
import vn.onepay.transaction.dto.RevenuePercentDTO;
import vn.onepay.transaction.dto.RevenueTrendDTO;

public interface DashboardService {
    Mono<DashboardSummaryDTO>  getSummary(String fromDate, String toDate,String currency,String merchantId,Boolean isPromotion,String paymentMethod,String orderSource);
    Flux<RevenueTrendDTO> getRevenueTrend(String fromDate, String toDate, String currency, String merchantId, IntervalType intervalType,Boolean isPromotion,String paymentMethod,String orderSource);
    Flux<RevenuePercentDTO>  getRevenueGroupBy(String fromDate, String toDate,String currency,String merchantId,Boolean isPromotion,String paymentMethod,String orderSource,String groupBy);
}
