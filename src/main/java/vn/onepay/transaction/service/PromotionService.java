package vn.onepay.transaction.service;

import reactor.core.publisher.Mono;
import vn.onepay.transaction.dto.ApiResponse;
import vn.onepay.transaction.dto.FileDownloadDto;
import vn.onepay.transaction.dto.PromotionReportResponse;
import vn.onepay.transaction.dto.ReportResponse;
import vn.onepay.transaction.entity.DownloadTaskEntity;

public interface PromotionService {
    Mono<ApiResponse<ReportResponse<PromotionReportResponse>>> getPromotionReport(
            String fromDate, String toDate, String promotionCode, String paymentMethod,
            String merchantId, String interval, String currency, int page, int size);

    Mono<Void> insertDailyPromotionReport(String fromDateStr, String toDateStr);
    FileDownloadDto createExcel(DownloadTaskEntity task);
    FileDownloadDto createReportExcel(DownloadTaskEntity task);
}
