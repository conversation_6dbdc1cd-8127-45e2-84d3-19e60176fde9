package vn.onepay.transaction.service;


import java.util.List;
import java.util.Map;

import reactor.core.publisher.Mono;
import vn.onepay.transaction.dto.ActionMessage;
import vn.onepay.transaction.dto.ApiResponse;
import vn.onepay.transaction.dto.PagedResponse;
import vn.onepay.transaction.dto.RequestRefundResponse;
import vn.onepay.transaction.dto.TransactionDTO;
import vn.onepay.transaction.dto.TransactionDetailResponse;
import vn.onepay.transaction.dto.TransactionHistoryDTO;
import vn.onepay.transaction.dto.UposListResponse;

public interface TransactionOldService {
    Mono<String> checkUserExistsInPostgres(String userId);
    Mono<ApiResponse<PagedResponse<TransactionDTO>>> callSearchGeneralReport(String fromDate, String toDate, String merchantId,String transId,String paymentMethod,String transactionStatus, String transactionType, int page, int size, String sortField,String sortOrder,String userId);
    TransactionDetailResponse getTransactionDetail(String transactionId, String paymentMethod, String transactionType, String paymentChannel, String xUserId);
    Mono<Boolean> checkMerchantIdByUserId(String userId, String merchantTransactionDetail, String paymentMethod);
    Mono<Map<String, String>> refundTransaction(String transactionId, String paymentMethod, String jsonData, String userId, String realIp);
    Mono<Map<String, String>> approveOrRejectTransaction(String transactionId, String paymentMethod, String jsonData, String userId, String realIp);
    Mono<ApiResponse<PagedResponse<RequestRefundResponse>>> getRefundRequestsOld(String fromDate, String toDate,
            String merchantId, String status, String searchKeyword, int page, int size,
            String sortField, String sortOrder, String userId);
    List<TransactionHistoryDTO> getTransactionHistory(String originalTransactionId, String paymentMethod, String userId);
    Mono<ApiResponse<PagedResponse<UposListResponse>>> getUposTransactions(String fromDate,
            String toDate, String merchantId, String tid, String transactionId, String orderReference,
            String merchantTransRef, String cardNumber, String approvalCode, String paymentChannel, String cardType,
            String transactionType, String transactionState, String installmentStatus, int page, int size, String xUserId);
    Mono<Map<String, String>> captureTransaction(String transactionId, String merchantId, String paymentMethod, String jsonData, String userId, String realIp);
    Mono<Map<String, String>> voidTransaction(String transactionId,  String merchantId, String paymentMethod, String jsonData, String userId, String realIp);
    Map<String, ActionMessage> checkPerAndActionByUserId(TransactionDetailResponse detail, String userId);
}
