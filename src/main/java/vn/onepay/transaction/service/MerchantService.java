package vn.onepay.transaction.service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.r2dbc.core.DatabaseClient;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class MerchantService {

    private final DatabaseClient db2DatabaseClient;

    public MerchantService(@Qualifier("db2DatabaseClient") DatabaseClient db2DatabaseClient) {
        this.db2DatabaseClient = db2DatabaseClient;
    }

    public Mono<List<String>> getMerchantIdsByPartnerId(ServerWebExchange exchange) {
        String partnerIdHeader = exchange.getRequest().getHeaders().getFirst("X-Partner-Id");
        if (!StringUtils.hasText(partnerIdHeader)) {
            return Mono.error(new IllegalArgumentException("Missing required header: X-Partner-Id"));
        }

        Long partnerId = Long.valueOf(partnerIdHeader);
        String sql = "SELECT merchant_id FROM raw_merchants WHERE partner_id = $1";

        return db2DatabaseClient.sql(sql)
                .bind(0, partnerId)
                .map(row -> row.get("merchant_id", String.class))
                .all()
                .filter(Objects::nonNull)
                .collectList();
    }

    /**
     * Lấy danh sách merchantId sau khi lọc theo đối tác và giá trị filter (nếu có).
     *
     * @param exchange     ServerWebExchange để lấy header X-Partner-Id
     * @param merchantId   giá trị query param (?merchantId=...)
     * @return danh sách merchantId sau khi lọc
     */
    public Mono<List<String>> resolveMerchantIdFilter(ServerWebExchange exchange, String merchantId) {
        return getMerchantIdsByPartnerId(exchange)
                .map(merchantIdListByPartner -> {
                    log.info("Input merchantId param: {}", merchantId);
                    log.info("merchantIdListByPartner from DB: {}", merchantIdListByPartner);

                    if (!StringUtils.hasText(merchantId) || "ALL".equalsIgnoreCase(merchantId)) {
                        return merchantIdListByPartner;
                    }

                    List<String> merchantIdInput = Arrays.stream(merchantId.split(","))
                            .map(String::trim)
                            .filter(s -> !s.isEmpty())
                            .toList();

                    log.info("Parsed merchantId input list: {}", merchantIdInput);

                    List<String> filteredList = merchantIdListByPartner.stream()
                            .filter(merchantIdInput::contains)
                            .toList();

                    log.info("Filtered merchantId list after matching: {}", filteredList);

                    return filteredList;
                });
    }
}
