package vn.onepay.transaction.service;

import java.util.Arrays;
import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.server.ResponseStatusException;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import vn.onepay.transaction.dto.Merchant;
import vn.onepay.transaction.dto.MerchantDTO;
import vn.onepay.transaction.dto.Permission;
import vn.onepay.transaction.dto.PermissionDTO;

@Slf4j
@Service
public class MerchantService {

    private final WebClient webClient;
    private final String baseUrl;
    private final String merchantByUserPath;

    public MerchantService(
            WebClient.Builder webClientBuilder,
            @Value("${permission.base-url}") String baseUrl,
            @Value("${permission.path.merchants-by-user}") String merchantByUserPath) {
        this.baseUrl = baseUrl;
        this.merchantByUserPath = merchantByUserPath;
        this.webClient = webClientBuilder.baseUrl(baseUrl).build();
    }

    public Mono<List<String>> getMerchantIdsByPartnerId() {
        return Mono.deferContextual(ctx -> {
            if (!ctx.hasKey("partnerId") || !ctx.hasKey("userId")) {
                return Mono.error(new IllegalStateException("Missing partnerId or userId in context"));
            }
    
            Long partnerId = ctx.get("partnerId");
            String userId = ctx.get("userId");
    
            return webClient.get()
                    .uri(uriBuilder -> uriBuilder
                            .path("/partner/"+partnerId+"/user/"+userId+"/merchants")
                            .build())
                    .retrieve()
                    .bodyToMono(MerchantDTO.class)
                    .flatMap(dto -> {
                        if (dto.getTotal() == 0) {
                            return Mono.error(new ResponseStatusException(HttpStatus.FORBIDDEN, "User has no merchant access"));
                        }
    
                        List<Merchant> list = dto.getList();
                        if (list == null) {
                            return Mono.just(List.of());
                        }
    
                        return Mono.just(list.stream()
                                .map(Merchant::getId)
                                .toList());
                    });
        });
    }

    public Mono<List<String>> getPermissionByPartnerIdAndUserId() {
        return Mono.deferContextual(ctx -> {
            if (!ctx.hasKey("partnerId") || !ctx.hasKey("userId")) {
                return Mono.error(new IllegalStateException("Missing partnerId or userId in context"));
            }
    
            Long partnerId = ctx.get("partnerId");
            String userId = ctx.get("userId");

            return webClient.get()
                    .uri(uriBuilder -> uriBuilder
                            .path("/auth/user/"+userId+"/permissions")
                            .queryParam("partnerId", partnerId)
                            .build())
                    .retrieve()
                    .bodyToMono(PermissionDTO.class)
                    .flatMap(dto -> {
                        List<Permission> list = dto.getList();
                        if (list == null) {
                            return Mono.just(List.of());
                        }

                        return Mono.just(list.stream()
                                .map(Permission::getId)
                                .toList());
                    });
        });
    }

    /**
     * Lấy danh sách merchantId sau khi lọc theo đối tác và giá trị filter (nếu có)
     * 
     * @param merchantId giá trị query param (?merchantId=...)
     * @return danh sách merchantId sau khi lọc
     */
    public Mono<List<String>> resolveMerchantIdFilter(String merchantId) {
        return getMerchantIdsByPartnerId()
                .map(merchantIdListByPartner -> {
                    log.info("Input merchantId param: {}", merchantId);
                    log.info("list merchant from permission: {}", merchantIdListByPartner);
    
                    if (!StringUtils.hasText(merchantId) || "ALL".equalsIgnoreCase(merchantId)) {
                        return merchantIdListByPartner;
                    }
    
                    List<String> merchantIdInput = Arrays.stream(merchantId.split(","))
                            .map(String::trim)
                            .filter(s -> !s.isEmpty())
                            .toList();
    
                    log.info("Parsed merchantId input list: {}", merchantIdInput);
    
                    List<String> filteredList = merchantIdListByPartner.stream()
                            .filter(merchantIdInput::contains)
                            .toList();
    
                    log.info("Filtered merchantId list after matching: {}", filteredList);
    
                    return filteredList;
                });
    }
    
}
