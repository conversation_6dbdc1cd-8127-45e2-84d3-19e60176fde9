package vn.onepay.transaction.service;

import java.util.List;
import reactor.core.publisher.Mono;
import vn.onepay.transaction.dto.ApiResponse;
import vn.onepay.transaction.dto.FileDownloadDto;
import vn.onepay.transaction.dto.PagedResponse;
import vn.onepay.transaction.dto.TransactionDTO;
import vn.onepay.transaction.dto.TransactionDetailResponse;
import vn.onepay.transaction.dto.TransactionHistoryDTO;
import vn.onepay.transaction.dto.TransactionPromotionDTO;
import vn.onepay.transaction.dto.TransactionPromotionDetailResponse;
import vn.onepay.transaction.entity.DownloadTaskEntity;

public interface TransactionService {
        Mono<TransactionDetailResponse> getTransactionDetail(String docId);

        Mono<ApiResponse<PagedResponse<TransactionDTO>>> getTransactions(String fromDate,
                        String toDate, String merchantId,String transId, String paymentMethod, String transactionStatus, String transactionType,
                        String searchType,String searchKeyword, int page, int size, String sortField,
                        String sortOrder, String promotion, String orderSource);

        Mono<ApiResponse<List<TransactionHistoryDTO>>> getTransactionHistory(String transactionId,String type);

        FileDownloadDto createExcel(DownloadTaskEntity task);

        Mono<ApiResponse<PagedResponse<TransactionPromotionDTO>>> getTransactionsPromotion(
                        String fromDate, String toDate, String merchantId,
                        String transId, String merTransactionId, String cardNumber, String authCode, String promoCode,
                        String promoName, String paymentMethod, String transactionType, String transactionStatus,
                        int page,int size, String sortField,String sortOrder,String orderInfo);

        Mono<TransactionPromotionDetailResponse> getTransactionPromotionDetail(String docId);

}
