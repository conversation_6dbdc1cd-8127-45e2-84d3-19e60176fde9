package vn.onepay.transaction.service;

import java.util.List;

import org.springframework.web.server.ServerWebExchange;

import reactor.core.publisher.Mono;
import vn.onepay.transaction.dto.ApiResponse;
import vn.onepay.transaction.dto.PagedResponse;
import vn.onepay.transaction.dto.TransactionDTO;
import vn.onepay.transaction.dto.TransactionDetailResponse;
import vn.onepay.transaction.dto.TransactionHistoryDTO;
import vn.onepay.transaction.dto.TransactionPromotionDTO;
import vn.onepay.transaction.dto.TransactionPromotionDetailResponse;
import vn.onepay.transaction.entity.DownloadTaskEntity;

public interface TransactionService {
        TransactionDetailResponse getTransactionDetail(String docId);

        Mono<ApiResponse<PagedResponse<TransactionDTO>>> getTransactions(ServerWebExchange exchange, String fromDate,
                        String toDate, String merchantId,
                        String transId, String paymentMethod, String transactionStatus, String transactionType,
                        String searchType,
                        String searchKeyword, int page, int size, String sortField,
                        String sortOrder, Boolean isPromotion);

        List<TransactionHistoryDTO> getTransactionHistory(String transactionId);

        String createExcel(DownloadTaskEntity task);

        Mono<ApiResponse<PagedResponse<TransactionPromotionDTO>>> getTransactionsPromotion(ServerWebExchange exchange,
                        String fromDate, String toDate, String merchantId,
                        String transId, String merTransactionId, String cardNumber, String authCode, String promoCode,
                        String promoName, String paymentMethod, String transactionType, String transactionStatus,
                        int page,
                        int size, String sortField,
                        String sortOrder);

        TransactionPromotionDetailResponse getTransactionPromotionDetail(String docId);

}
