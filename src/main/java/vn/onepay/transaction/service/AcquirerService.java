package vn.onepay.transaction.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import vn.onepay.transaction.dto.AcquirerDTO;
import vn.onepay.transaction.repository.AcquirerRepository;

@Service
public class AcquirerService {
     @Autowired
    private AcquirerRepository acquirerRepository;

    @Cacheable("acquirerCache")
    public AcquirerDTO getAllAcquirers() {
        System.out.println("Querying DB for acquirers...");
        return acquirerRepository.getListAcquirer();
    }

    @CacheEvict(value = "acquirerCache", allEntries = true)
    public void clearAcquirerCache() {
        System.out.println("Cache cleared!");
    }
}
