package vn.onepay.transaction.job;

import java.util.TimeZone;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.atomic.AtomicReference;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.scheduler.Schedulers;
import vn.onepay.transaction.service.TransactionReportService;

@Slf4j
@Component
@RequiredArgsConstructor
public class TransactionReportJob {
    @Value("${transaction-report.job.enabled:true}")
    private String enabled;

    @Value("${transaction-report.job.cron:0 0 6 * * *}")
    private String cron;

    private final TransactionReportService service;

    private final AtomicReference<ScheduledFuture<?>> scheduledFutureRef = new AtomicReference<>();
    private final TaskScheduler taskScheduler = threadPoolScheduler();

    @PostConstruct
    public void schedule() {
        if (!enabled.equals("true")) {
            log.info("TransactionReportJob is disabled by config.");
            return;
        }

        log.info("Scheduling TransactionReportJob with cron: {}", cron);
        CronTrigger cronTrigger = new CronTrigger(cron, TimeZone.getTimeZone("Asia/Ho_Chi_Minh"));

        scheduledFutureRef.set(taskScheduler.schedule(() -> {
            log.info("⚙️ Running TransactionReportJob...");
            service.insertDailyTransactionReport()
                    .doOnError(e -> log.error("Error inserting transaction report", e))
                    .subscribeOn(Schedulers.boundedElastic())
                    .subscribe();
        }, cronTrigger));

        log.info("End TransactionReportJob!");
    }

    private ThreadPoolTaskScheduler threadPoolScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(1);
        scheduler.setThreadNamePrefix("transaction-report-job-");
        scheduler.initialize();
        return scheduler;
    }
}
