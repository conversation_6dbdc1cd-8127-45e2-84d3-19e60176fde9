package vn.onepay.transaction.job;

import java.time.LocalDate;
import java.util.TimeZone;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.atomic.AtomicReference;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.scheduler.Schedulers;
import vn.onepay.transaction.service.PromotionService;

@Slf4j
@Component
@RequiredArgsConstructor
public class PromotionReportJob {
    @Value("${promotion-report.job.enabled:true}")
    private String enabled;

    @Value("${promotion-report.job.cron:0 0 6 * * *}")
    private String cron;

    private final PromotionService service;

    private final AtomicReference<ScheduledFuture<?>> scheduledFutureRef = new AtomicReference<>();
    private final TaskScheduler taskScheduler = threadPoolScheduler();

    @PostConstruct
    public void schedule() {
        if (!enabled.equals("true")) {
            log.info("PromotionReportJob is disabled by config.");
            return;
        }

        log.info("Scheduling PromotionReportJob with cron: {}", cron);
        CronTrigger cronTrigger = new CronTrigger(cron, TimeZone.getTimeZone("Asia/Ho_Chi_Minh"));
        String from = LocalDate.now().minusDays(1).toString();
        String to = LocalDate.now().minusDays(1).toString();
        scheduledFutureRef.set(taskScheduler.schedule(() -> {
            log.info("⚙️ Running PromotionReportJob...");
            service.insertDailyPromotionReport(from,to)
                    .doOnError(e -> log.error("Error inserting Promotion report", e))
                    .subscribeOn(Schedulers.boundedElastic())
                    .subscribe();
        }, cronTrigger));

        log.info("End PromotionReportJob!");
    }

    private ThreadPoolTaskScheduler threadPoolScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(1);
        scheduler.setThreadNamePrefix("Promotion-report-job-");
        scheduler.initialize();
        return scheduler;
    }
}

