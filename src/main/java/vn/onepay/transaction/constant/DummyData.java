package vn.onepay.transaction.constant;

import java.time.Instant;
import java.util.*;

import com.fasterxml.jackson.annotation.JsonInclude;

import vn.onepay.transaction.constant.DummyData.PermissionDTO;

public class DummyData {
    // Role list
    public static final List<Map<String, Object>> ROLES = new ArrayList<>();
    // Function list
    public static final List<Map<String, Object>> FUNCTIONS = new ArrayList<>();
    // Role -> Functions mapping
    public static final Map<String, List<Map<String, Object>>> ROLE_FUNCTIONS = new HashMap<>();
    // Ecosystem apps
    public static final List<String> ECO_APPS = List.of("Payment", "Report", "Admin");
    // User list
    public static final List<Map<String, Object>> USERS = new ArrayList<>();
    public static final Map<String, List<Map<String, Object>>> ROLE_DETAILS = new HashMap<>();

    public record EcoAppDTO(String id, String value, List<ModuleDTO> modules) {
    }
    public record Roles(String id, String name) {
    }
    public record ModuleDTO(String id, String value, List<PermissionDTO> permissions) {
    }

    public record PermissionDTO(String id, String value) {
    }
@JsonInclude(JsonInclude.Include.NON_NULL)
    public record UserDTO(
            String userName,
            String fullName,
            String email,
            String phone,
            String role,
            List<String> merchantIds,
            Boolean isActive,
            Instant createdAt,
            Instant lastLogin,
            List<EcoAppDTO> functions,
            List<Roles> roles) {
    }

    public record RoleDTO(
            String id,
            String code,
            String name,
            String description) {
    }

    public record EcosystemDTO(String id, String value, List<ModuleDTO> modules) {
    }

    public static List<UserDTO> userList() {
        Roles staffRole = new Roles("r1", "Nhân viên");
        Roles technicianRole = new Roles("r2", "Kỹ thuật");
        Roles financeRole = new Roles("r3", "Tài chính/Kế toán");
        Roles managerRole = new Roles("r4", "Quản lý");
    
        return List.of(
            new UserDTO("nguyenvan.a", "Nguyễn Văn A", "<EMAIL>", "0909123456",
                    "staff", List.of("mch_001", "mch_005"), true,
                    Instant.parse("2024-12-01T08:30:00Z"), Instant.parse("2025-05-19T14:45:00Z"),
                    null,
                    List.of(staffRole)),
    
            new UserDTO("tranthib", "Trần Thị B", "<EMAIL>", "0911222333",
                    "technician", List.of("mch_002"), false,
                    Instant.parse("2024-11-10T10:15:00Z"), null,
                    null,
                    List.of(technicianRole)),
    
            new UserDTO("lehoangc", "Lê Hoàng C", "<EMAIL>", "0933444555",
                    "finance", List.of("mch_001", "mch_003"), true,
                    Instant.parse("2025-01-05T09:00:00Z"), Instant.parse("2025-05-18T17:20:00Z"),
                    List.of(transactionApp(), payCollectApp()),
                    null),
    
            new UserDTO("phamminhd", "Phạm Minh D", "<EMAIL>", "0988777666",
                    "manager", List.of(), true,
                    Instant.parse("2024-10-20T13:00:00Z"), Instant.parse("2025-05-19T09:10:00Z"),
                    null,
                    List.of(managerRole)),
    
            new UserDTO("customuser", "Người Dùng Tùy Chỉnh", "<EMAIL>", "0911222444",
                    "staff", List.of("mch_009"), true,
                    Instant.parse("2025-03-01T09:00:00Z"), Instant.parse("2025-05-20T08:00:00Z"),
                    List.of(transactionApp(), payCollectApp()),
                    null)
        );
    }
    
    

    public static EcoAppDTO transactionApp() {
        return new EcoAppDTO(
                "eco_tx", "transaction",
                List.of(
                        new ModuleDTO("mod_tx_dash", "dashboard", List.of(
                                new PermissionDTO("perm_tx_dash_revenue", "view_dashboard_revenue"),
                                new PermissionDTO("perm_tx_dash_service", "view_dashboard_service"))),
                        new ModuleDTO("mod_tx_list", "list", List.of(
                                new PermissionDTO("perm_tx_list_view", "view_transaction_list"),
                                new PermissionDTO("perm_tx_list_filter", "filter_transaction"),
                                new PermissionDTO("perm_tx_list_export", "export_transaction_list"))),
                        new ModuleDTO("mod_tx_report", "report", List.of(
                                new PermissionDTO("perm_tx_report_view", "view_transaction_report"),
                                new PermissionDTO("perm_tx_report_export", "export_transaction_report")))));
    }

    public static EcoAppDTO payCollectApp() {
        return new EcoAppDTO(
                "eco_pc", "paycollect",
                List.of(
                        new ModuleDTO("mod_pc_dash", "dashboard", List.of(
                                new PermissionDTO("perm_pc_dash_revenue", "view_dashboard_revenue"),
                                new PermissionDTO("perm_pc_dash_collection", "view_dashboard_collection"))),
                        new ModuleDTO("mod_pc_list", "list", List.of(
                                new PermissionDTO("perm_pc_list_view", "view_paycollect_list"),
                                new PermissionDTO("perm_pc_list_filter", "filter_paycollect"),
                                new PermissionDTO("perm_pc_list_export", "export_paycollect_list"))),
                        new ModuleDTO("mod_pc_report", "report", List.of(
                                new PermissionDTO("perm_pc_report_view", "view_paycollect_report"),
                                new PermissionDTO("perm_pc_report_export", "export_paycollect_report")))));
    }

    static {
        // roles
        ROLES.add(Map.of("id", "admin", "name", "Administrator"));
        ROLES.add(Map.of("id", "user", "name", "User"));

        // functions
        Map<String, Object> f1 = Map.of("code", "dashboard:view", "description", "View dashboard");
        Map<String, Object> f2 = Map.of("code", "report:view", "description", "View reports");
        Map<String, Object> f3 = Map.of("code", "user:manage", "description", "Manage users");
        FUNCTIONS.addAll(List.of(f1, f2, f3));

        // role -> functions mapping
        ROLE_FUNCTIONS.put("admin", List.of(f1, f2, f3));
        ROLE_FUNCTIONS.put("user", List.of(f1));

        // users
        USERS.add(Map.of("email", "<EMAIL>", "fullName", "John Doe", "roles", List.of("admin")));
        USERS.add(Map.of("email", "<EMAIL>", "fullName", "Jane Smith", "roles", List.of("user")));

        List<Map<String, Object>> ecoTxModules = List.of(
                Map.of(
                        "id", "mod_tx_dash",
                        "value", "dashboard",
                        "permissions", List.of(
                                Map.of("id", "perm_tx_dash_revenue", "value", "view_dashboard_revenue"),
                                Map.of("id", "perm_tx_dash_service", "value", "view_dashboard_service"))),
                Map.of(
                        "id", "mod_tx_list",
                        "value", "list",
                        "permissions", List.of(
                                Map.of("id", "perm_tx_list_view", "value", "view_transaction_list"),
                                Map.of("id", "perm_tx_list_filter", "value", "filter_transaction"),
                                Map.of("id", "perm_tx_list_export", "value", "export_transaction_list"))),
                Map.of(
                        "id", "mod_tx_report",
                        "value", "report",
                        "permissions", List.of(
                                Map.of("id", "perm_tx_report_view", "value", "view_transaction_report"),
                                Map.of("id", "perm_tx_report_export", "value", "export_transaction_report"))));

        List<Map<String, Object>> ecoPcModules = List.of(
                Map.of(
                        "id", "mod_pc_dash",
                        "value", "dashboard",
                        "permissions", List.of(
                                Map.of("id", "perm_pc_dash_revenue", "value", "view_dashboard_revenue"),
                                Map.of("id", "perm_pc_dash_collection", "value", "view_dashboard_collection"))),
                Map.of(
                        "id", "mod_pc_list",
                        "value", "list",
                        "permissions", List.of(
                                Map.of("id", "perm_pc_list_view", "value", "view_paycollect_list"),
                                Map.of("id", "perm_pc_list_filter", "value", "filter_paycollect"),
                                Map.of("id", "perm_pc_list_export", "value", "export_paycollect_list"))),
                Map.of(
                        "id", "mod_pc_report",
                        "value", "report",
                        "permissions", List.of(
                                Map.of("id", "perm_pc_report_view", "value", "view_paycollect_report"),
                                Map.of("id", "perm_pc_report_export", "value", "export_paycollect_report"))));

        ROLE_DETAILS.put("admin", List.of(
                Map.of("id", "eco_tx", "value", "transaction", "modules", ecoTxModules),
                Map.of("id", "eco_pc", "value", "paycollect", "modules", ecoPcModules)));
    }
}
