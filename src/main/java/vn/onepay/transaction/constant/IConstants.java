package vn.onepay.transaction.constant;

import java.util.List;
import java.util.Map;
import java.util.Set;

public class IConstants {
        public static final String PAYMENT_METHOD_INTERNATIONAL = "International";
        public static final String PAYMENT_METHOD_INSTALLMENT = "Installment";
        public static final String PAYMENT_METHOD_DOMESTIC = "Domestic";
        public static final String PAYMENT_METHOD_SS_PAY = "Samsung Pay";
        public static final String PAYMENT_METHOD_GG_PAY = "Google Pay";
        public static final String PAYMENT_METHOD_AP_PAY = "Apple Pay";

        public static final String DOWNLOAD_TASK_TOPIC = "download-task-topic";

        // Type
        public static final String TYPE_TRANSACTION_REPORT = "TRANSACTION_REPORT";
        public static final String TYPE_TRANSACTION = "TRANSACTION";
        public static final String TYPE_REQUEST_REFUND = "REQUEST_REFUND";
        public static final String TYPE_PROMOTION = "PROMOTION";
        public static final String TYPE_PROMOTION_REPORT = "PROMOTION_REPORT";
        public static final String TYPE_UPOS = "UPOS";
        public static final String TYPE_UPOS_REPORT = "UPOS_REPORT";
        // Status
        public static final String STATUS_PENDING = "PENDING";
        public static final String STATUS_DONE = "DONE";
        public static final String STATUS_FAILED = "FAILED";

        public static final Set<Integer> ALLOWED_SIZES = Set.of(20, 50, 100, 200, 500);
        
        public static final Map<String, String> PAYMENT_METHOD_MAP = Map.of(
                "International", "QT",
                "Domestic",      "ND",
                "Mobile App",    "QR",
                "VietQR",        "VIETQR",
                "BNPL",          "BNPL",
                "Direct Debit",  "DD",
                
                "INT",           "QT",
                "DOM",           "ND",
                "DIRECT-DEBIT",  "DD"
        );

        // Config trường trả ra từ opensearch
        public static final List<String> SOURCE_FIELDS_TRANSACTION = List.of(
                        "msp_merchant.s_id", "enrich_txn.s_base_id", "enrich_txn.s_id", "enrich_txn.s_txn_ref",
                        "msp_payment.s_e_card_number", "enrich_txn.s_txn_type", "enrich_txn.s_response_code",
                        "enrich_txn.s_e_response_code", "enrich_txn.s_state", "msp_payment.s_e_gate_label",
                        "msp_invoice.s_currencies", "msp_payment.s_e_pay_method", "msp_payment.s_e_source",
                        "msp_invoice.n_amount", "enrich_txn.d_original_date", "enrich_txn.d_create",
                        "enrich_txn.d_update", "msp_invoice.s_e_order_source","msp_invoice.s_info");

        public static final List<String> SOURCE_FIELDS_REQUEST_REFUND = List.of(
                        "msp_merchant.s_id", "enrich_txn.s_id", "enrich_txn.s_response_code", "enrich_txn.s_state",
                        "msp_invoice.s_currencies", "msp_payment.s_e_source", "enrich_txn.d_create",
                        "enrich_txn.n_amount", "msp_refund_request.s_parent_id","msp_invoice.s_info");
        public static final List<String> SOURCE_FIELDS_PROMOTION = List.of(
                        "msp_merchant.s_id",
                        "enrich_txn.s_base_id",
                        "enrich_txn.s_id",
                        "enrich_txn.s_txn_ref",
                        "msp_payment.s_e_card_number",
                        "enrich_txn.s_txn_type",
                        "enrich_txn.s_response_code",
                        "enrich_txn.s_e_response_code",
                        "enrich_txn.s_state",
                        "msp_invoice.s_currencies",
                        "onepr_pr.s_id",
                        "onepr_pr.s_name",
                        "msp_payment.s_e_pay_method",
                        "enrich_txn.n_original_amount",
                        "msp_payment.n_amount",
                        "enrich_txn.n_original_amount",
                        "enrich_txn.d_create","msp_invoice.s_info");
        public static String baseUposConditionJson = "{\n" + //
                                "    \"size\": 0,\n" + //
                                "    \"query\": {\n" + //
                                "        \"bool\": {\n" + //
                                "            \"filter\": [\n" + //
                                "                {\n" + //
                                "                    \"range\": {\n" + //
                                "                        \"enrich_txn.d_create\": {\n" + //
                                "                            \"gte\": \"%s\",\r\n" + //
                                "                            \"lt\": \"%s\",\r\n" + //
                                "                            \"time_zone\": \"+07:00\"\n" + //
                                "                        }\n" + //
                                "                    }\n" + //
                                "                },\n" + //
                                "                {\n" + //
                                "                    \"terms\": {\n" + //
                                "                        \"msp_payment.s_e_merchant_channel.keyword\": [\n" + //
                                "                            \"UPOS\"\n" + //
                                "                        ]\n" + //
                                "                    }\n" + //
                                "                },\n" + //
                                "                {\n" + //
                                "                    \"wildcard\": {\n" + //
                                "                        \"enrich_txn.s_state.keyword\": \"Successful\"\n" + //
                                "                    }\n" + //
                                "                }\n" + //
                                "            ],\n" + //
                                "            \"must_not\": [\n" + //
                                "                {\n" + //
                                "                    \"term\": {\n" + //
                                "                        \"b_has_payment\": true\n" + //
                                "                    }\n" + //
                                "                },\n" + //
                                "                {\n" + //
                                "                    \"term\": {\n" + //
                                "                        \"enrich_txn.s_base_txn_type.keyword\": \"invoice\"\n" + //
                                "                    }\n" + //
                                "                },\n" + //
                                "                {\n" + //
                                "                    \"terms\": {\n" + //
                                "                        \"enrich_txn.s_txn_type.keyword\": [\n" + //
                                "                            \"Refund Request\",\n" + //
                                "                            \"Request Refund\",\n" + //
                                "                            \"\"\n" + //
                                "                        ]\n" + //
                                "                    }\n" + //
                                "                }\n" + //
                                "            ]\n" + //
                                "        }\n" + //
                                "    },\n" + //
                                "    \"aggs\": {\n" + //
                                "        \"byComposite\": {\n" + //
                                "            \"composite\": {\n" + //
                                "                \"size\": 10000,\n" + //
                                "                \"sources\": [\n" + //
                                "                    {\n" + //
                                "                        \"transactionDay\": {\n" + //
                                "                            \"date_histogram\": {\n" + //
                                "                                \"field\": \"enrich_txn.d_create\",\n" + //
                                "                                \"calendar_interval\": \"day\",\n" + //
                                "                                \"time_zone\": \"+07:00\"\n" + //
                                "                            }\n" + //
                                "                        }\n" + //
                                "                    },\n" + //
                                "                    {\n" + //
                                "                        \"merchantId\": {\n" + //
                                "                            \"terms\": {\n" + //
                                "                                \"field\": \"msp_merchant.s_id.keyword\"\n" + //
                                "                            }\n" + //
                                "                        }\n" + //
                                "                    },\n" + //
                                "                    {\n" + //
                                "                        \"currency\": {\n" + //
                                "                            \"terms\": {\n" + //
                                "                                \"field\": \"msp_payment.s_currency.keyword\"\n" + //
                                "                            }\n" + //
                                "                        }\n" + //
                                "                    },\n" + //
                                "                    {\n" + //
                                "                        \"txnType\": {\n" + //
                                "                            \"terms\": {\n" + //
                                "                                \"field\": \"enrich_txn.s_txn_type.keyword\"\n" + //
                                "                            }\n" + //
                                "                        }\n" + //
                                "                    },\n" + //
                                "                    {\n" + //
                                "                        \"paymentChannel\": {\n" + //
                                "                            \"terms\": {\n" + //
                                "                                \"script\": {\n" + //
                                "                                    \"source\": \"def method = doc['msp_payment.s_e_pay_method.keyword'].size() > 0 ? doc['msp_payment.s_e_pay_method.keyword'].value : null; if (method != 'CARD') return method; if (!doc.containsKey('msp_payment.s_ita_id.keyword') || doc['msp_payment.s_ita_id.keyword'].size() == 0) return 'CARD'; def ita = doc['msp_payment.s_ita_id.keyword'].value; return (ita == '') ? 'CARD' : 'Installment';\",\n" + //
                                "                                    \"lang\": \"painless\"\n" + //
                                "                                }\n" + //
                                "                            }\n" + //
                                "                        }\n" + //
                                "                    },\n" + //
                                "                    {\n" + //
                                "                        \"cardType\": {\n" + //
                                "                            \"terms\": {\n" + //
                                "                                \"field\": \"msp_payment.s_e_card.keyword\"\n" + //
                                "                            }\n" + //
                                "                        }\n" + //
                                "                    },\n" + //
                                "                    {\n" + //
                                "                        \"tid\": {\n" + //
                                "                            \"terms\": {\n" + //
                                "                                \"script\": {\n" + //
                                "                                    \"source\": \"def v = doc['msp_payment.s_data.tid.keyword'].size() == 0 ? '' : doc['msp_payment.s_data.tid.keyword'].value;\\n" + //
                                "return v == '' ? '' : v;\",\n" + //
                                "                                    \"lang\": \"painless\"\n" + //
                                "                                }\n" + //
                                "                            }\n" + //
                                "                        }\n" + //
                                "                    }\n" + //
                                "                ]\n" + //
                                "            },\n" + //
                                "            \"aggs\": {\n" + //
                                "                \"amount\": {\n" + //
                                "                    \"sum\": {\n" + //
                                "                        \"field\": \"msp_payment.n_amount\"\n" + //
                                "                    }\n" + //
                                "                }\n" + //
                                "            }\n" + //
                                "        }\n" + //
                                "    }\n" + //
                                "}";
        public static String basePromotionConditionJson = "{\r\n" + //
                        "    \"size\": 0,\r\n" + //
                        "    \"query\": {\r\n" + //
                        "        \"bool\": {\r\n" + //
                        "            \"filter\": [\r\n" + //
                        "                {\r\n" + //
                        "                    \"range\": {\r\n" + //
                        "                        \"enrich_txn.d_create\": {\r\n" + //
                        "                            \"gte\": \"%s\",\r\n" + //
                        "                            \"lt\": \"%s\",\r\n" + //
                        "                            \"time_zone\": \"+07:00\"\r\n" + //
                        "                        }\r\n" + //
                        "                    }\r\n" + //
                        "                },\r\n" + //
                        "                {\r\n" + //
                        "                    \"terms\": {\r\n" + //
                        "                        \"msp_payment.s_e_channel.keyword\": [\r\n" + //
                        "                            \"Promotion\"\r\n" + //
                        "                        ]\r\n" + //
                        "                    }\r\n" + //
                        "                },\r\n" + //
                        "                {\r\n" + //
                        "                    \"wildcard\": {\r\n" + //
                        "                        \"enrich_txn.s_state.keyword\": \"Successful\"\r\n" + //
                        "                    }\r\n" + //
                        "                }\r\n" + //
                        "            ],\r\n" + //
                        "            \"must_not\": [\r\n" + //
                        "                {\r\n" + //
                        "                    \"term\": {\r\n" + //
                        "                        \"b_has_payment\": true\r\n" + //
                        "                    }\r\n" + //
                        "                },\r\n" + //
                        "                {\r\n" + //
                        "                    \"term\": {\r\n" + //
                        "                        \"enrich_txn.s_base_txn_type.keyword\": \"invoice\"\r\n" + //
                        "                    }\r\n" + //
                        "                },\r\n" + //
                        "                {\r\n" + //
                        "                    \"terms\": {\r\n" + //
                        "                        \"enrich_txn.s_txn_type.keyword\": [\r\n" + //
                        "                            \"Refund Request\",\r\n" + //
                        "                            \"Request Refund\",\r\n" + //
                        "                            \"\"\r\n" + //
                        "                        ]\r\n" + //
                        "                    }\r\n" + //
                        "                }\r\n" + //
                        "            ]\r\n" + //
                        "        }\r\n" + //
                        "    },\r\n" + //
                        "    \"aggs\": {\r\n" + //
                        "        \"byComposite\": {\r\n" + //
                        "            \"composite\": {\r\n" + //
                        "                \"size\": 10000,\r\n" + //
                        "                \"sources\": [\r\n" + //
                        "                    {\r\n" + //
                        "                        \"transactionDay\": {\r\n" + //
                        "                            \"date_histogram\": {\r\n" + //
                        "                                \"field\": \"enrich_txn.d_create\",\r\n" + //
                        "                                \"calendar_interval\": \"day\",\r\n" + //
                        "                                \"time_zone\": \"+07:00\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    },\r\n" + //
                        "                    {\r\n" + //
                        "                        \"merchantId\": {\r\n" + //
                        "                            \"terms\": {\r\n" + //
                        "                                \"field\": \"msp_merchant.s_id.keyword\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    },\r\n" + //
                        "                    {\r\n" + //
                        "                        \"currency\": {\r\n" + //
                        "                            \"terms\": {\r\n" + //
                        "                                \"field\": \"msp_payment.s_currency.keyword\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    },\r\n" + //
                        "                    {\r\n" + //
                        "                        \"txnType\": {\r\n" + //
                        "                            \"terms\": {\r\n" + //
                        "                                \"field\": \"enrich_txn.s_txn_type.keyword\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    },\r\n" + //
                        "                    {\r\n" + //
                        "                        \"pr_id\": {\r\n" + //
                        "                            \"terms\": {\r\n" + //
                        "                                \"field\": \"onepr_pr.s_id.keyword\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    },\r\n" + //
                        "                    {\r\n" + //
                        "                        \"paymentMethod\": {\r\n" + //
                        "                            \"terms\": {\r\n" + //
                        "                                \"field\": \"msp_payment.s_e_pay_method.keyword\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    },\r\n" + //
                        "                    {\r\n" + //
                        "                        \"payGate\": {\r\n" + //
                        "                            \"terms\": {\r\n" + //
                        "                                \"field\": \"msp_payment.s_e_gate_label.keyword\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    },\r\n" + //
                        "                    {\r\n" + //
                        "                        \"cardType\": {\r\n" + //
                        "                            \"terms\": {\r\n" + //
                        "                                \"field\": \"msp_payment.s_e_card.keyword\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    }\r\n" + //
                        "                ]\r\n" + //
                        "            },\r\n" + //
                        "            \"aggs\": {\r\n" + //
                        "                \"totalVolume\": {\r\n" + //
                        "                    \"sum\": {\r\n" + //
                        "                        \"field\": \"msp_invoice.n_amount\"\r\n" + //
                        "                    }\r\n" + //
                        "                },\r\n" + //
                        "                \"totalPayment\": {\r\n" + //
                        "                    \"sum\": {\r\n" + //
                        "                        \"field\": \"msp_payment.n_amount\"\r\n" + //
                        "                    }\r\n" + //
                        "                }\r\n" + //
                        "            }\r\n" + //
                        "        }\r\n" + //
                        "    }\r\n" + //
                        "}";
        public static String baseConditionJson = "{\r\n" + //
                        "    \"size\": 0,\r\n" + //
                        "    \"query\": {\r\n" + //
                        "        \"bool\": {\r\n" + //
                        "            \"filter\": [\r\n" + //
                        "                {\r\n" + //
                        "                    \"range\": {\r\n" + //
                        "                        \"enrich_txn.d_create\": {\r\n" + //
                        "                            \"gte\": \"%s\",\r\n" + //
                        "                            \"lt\": \"%s\",\r\n" + //
                        "                            \"time_zone\": \"+07:00\"\r\n" + //
                        "                        }\r\n" + //
                        "                    }\r\n" + //
                        "                },\r\n" + //
                        "                {\r\n" + //
                        "                    \"wildcard\": {\r\n" + //
                        "                        \"enrich_txn.s_state.keyword\": \"Successful\"\r\n" + //
                        "                    }\r\n" + //
                        "                }\r\n" + //
                        "            ],\r\n" + //
                        "            \"must_not\": [\r\n" + //
                        "                {\r\n" + //
                        "                    \"term\": {\r\n" + //
                        "                        \"b_has_payment\": true\r\n" + //
                        "                    }\r\n" + //
                        "                },\r\n" + //
                        "                {\r\n" + //
                        "                    \"term\": {\r\n" + //
                        "                        \"enrich_txn.s_base_txn_type.keyword\": \"invoice\"\r\n" + //
                        "                    }\r\n" + //
                        "                },\r\n" + //
                        "                {\r\n" + //
                        "                    \"terms\": {\r\n" + //
                        "                        \"enrich_txn.s_txn_type.keyword\": [\r\n" + //
                        "                            \"Refund Request\",\r\n" + //
                        "                            \"Request Refund\",\r\n" + //
                        "                            \"\"\r\n" + //
                        "                        ]\r\n" + //
                        "                    }\r\n" + //
                        "                }\r\n" + //
                        "            ]\r\n" + //
                        "        }\r\n" + //
                        "    },\r\n" + //
                        "    \"aggs\": {\r\n" + //
                        "        \"byComposite\": {\r\n" + //
                        "            \"composite\": {\r\n" + //
                        "                \"size\": 10000,\r\n" + //
                        "                \"sources\": [\r\n" + //
                        "                    {\r\n" + //
                        "                        \"transactionDay\": {\r\n" + //
                        "                            \"date_histogram\": {\r\n" + //
                        "                                \"field\": \"enrich_txn.d_create\",\r\n" + //
                        "                                \"calendar_interval\": \"day\",\r\n" + //
                        "                                \"time_zone\": \"+07:00\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    },\r\n" + //
                        "                    {\r\n" + //
                        "                        \"merchantId\": {\r\n" + //
                        "                            \"terms\": {\r\n" + //
                        "                                \"field\": \"msp_merchant.s_id.keyword\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    },\r\n" + //
                        "                    {\r\n" + //
                        "                        \"currency\": {\r\n" + //
                        "                            \"terms\": {\r\n" + //
                        "                                \"field\": \"msp_payment.s_currency.keyword\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    },\r\n" + //
                        "                    {\r\n" + //
                        "                        \"txnType\": {\r\n" + //
                        "                            \"terms\": {\r\n" + //
                        "                                \"field\": \"enrich_txn.s_txn_type.keyword\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    },\r\n" + //
                        "                    {\r\n" + //
                        "                        \"paymentMethod\": {\r\n" + //
                        "                            \"terms\": {\r\n" + //
                        "                                \"field\": \"msp_payment.s_e_pay_method.keyword\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    },\r\n" + //
                        "                    {\r\n" + //
                        "                        \"orderSource\": {\r\n" + //
                        "                            \"terms\": {\r\n" + //
                        "                                \"field\": \"msp_invoice.s_e_order_source.keyword\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    },\r\n" + //
                        "                    {\r\n" + //
                        "                        \"isPromotion\": {\r\n" + //
                        "                            \"terms\": {\r\n" + //
                        "                                \"script\": {\r\n" + //
                        "                                    \"source\": \"def channel = doc['msp_payment.s_e_channel.keyword'].size() == 0 ? null : doc['msp_payment.s_e_channel.keyword'].value;\\n"
                        + //
                        "return channel == 'Promotion';\",\r\n" + //
                        "                                    \"lang\": \"painless\"\r\n" + //
                        "                                }\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    }\r\n" + //
                        "                ]\r\n" + //
                        "            },\r\n" + //
                        "            \"aggs\": {\r\n" + //
                        "                \"totalVolume\": {\r\n" + //
                        "                    \"sum\": {\r\n" + //
                        "                        \"field\": \"enrich_txn.n_amount\"\r\n" + //
                        "                    }\r\n" + //
                        "                }\r\n" + //
                        "            }\r\n" + //
                        "        }\r\n" + //
                        "    }\r\n" + //
                        "}";
}
