package vn.onepay.transaction.constant;

import java.util.List;

public class IConstants {
        public static final String PAYMENT_METHOD_INTERNATIONAL = "International";
        public static final String PAYMENT_METHOD_INSTALLMENT = "Installment";
        public static final String PAYMENT_METHOD_DOMESTIC = "Domestic";
        public static final String PAYMENT_METHOD_SS_PAY = "Samsung Pay";
        public static final String PAYMENT_METHOD_GG_PAY = "Google Pay";
        public static final String PAYMENT_METHOD_AP_PAY = "Apple Pay";

        public static final String DOWNLOAD_TASK_TOPIC = "download-task-topic";

        // Type dowload file
        public static final String TYPE_TRANSACTION_REPORT = "TRANSACTION_REPORT";
        public static final String TYPE_TRANSACTION = "TRANSACTION";
        public static final String TYPE_REQUEST_REFUND = "REQUEST_REFUND";
        public static final String TYPE_PROMOTION = "PROMOTION";
        // Status
        public static final String STATUS_PENDING = "PENDING";
        public static final String STATUS_DONE = "DONE";
        public static final String STATUS_FAILED = "FAILED";

        // Config trường trả ra từ opensearch
        public static final List<String> SOURCE_FIELDS_TRANSACTION = List.of(
                        "msp_merchant.s_id", "enrich_txn.s_base_id", "enrich_txn.s_id", "enrich_txn.s_txn_ref",
                        "msp_payment.s_e_card_number", "enrich_txn.s_txn_type", "enrich_txn.s_response_code",
                        "enrich_txn.s_e_response_code", "enrich_txn.s_state", "msp_payment.s_e_gate_label",
                        "msp_invoice.s_currencies", "msp_payment.s_ita_state", "msp_payment.s_e_source",
                        "msp_invoice.n_amount", "enrich_txn.d_original_date", "enrich_txn.d_create",
                        "enrich_txn.d_update");

        public static final List<String> SOURCE_FIELDS_REQUEST_REFUND = List.of(
                        "msp_merchant.s_id", "enrich_txn.s_id", "enrich_txn.s_response_code", "enrich_txn.s_state",
                        "msp_invoice.s_currencies", "msp_payment.s_e_source", "enrich_txn.d_create",
                        "enrich_txn.n_amount", "msp_refund_request.s_parent_id");
        public static String baseConditionJson = "{\n" +
                        "  \"size\": 0,\n" +
                        "  \"query\": {\n" +
                        "    \"bool\": {\n" +
                        "      \"filter\": [\n" +
                        "        {\n" +
                        "          \"range\": {\n" +
                        "            \"enrich_txn.d_create\": {\n" +
                        "              \"gte\": \"now-1d/d\",\n" +
                        "              \"lt\": \"now/d\",\n" +
                        "              \"time_zone\": \"+07:00\"\n" +
                        "            }\n" +
                        "          }\n" +
                        "        },\n" +
                        "        {\n" +
                        "          \"wildcard\": {\n" +
                        "            \"enrich_txn.s_state.keyword\": \"Successful\" \n" +
                        "          }\n" +
                        "        } \n" +
                        "      ],\n" +
                        "      \"must_not\": [\n" +
                        "        { \"term\": { \"b_has_payment\": true } },\n" +
                        "        { \"term\": { \"enrich_txn.s_base_txn_type.keyword\": \"invoice\" } },\n" +
                        "        {\n" +
                        "          \"terms\": {\n" +
                        "            \"enrich_txn.s_txn_type.keyword\": [\n" +
                        "              \"Refund Request\",\n" +
                        "              \"Request Refund\",\n" +
                        "              \"\"\n" +
                        "            ]\n" +
                        "          }\n" +
                        "        }\n" +
                        "      ]\n" +
                        "    }\n" +
                        "  },\n" +
                        "  \"aggs\": {\n" +
                        "    \"byComposite\": {\n" +
                        "      \"composite\": {\n" +
                        "        \"size\": 10000,\n" +
                        "        \"sources\": [\n" +
                        "          { \"transactionDay\": { \"date_histogram\": { \"field\": \"enrich_txn.d_create\", \"calendar_interval\": \"day\", \"time_zone\": \"+07:00\" } } },\n"
                        +
                        "          { \"merchantId\": { \"terms\": { \"field\": \"msp_merchant.s_id.keyword\" } } },\n" +
                        "          { \"currency\": { \"terms\": { \"field\": \"msp_payment.s_currency.keyword\" } } },\n"
                        +
                        "          { \"txnType\": { \"terms\": { \"field\": \"enrich_txn.s_txn_type.keyword\" } } }\n" +
                        "        ]\n" +
                        "      },\n" +
                        "      \"aggs\": {\n" +
                        "        \"totalVolume\": {\n" +
                        "          \"sum\": { \"field\": \"msp_invoice.n_amount\" }\n" +
                        "        }\n" +
                        "      }\n" +
                        "    }\n" +
                        "  }\n" +
                        "}";
}
