package vn.onepay.transaction.constant;

import java.util.List;
import java.util.Map;
import java.util.Set;

public class IConstants {
        public static final String PAYMENT_METHOD_INTERNATIONAL = "International";
        public static final String PAYMENT_METHOD_INSTALLMENT = "Installment";
        public static final String PAYMENT_METHOD_DOMESTIC = "Domestic";
        public static final String PAYMENT_METHOD_SS_PAY = "Samsung Pay";
        public static final String PAYMENT_METHOD_GG_PAY = "Google Pay";
        public static final String PAYMENT_METHOD_AP_PAY = "Apple Pay";
        public static final String PAYMENT_METHOD_UPOS = "UPOS";
        public static final String PAYMENT_METHOD_QT = "QT";
        public static final String PAYMENT_METHOD_ND = "ND";
        public static final String PAYMENT_METHOD_QR = "QR";
        public static final String PAYMENT_METHOD_BNPL = "BNPL";
        public static final String PAYMENT_METHOD_VIETQR = "VIETQR";

        public static final String PAYMENT_CHANNEL_CARD = "CARD";
        public static final String PAYMENT_CHANNEL_INSTALLMENT = "INSTALLMENT";


        public static final String DOWNLOAD_TASK_TOPIC = "download-task-topic";

        // provider 
        public static final String PROVIDER_KREDIVO = "Kredivo";

        // Type
        public static final String TYPE_TRANSACTION_REPORT = "TRANSACTION_REPORT";
        public static final String TYPE_TRANSACTION = "TRANSACTION";
        public static final String TYPE_REQUEST_REFUND = "REFUND_REQUEST_LIST";
        public static final String TYPE_PROMOTION = "PROMOTION";
        public static final String TYPE_PROMOTION_REPORT = "PROMOTION_REPORT";
        public static final String TYPE_UPOS = "UPOS";
        public static final String TYPE_UPOS_REPORT = "UPOS_REPORT";

        //language
        public static final String TYPE_LANGUAGE_EN = "en";
        public static final String TYPE_LANGUAGE_VI = "vi";
        // Status
        public static final String STATUS_PENDING = "PENDING";
        public static final String STATUS_DONE = "DONE";
        public static final String STATUS_FAILED = "FAILED";

        public static final Set<Integer> ALLOWED_SIZES = Set.of(20, 50, 100, 200, 500);

        public static final String TYPE_ACTION_REFUND_REQUEST = "REFUND_REQUEST";
        public static final String TYPE_ACTION_REFUND_APPROVE = "REFUND_APPROVE";
        public static final String TYPE_ACTION_REFUND_REJECT = "REFUND_REJECT";
        public static final String TYPE_ACTION_VOID = "VOID";
        public static final String TYPE_ACTION_CAPTURE = "CAPTURE";

        // transactionType
        public static final String TRANSACTION_TYPE_PURCHASE_BNPL = "Pay Later";
        public static final String TRANSACTION_TYPE_PURCHASE = "Purchase";
        public static final String TRANSACTION_TYPE_AUTHORIZE = "Authorize";
        public static final String TRANSACTION_TYPE_CAPTURE = "Capture";
        public static final String TRANSACTION_TYPE_REFUND = "Refund";
        public static final String TRANSACTION_TYPE_REQUEST_REFUND = "Request Refund";
        public static final String TRANSACTION_TYPE_REFUND_CAPTURE = "Refund Capture";
        public static final String TRANSACTION_TYPE_REFUND_PURCHASE = "Refund Purchase";
        public static final String TRANSACTION_TYPE_REFUND_DISPUTE = "Refund Dispute";
        public static final String TRANSACTION_TYPE_VOID_PURCHASE = "Void Purchase";
        public static final String TRANSACTION_TYPE_VOID_AUTHORIZE = "Void Authorize";
        public static final String TRANSACTION_TYPE_VOID_CAPTURE = "Void Capture";
        public static final String TRANSACTION_TYPE_VOID_REFUND = "Void Refund";
        public static final String TRANSACTION_TYPE_VOID_REFUND_CAPTURE = "Void Refund Capture";
        public static final String TRANSACTION_TYPE_VOID_REFUND_PURCHASE = "Void Refund Purchase";

        // action state
        public static final String ACTION_STATE_VISIBLE = "VISIBLE";
        public static final String ACTION_STATE_DISABLED = "DISABLED";
        public static final String ACTION_STATE_HIDDEN = "HIDDEN";

        public static final String ACTION_REFUND = "refund";
        public static final String ACTION_VOID = "void";
        public static final String ACTION_CAPTURE = "capture";
        public static final String ACTION_APPROVE_REJECT = "approveReject";
        public static final String ACTION_ALL = "all";

        //  transaction permission 
        public static final String TRANSACTION_APP = "transaction_app";
        public static final String TRANSACTION_APP_TRANS_MGMT = "transaction_app_trans_mgmt";
        public static final String TRANSACTION_APP_TRANS_MGMT_VIEW = "transaction_app_trans_mgmt_view";
        public static final String TRANSACTION_APP_TRANS_MGMT_DOWNLOAD = "transaction_app_trans_mgmt_download";
        public static final String TRANSACTION_APP_TRANS_MGMT_CAPTURE = "transaction_app_trans_mgmt_capture";
        public static final String TRANSACTION_APP_TRANS_MGMT_CREATE_REFUND = "transaction_app_trans_mgmt_create_refund";
        public static final String TRANSACTION_APP_TRANS_MGMT_APPROVE_REFUND = "transaction_app_trans_mgmt_approve_refund";
        public static final String TRANSACTION_APP_TRANS_MGMT_VOID_TRANS = "transaction_app_trans_mgmt_void_trans";
        public static final String TRANSACTION_APP_DASHBOARD_REPORT = "transaction_app_dashboard_report";
        public static final String TRANSACTION_APP_DASHBOARD_REPORT_VIEW = "transaction_app_dashboard_report_view";
        public static final String TRANSACTION_APP_DASHBOARD_REPORT_DOWNLOAD = "transaction_app_dashboard_report_download";

        // appName
        public static final String APP_NAME_MOMO = "Momo";
        public static final String APP_NAME_SMARTPAY = "SMARTPAY";
        public static final String APP_NAME_ZALOPAY = "ZALOPAY";

        // card type
        public static final String CARD_TYPE_JCB = "JCB";
        public static final String CARD_TYPE_PAYPAL = "PayPal";

        // advance status request refund 
        public static final String REFUND_STATUS_WAITING_FOR_ONEPAY = "Waiting for OnePay's Approval";
        public static final String REFUND_STATUS_ONEPAY_APPROVED = "OnePays approved";
        public static final String REFUND_STATUS_ONEPAY_REJECTED = "OnePays rejected";
        public static final String REFUND_STATUS_WAITING_FOR_MERCHANT = "Waiting for Approval";
        public static final String REFUND_STATUS_MERCHANT_APPROVED = "Merchant Approved";
        public static final String REFUND_STATUS_MERCHANT_REJECTED = "Merchant Rejected";

        // Installment status
        public static final String INSTALLMENT_STATUS_APPROVED = "approved";
        public static final String INSTALLMENT_STATUS_SENT = "sent";
        public static final String INSTALLMENT_STATUS_CREATED = "created";

        public static final Map<String, String> REFUND_REQUEST_LANGUAGE =  Map.ofEntries(
                Map.entry("Waiting for Merchant's Approval", "Chờ đơn vị duyệt"),
                Map.entry("Waiting for Approval", "Chờ đơn vị duyệt"),
                Map.entry("Merchant Rejected", "Đơn vị từ chối"),
                Map.entry("Waiting for OnePay's Approval", "Chờ OnePay duyệt"),
                Map.entry("OnePay Rejected", "OnePay từ chối"),
                Map.entry("Merchant Approved", "Đơn vị phê duyệt"),
                Map.entry("OnePay Approved", "OnePay phê duyệt")
        );

        public static final Map<String, String> TRANSACTION_LANGUAGE =  Map.ofEntries(
                //transaction status
                Map.entry("Successful", "Thành công"),
                Map.entry("Failed", "Thất bại"),
                Map.entry("Processing", "Đang xử lý"),
                Map.entry("Incomplete", "Chưa hoàn thiện"),

                // transaction type
                Map.entry("Purchase", "Thanh toán"),
                Map.entry("Authorize", "Cấp phép"),
                Map.entry("Capture", "Quyết toán"),
                Map.entry("Refund", "Hoàn tiền"),
                Map.entry("Refund Capture", "Hoàn tiền quyết toán"),
                Map.entry("Refund Dispute", "Hoàn tiền khiếu nại"),
                Map.entry("Void Purchase", "Hủy thanh toán"),
                Map.entry("Void Authorize", "Hủy cấp phép"),
                Map.entry("Void Capture", "Hủy quyết toán"),
                Map.entry("Void Refund", "Hủy hoàn tiền"),
                Map.entry("Void Refund Capture", "Hủy hoàn tiền quyết toán"),

                // payment method
                Map.entry("International", "Thẻ quốc tế"),
                Map.entry("Domestic", "Thẻ nội địa"),
                Map.entry("Mobile App", "Ứng dụng di động"),
                Map.entry("QR", "Ứng dụng di động"),
                Map.entry("VietQR", "VietQR"),
                Map.entry("VIETQR", "VietQR"),
                Map.entry("BNPL", "Mua ngay trả sau"),
                Map.entry("Installment", "Trả góp"),
                Map.entry("Apple Pay", "Apple Pay"),
                Map.entry("Google Pay", "Google Pay"),
                Map.entry("Samsung Pay", "Samsung Pay"),
                Map.entry("PayPal", "PayPal")
        );

        public static final Map<String, String> TRANSACTION_STATUS_MAP = Map.ofEntries(
                // Successful status
                Map.entry("approved", "Successful"),
                Map.entry("400", "Successful"),

                // Failed status
                Map.entry("failed", "Failed"),
                Map.entry("decline", "Failed"),

                // Waiting for approval
                Map.entry("Waiting for onepays approval", "Waiting for OnePay's Approval"),
                Map.entry("Waiting for OnePAY's Approval", "Waiting for OnePay's Approval"),
                Map.entry("405", "Waiting for OnePay's Approval"),
                Map.entry("401", "Waiting for Approval"),
                Map.entry("Waiting for approval", "Waiting for Approval"),

                // OnePay approval result
                Map.entry("OnePAY Approved", "OnePays approved"),
                Map.entry("OnePAY Rejected", "OnePays rejected"),
                Map.entry("310", "OnePays approved"),

                // Processing states
                Map.entry("Waiting for Authentication", "Processing"),
                Map.entry("pending", "Processing"),
                Map.entry("authorization_required", "Processing"),
                Map.entry("verified", "Processing")
        );

        public static final Map<String, String> TRANSACTION_TYPE_MAP = Map.ofEntries(
                Map.entry("Pay Later", "Purchase")
        );
        
        public static final Map<String, String> PAYMENT_METHOD_MAP = Map.of(
                "International", "QT",
                "Domestic",      "ND",
                "Mobile App",    "QR",
                "VietQR",        "VIETQR",
                "BNPL",          "BNPL",
                "Direct Debit",  "DD",

                "INT",           "QT",
                "DOM",           "ND",
                "DIRECT-DEBIT",  "DD"
        );

        public static final Map<String, String> PAYMENT_METHOD_MAP_IN_LIST = Map.of(
                "INT",           "International",
                "DOM",           "Domestic"
        );

        public static final Map<String, String> PAYMENT_METHOD_MAP_LANGUAGE = Map.of(
                "QR",           "Mobile App"
        );

        // Config trường trả ra từ opensearch
        public static final List<String> SOURCE_FIELDS_TRANSACTION = List.of(
                        "msp_merchant.s_id", "enrich_txn.s_base_id", "enrich_txn.s_id", "enrich_txn.s_txn_ref",
                        "msp_payment.s_e_card_number", "enrich_txn.s_txn_type", "enrich_txn.s_response_code",
                        "enrich_txn.s_e_response_code", "enrich_txn.s_state", "msp_payment.s_e_gate_label",
                        "msp_invoice.s_currencies", "msp_payment.s_e_pay_method", "msp_payment.s_e_source",
                        "msp_invoice.n_amount", "enrich_txn.d_original_date", "enrich_txn.d_create",
                        "enrich_txn.d_update", "msp_invoice.s_e_order_source","msp_invoice.s_info");

        public static final List<String> SOURCE_FIELDS_REQUEST_REFUND = List.of(
                        "msp_merchant.s_id", "enrich_txn.s_id", "enrich_txn.s_response_code", "enrich_txn.s_state",
                        "msp_invoice.s_currencies", "msp_payment.s_e_source", "enrich_txn.d_create",
                        "enrich_txn.n_amount", "msp_refund_request.s_parent_id","msp_invoice.s_info");
        public static final List<String> SOURCE_FIELDS_PROMOTION = List.of(
                        "msp_merchant.s_id",
                        "enrich_txn.s_base_id",
                        "enrich_txn.s_id",
                        "enrich_txn.s_txn_ref",
                        "msp_payment.s_e_card_number",
                        "enrich_txn.s_txn_type",
                        "enrich_txn.s_response_code",
                        "enrich_txn.s_e_response_code",
                        "enrich_txn.s_state",
                        "msp_invoice.s_currencies",
                        "onepr_pr.s_id",
                        "onepr_pr.s_name",
                        "msp_payment.s_e_pay_method",
                        "enrich_txn.n_original_amount",
                        "msp_payment.n_amount",
                        "enrich_txn.n_original_amount",
                        "enrich_txn.d_create","msp_invoice.s_info");
        public static String baseUposConditionJson = "{\n" + //
                                "    \"size\": 0,\n" + //
                                "    \"query\": {\n" + //
                                "        \"bool\": {\n" + //
                                "            \"filter\": [\n" + //
                                "                {\n" + //
                                "                    \"range\": {\n" + //
                                "                        \"enrich_txn.d_create\": {\n" + //
                                "                            \"gte\": \"%s\",\r\n" + //
                                "                            \"lt\": \"%s\",\r\n" + //
                                "                            \"time_zone\": \"+07:00\"\n" + //
                                "                        }\n" + //
                                "                    }\n" + //
                                "                },\n" + //
                                "                {\n" + //
                                "                    \"terms\": {\n" + //
                                "                        \"msp_payment.s_e_merchant_channel.keyword\": [\n" + //
                                "                            \"UPOS\"\n" + //
                                "                        ]\n" + //
                                "                    }\n" + //
                                "                },\n" + //
                                "                {\n" + //
                                "                    \"wildcard\": {\n" + //
                                "                        \"enrich_txn.s_state.keyword\": \"Successful\"\n" + //
                                "                    }\n" + //
                                "                }\n" + //
                                "            ],\n" + //
                                "            \"must_not\": [\n" + //
                                "                {\n" + //
                                "                    \"term\": {\n" + //
                                "                        \"b_has_payment\": true\n" + //
                                "                    }\n" + //
                                "                },\n" + //
                                "                {\n" + //
                                "                    \"term\": {\n" + //
                                "                        \"enrich_txn.s_base_txn_type.keyword\": \"invoice\"\n" + //
                                "                    }\n" + //
                                "                },\n" + //
                                "                {\n" + //
                                "                    \"terms\": {\n" + //
                                "                        \"enrich_txn.s_txn_type.keyword\": [\n" + //
                                "                            \"Refund Request\",\n" + //
                                "                            \"Request Refund\",\n" + //
                                "                            \"\"\n" + //
                                "                        ]\n" + //
                                "                    }\n" + //
                                "                }\n" + //
                                "            ]\n" + //
                                "        }\n" + //
                                "    },\n" + //
                                "    \"aggs\": {\n" + //
                                "        \"byComposite\": {\n" + //
                                "            \"composite\": {\n" + //
                                "                \"size\": 10000,\n" + //
                                "                \"sources\": [\n" + //
                                "                    {\n" + //
                                "                        \"transactionDay\": {\n" + //
                                "                            \"date_histogram\": {\n" + //
                                "                                \"field\": \"enrich_txn.d_create\",\n" + //
                                "                                \"calendar_interval\": \"day\",\n" + //
                                "                                \"time_zone\": \"+07:00\"\n" + //
                                "                            }\n" + //
                                "                        }\n" + //
                                "                    },\n" + //
                                "                    {\n" + //
                                "                        \"merchantId\": {\n" + //
                                "                            \"terms\": {\n" + //
                                "                                \"field\": \"msp_merchant.s_id.keyword\"\n" + //
                                "                            }\n" + //
                                "                        }\n" + //
                                "                    },\n" + //
                                "                    {\n" + //
                                "                        \"currency\": {\n" + //
                                "                            \"terms\": {\n" + //
                                "                                \"field\": \"msp_payment.s_currency.keyword\"\n" + //
                                "                            }\n" + //
                                "                        }\n" + //
                                "                    },\n" + //
                                "                    {\n" + //
                                "                        \"txnType\": {\n" + //
                                "                            \"terms\": {\n" + //
                                "                                \"field\": \"enrich_txn.s_txn_type.keyword\"\n" + //
                                "                            }\n" + //
                                "                        }\n" + //
                                "                    },\n" + //
                                "                    {\n" + //
                                "                        \"paymentChannel\": {\n" + //
                                "                            \"terms\": {\n" + //
                                "                                \"script\": {\n" + //
                                "                                    \"source\": \"def method = doc['msp_payment.s_e_pay_method.keyword'].size() > 0 ? doc['msp_payment.s_e_pay_method.keyword'].value : null; if (method != 'CARD') return method; if (!doc.containsKey('msp_payment.s_ita_id.keyword') || doc['msp_payment.s_ita_id.keyword'].size() == 0) return 'CARD'; def ita = doc['msp_payment.s_ita_id.keyword'].value; return (ita == '') ? 'CARD' : 'Installment';\",\n" + //
                                "                                    \"lang\": \"painless\"\n" + //
                                "                                }\n" + //
                                "                            }\n" + //
                                "                        }\n" + //
                                "                    },\n" + //
                                "                    {\n" + //
                                "                        \"cardType\": {\n" + //
                                "                            \"terms\": {\n" + //
                                "                                \"field\": \"msp_payment.s_e_card.keyword\"\n" + //
                                "                            }\n" + //
                                "                        }\n" + //
                                "                    },\n" + //
                                "                    {\n" + //
                                "                        \"tid\": {\n" + //
                                "                            \"terms\": {\n" + //
                                "                                \"script\": {\n" + //
                                "                                    \"source\": \"def v = doc['msp_payment.s_data.tid.keyword'].size() == 0 ? '' : doc['msp_payment.s_data.tid.keyword'].value;\\n" + //
                                "return v == '' ? '' : v;\",\n" + //
                                "                                    \"lang\": \"painless\"\n" + //
                                "                                }\n" + //
                                "                            }\n" + //
                                "                        }\n" + //
                                "                    }\n" + //
                                "                ]\n" + //
                                "            },\n" + //
                                "            \"aggs\": {\n" + //
                                "                \"amount\": {\n" + //
                                "                    \"sum\": {\n" + //
                                "                        \"field\": \"msp_payment.n_amount\"\n" + //
                                "                    }\n" + //
                                "                }\n" + //
                                "            }\n" + //
                                "        }\n" + //
                                "    }\n" + //
                                "}";
        public static String basePromotionConditionJson = "{\r\n" + //
                        "    \"size\": 0,\r\n" + //
                        "    \"query\": {\r\n" + //
                        "        \"bool\": {\r\n" + //
                        "            \"filter\": [\r\n" + //
                        "                {\r\n" + //
                        "                    \"range\": {\r\n" + //
                        "                        \"enrich_txn.d_create\": {\r\n" + //
                        "                            \"gte\": \"%s\",\r\n" + //
                        "                            \"lt\": \"%s\",\r\n" + //
                        "                            \"time_zone\": \"+07:00\"\r\n" + //
                        "                        }\r\n" + //
                        "                    }\r\n" + //
                        "                },\r\n" + //
                        "                {\r\n" + //
                        "                    \"terms\": {\r\n" + //
                        "                        \"msp_payment.s_e_channel.keyword\": [\r\n" + //
                        "                            \"Promotion\"\r\n" + //
                        "                        ]\r\n" + //
                        "                    }\r\n" + //
                        "                },\r\n" + //
                        "                {\r\n" + //
                        "                    \"wildcard\": {\r\n" + //
                        "                        \"enrich_txn.s_state.keyword\": \"Successful\"\r\n" + //
                        "                    }\r\n" + //
                        "                }\r\n" + //
                        "            ],\r\n" + //
                        "            \"must_not\": [\r\n" + //
                        "                {\r\n" + //
                        "                    \"term\": {\r\n" + //
                        "                        \"b_has_payment\": true\r\n" + //
                        "                    }\r\n" + //
                        "                },\r\n" + //
                        "                {\r\n" + //
                        "                    \"term\": {\r\n" + //
                        "                        \"enrich_txn.s_base_txn_type.keyword\": \"invoice\"\r\n" + //
                        "                    }\r\n" + //
                        "                },\r\n" + //
                        "                {\r\n" + //
                        "                    \"terms\": {\r\n" + //
                        "                        \"enrich_txn.s_txn_type.keyword\": [\r\n" + //
                        "                            \"Refund Request\",\r\n" + //
                        "                            \"Request Refund\",\r\n" + //
                        "                            \"\"\r\n" + //
                        "                        ]\r\n" + //
                        "                    }\r\n" + //
                        "                }\r\n" + //
                        "            ]\r\n" + //
                        "        }\r\n" + //
                        "    },\r\n" + //
                        "    \"aggs\": {\r\n" + //
                        "        \"byComposite\": {\r\n" + //
                        "            \"composite\": {\r\n" + //
                        "                \"size\": 10000,\r\n" + //
                        "                \"sources\": [\r\n" + //
                        "                    {\r\n" + //
                        "                        \"transactionDay\": {\r\n" + //
                        "                            \"date_histogram\": {\r\n" + //
                        "                                \"field\": \"enrich_txn.d_create\",\r\n" + //
                        "                                \"calendar_interval\": \"day\",\r\n" + //
                        "                                \"time_zone\": \"+07:00\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    },\r\n" + //
                        "                    {\r\n" + //
                        "                        \"merchantId\": {\r\n" + //
                        "                            \"terms\": {\r\n" + //
                        "                                \"field\": \"msp_merchant.s_id.keyword\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    },\r\n" + //
                        "                    {\r\n" + //
                        "                        \"currency\": {\r\n" + //
                        "                            \"terms\": {\r\n" + //
                        "                                \"field\": \"msp_payment.s_currency.keyword\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    },\r\n" + //
                        "                    {\r\n" + //
                        "                        \"txnType\": {\r\n" + //
                        "                            \"terms\": {\r\n" + //
                        "                                \"field\": \"enrich_txn.s_txn_type.keyword\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    },\r\n" + //
                        "                    {\r\n" + //
                        "                        \"pr_id\": {\r\n" + //
                        "                            \"terms\": {\r\n" + //
                        "                                \"field\": \"onepr_pr.s_id.keyword\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    },\r\n" + //
                        "                    {\r\n" + //
                        "                        \"paymentMethod\": {\r\n" + //
                        "                            \"terms\": {\r\n" + //
                        "                                \"field\": \"msp_payment.s_e_pay_method.keyword\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    },\r\n" + //
                        "                    {\r\n" + //
                        "                        \"payGate\": {\r\n" + //
                        "                            \"terms\": {\r\n" + //
                        "                                \"field\": \"msp_payment.s_e_gate_label.keyword\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    },\r\n" + //
                        "                    {\r\n" + //
                        "                        \"cardType\": {\r\n" + //
                        "                            \"terms\": {\r\n" + //
                        "                                \"field\": \"msp_payment.s_e_card.keyword\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    }\r\n" + //
                        "                ]\r\n" + //
                        "            },\r\n" + //
                        "            \"aggs\": {\r\n" + //
                        "                \"totalVolume\": {\r\n" + //
                        "                    \"sum\": {\r\n" + //
                        "                        \"field\": \"msp_invoice.n_amount\"\r\n" + //
                        "                    }\r\n" + //
                        "                },\r\n" + //
                        "                \"totalPayment\": {\r\n" + //
                        "                    \"sum\": {\r\n" + //
                        "                        \"field\": \"msp_payment.n_amount\"\r\n" + //
                        "                    }\r\n" + //
                        "                }\r\n" + //
                        "            }\r\n" + //
                        "        }\r\n" + //
                        "    }\r\n" + //
                        "}";
        public static String baseConditionJson = "{\r\n" + //
                        "    \"size\": 0,\r\n" + //
                        "    \"query\": {\r\n" + //
                        "        \"bool\": {\r\n" + //
                        "            \"filter\": [\r\n" + //
                        "                {\r\n" + //
                        "                    \"range\": {\r\n" + //
                        "                        \"enrich_txn.d_create\": {\r\n" + //
                        "                            \"gte\": \"%s\",\r\n" + //
                        "                            \"lt\": \"%s\",\r\n" + //
                        "                            \"time_zone\": \"+07:00\"\r\n" + //
                        "                        }\r\n" + //
                        "                    }\r\n" + //
                        "                },\r\n" + //
                        "                {\r\n" + //
                        "                    \"wildcard\": {\r\n" + //
                        "                        \"enrich_txn.s_state.keyword\": \"Successful\"\r\n" + //
                        "                    }\r\n" + //
                        "                }\r\n" + //
                        "            ],\r\n" + //
                        "            \"must_not\": [\r\n" + //
                        "                {\r\n" + //
                        "                    \"term\": {\r\n" + //
                        "                        \"b_has_payment\": true\r\n" + //
                        "                    }\r\n" + //
                        "                },\r\n" + //
                        "                {\r\n" + //
                        "                    \"term\": {\r\n" + //
                        "                        \"enrich_txn.s_base_txn_type.keyword\": \"invoice\"\r\n" + //
                        "                    }\r\n" + //
                        "                },\r\n" + //
                        "                {\r\n" + //
                        "                    \"terms\": {\r\n" + //
                        "                        \"enrich_txn.s_txn_type.keyword\": [\r\n" + //
                        "                            \"Refund Request\",\r\n" + //
                        "                            \"Request Refund\",\r\n" + //
                        "                            \"\"\r\n" + //
                        "                        ]\r\n" + //
                        "                    }\r\n" + //
                        "                }\r\n" + //
                        "            ]\r\n" + //
                        "        }\r\n" + //
                        "    },\r\n" + //
                        "    \"aggs\": {\r\n" + //
                        "        \"byComposite\": {\r\n" + //
                        "            \"composite\": {\r\n" + //
                        "                \"size\": 10000,\r\n" + //
                        "                \"sources\": [\r\n" + //
                        "                    {\r\n" + //
                        "                        \"transactionDay\": {\r\n" + //
                        "                            \"date_histogram\": {\r\n" + //
                        "                                \"field\": \"enrich_txn.d_create\",\r\n" + //
                        "                                \"calendar_interval\": \"day\",\r\n" + //
                        "                                \"time_zone\": \"+07:00\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    },\r\n" + //
                        "                    {\r\n" + //
                        "                        \"merchantId\": {\r\n" + //
                        "                            \"terms\": {\r\n" + //
                        "                                \"field\": \"msp_merchant.s_id.keyword\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    },\r\n" + //
                        "                    {\r\n" + //
                        "                        \"currency\": {\r\n" + //
                        "                            \"terms\": {\r\n" + //
                        "                                \"field\": \"msp_payment.s_currency.keyword\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    },\r\n" + //
                        "                    {\r\n" + //
                        "                        \"txnType\": {\r\n" + //
                        "                            \"terms\": {\r\n" + //
                        "                                \"field\": \"enrich_txn.s_txn_type.keyword\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    },\r\n" + //
                        "                    {\r\n" + //
                        "                        \"paymentMethod\": {\r\n" + //
                        "                            \"terms\": {\r\n" + //
                        "                                \"field\": \"msp_payment.s_e_pay_method.keyword\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    },\r\n" + //
                        "                    {\r\n" + //
                        "                        \"orderSource\": {\r\n" + //
                        "                            \"terms\": {\r\n" + //
                        "                                \"field\": \"msp_invoice.s_e_order_source.keyword\"\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    },\r\n" + //
                        "                    {\r\n" + //
                        "                        \"isPromotion\": {\r\n" + //
                        "                            \"terms\": {\r\n" + //
                        "                                \"script\": {\r\n" + //
                        "                                    \"source\": \"def channel = doc['msp_payment.s_e_channel.keyword'].size() == 0 ? null : doc['msp_payment.s_e_channel.keyword'].value;\\n"
                        + //
                        "return channel == 'Promotion';\",\r\n" + //
                        "                                    \"lang\": \"painless\"\r\n" + //
                        "                                }\r\n" + //
                        "                            }\r\n" + //
                        "                        }\r\n" + //
                        "                    }\r\n" + //
                        "                ]\r\n" + //
                        "            },\r\n" + //
                        "            \"aggs\": {\r\n" + //
                        "                \"totalVolume\": {\r\n" + //
                        "                    \"sum\": {\r\n" + //
                        "                        \"field\": \"enrich_txn.n_amount\"\r\n" + //
                        "                    }\r\n" + //
                        "                }\r\n" + //
                        "            }\r\n" + //
                        "        }\r\n" + //
                        "    }\r\n" + //
                        "}";
}
