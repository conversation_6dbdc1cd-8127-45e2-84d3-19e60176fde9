package vn.onepay.transaction.opensearch;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import vn.onepay.transaction.constant.IConstants;

import vn.onepay.transaction.util.OpenSearchUtil;

public class OpenSearchQueryBuilder {
    private static final Logger logger = LoggerFactory.getLogger(OpenSearchQueryBuilder.class);
    public static String buildQuery(Map<String, Object> conditions) throws Exception {
        ObjectMapper mapper = new ObjectMapper();

        int page = (Integer) conditions.get("page");
        int pageSize = (Integer) conditions.get("pageSize");
        int from = page * pageSize;
        String fromDate = OpenSearchUtil.convertUtcToOffset(conditions.get("fromDate").toString()).replaceAll(" ", "");
        String toDate = OpenSearchUtil.convertUtcToOffset(conditions.get("toDate").toString()).replaceAll(" ", "");
        String sortOrder = (String) conditions.get("sortOrder");
        String paymentMethod = (String) conditions.get("paymentMethod");
        String type = (String) conditions.get("type");
        ObjectNode query = mapper.createObjectNode();
        query.put("from", from);
        query.put("size", pageSize);
        query.put("track_total_hits", true);

        ObjectNode boolObj = mapper.createObjectNode();
        if (type != null && IConstants.TYPE_REQUEST_REFUND.equals(type)) {
            boolObj.set("filter", buildFiltersRequestRefund(mapper, conditions, fromDate, toDate, paymentMethod));
            boolObj.set("must_not", buildMustNotByType(mapper,type));

            // Bổ sung phần searchKeyword (LIKE trên nhiều trường)
            String searchKeyword = (String) conditions.get("searchKeyword");
            if (searchKeyword != null && !searchKeyword.trim().isEmpty()) {
                ArrayNode shouldArray = mapper.createArrayNode();
                String wildcardValue = "*" + searchKeyword.trim().toLowerCase() + "*";

                String[] fields = new String[] {
                        "msp_merchant.s_id.keyword",
                        "enrich_txn.s_id.keyword",
                        "enrich_txn.s_state.keyword",
                        "enrich_txn.s_operator_id.keyword",
                        "msp_refund_request.s_parent_id.keyword"
                };

                for (String field : fields) {
                    ObjectNode wildcardObj = mapper.createObjectNode();
                    ObjectNode fieldObj = mapper.createObjectNode();
                    fieldObj.put("value", wildcardValue);
                    fieldObj.put("case_insensitive", true);
                    wildcardObj.set(field, fieldObj);

                    ObjectNode wildcardQuery = mapper.createObjectNode();
                    wildcardQuery.set("wildcard", wildcardObj);

                    shouldArray.add(wildcardQuery);
                }

                //search amount
                try {
                    long amount = Long.parseLong(searchKeyword.trim());
                    ObjectNode termQuery = mapper.createObjectNode();
                    ObjectNode amountField = mapper.createObjectNode();
                    amountField.put("value", amount);
                    termQuery.set("enrich_txn.n_amount", amountField);
            
                    ObjectNode termWrapper = mapper.createObjectNode();
                    termWrapper.set("term", termQuery);
            
                    shouldArray.add(termWrapper);
                } catch (NumberFormatException e) {
                    logger.debug("Search keyword is not a valid number, skip amount term query");
                }

                boolObj.set("should", shouldArray);
                boolObj.put("minimum_should_match", 1);
            }
        } else {
            boolObj.set("filter", buildFilters(mapper, conditions, fromDate, toDate, paymentMethod));
            boolObj.set("must_not", buildMustNot(mapper));

            String searchType = (String) conditions.get("searchType");
            String searchKeyword = (String) conditions.get("searchKeyword");
            if (searchKeyword != null && !searchKeyword.trim().isEmpty()) {
                ArrayNode shouldArray = mapper.createArrayNode();
                String wildcardValue = "*" + searchKeyword.trim().toLowerCase() + "*";

                String[] fields = new String[] {};
                switch (searchType.toLowerCase()) {
                    case "all" -> fields = new String[] {
                        "msp_merchant.s_id.keyword",
                        "enrich_txn.s_id.keyword",
                        "enrich_txn.s_txn_ref.keyword",
                        "enrich_txn.s_txn_type.keyword",
                        "enrich_txn.s_state.keyword",
                        "enrich_txn.s_response_code.keyword",
                        "msp_payment.s_e_card_number.keyword"
                    };
                    case "transactionid" -> fields = new String[] { "enrich_txn.s_id.keyword" };
                    case "orderreference" -> fields = new String[] { "enrich_txn.s_txn_ref.keyword" };
                    case "cardnumber" -> fields = new String[] { "msp_payment.s_e_card_number.keyword" };
                }
                for (String field : fields) {
                    ObjectNode wildcardObj = mapper.createObjectNode();
                    ObjectNode fieldObj = mapper.createObjectNode();
                    fieldObj.put("value", wildcardValue);
                    fieldObj.put("case_insensitive", true);
                    wildcardObj.set(field, fieldObj);

                    ObjectNode wildcardQuery = mapper.createObjectNode();
                    wildcardQuery.set("wildcard", wildcardObj);

                    shouldArray.add(wildcardQuery);
                }
                //search amount
                try {
                    long amount = Long.parseLong(searchKeyword.trim());
                    ObjectNode termQuery = mapper.createObjectNode();
                    ObjectNode amountField = mapper.createObjectNode();
                    amountField.put("value", amount);
                    termQuery.set("msp_invoice.n_amount", amountField);
            
                    ObjectNode termWrapper = mapper.createObjectNode();
                    termWrapper.set("term", termQuery);
            
                    shouldArray.add(termWrapper);
                } catch (NumberFormatException e) {
                    logger.debug("Search keyword is not a valid number, skip amount term query");
                }
                boolObj.set("should", shouldArray);
                boolObj.put("minimum_should_match", 1);
            }

        }

        //source giới hạn field trả ra
        ObjectNode queryObj = mapper.createObjectNode();
        ArrayNode sourceFields = mapper.createArrayNode();
        if(IConstants.TYPE_TRANSACTION.equals(type)){         
            for (String field : IConstants.SOURCE_FIELDS_TRANSACTION) {
                sourceFields.add(field);
            }
                query.set("_source", sourceFields);
        }else if(IConstants.TYPE_REQUEST_REFUND.equals(type)) {
            for (String field : IConstants.SOURCE_FIELDS_REQUEST_REFUND) {
                sourceFields.add(field);
            }
                query.set("_source", sourceFields);
        }else if(IConstants.TYPE_PROMOTION.equals(type)) {
            for (String field : IConstants.SOURCE_FIELDS_PROMOTION) {
                sourceFields.add(field);
            }
                query.set("_source", sourceFields);
        }
        
        queryObj.set("bool", boolObj);
        query.set("query", queryObj);
        query.set("sort", buildSortArr(mapper, sortOrder));
        // query.set("aggs", buildAggregations(mapper));

        return mapper.writeValueAsString(query);
    }

    public static String buildQueryHistory(String baseId,String type) throws Exception {
        ObjectMapper mapper = new ObjectMapper();

        ObjectNode query = mapper.createObjectNode();
        query.put("track_total_hits", true);

        // === wildcard filter enrich_txn.s_base_id.keyword ===
        ObjectNode wildcard = mapper.createObjectNode();
        wildcard.put("enrich_txn.s_base_id.keyword", baseId);
        ObjectNode wildcardWrapper = mapper.createObjectNode();
        wildcardWrapper.set("wildcard", wildcard);

        ArrayNode filterArray = mapper.createArrayNode();
        filterArray.add(wildcardWrapper);

        // === Combine into bool query ===
        ObjectNode boolNode = mapper.createObjectNode();
        boolNode.set("filter", filterArray);
        boolNode.set("must_not", buildMustNotByType(mapper, type));

        ObjectNode queryNode = mapper.createObjectNode();
        queryNode.set("bool", boolNode);
        query.set("query", queryNode);
        query.set("sort", buildSortArr(mapper, "desc"));

        return mapper.writeValueAsString(query);
    }

    private static ArrayNode buildFiltersRequestRefund(ObjectMapper mapper, Map<String, Object> conditions,
            String fromDate, String toDate, String paymentMethod) {
        ArrayNode filters = mapper.createArrayNode();
        // Date range
        ObjectNode range = mapper.createObjectNode();
        ObjectNode date = mapper.createObjectNode();
        date.put("gte", fromDate);
        date.put("lte", toDate);
        range.set("enrich_txn.d_create", date);

        ObjectNode rangeWrapper = mapper.createObjectNode();
        rangeWrapper.set("range", range);
        filters.add(rangeWrapper);
        conditions.put("typeRequestRefund", "Request Refund,Refund Request");
        // Terms
        addTermsFilterIfPresent(mapper, conditions, "merchantId", "msp_invoice.s_merchant_id.keyword", filters);
        addTermsFilterIfPresent(mapper, conditions, "status", "enrich_txn.s_state.keyword", filters);
        addTermsFilterIfPresent(mapper, conditions, "typeRequestRefund", "enrich_txn.s_txn_type.keyword", filters);
        return filters;
    }

    private static ArrayNode buildFilters(ObjectMapper mapper, Map<String, Object> conditions,
            String fromDate, String toDate, String paymentMethod) {
        ArrayNode filters = mapper.createArrayNode();

        // Date range
        ObjectNode range = mapper.createObjectNode();
        ObjectNode date = mapper.createObjectNode();
        date.put("gte", fromDate);
        date.put("lte", toDate);
        range.set("enrich_txn.d_create", date);

        ObjectNode rangeWrapper = mapper.createObjectNode();
        rangeWrapper.set("range", range);
        filters.add(rangeWrapper);

        // Terms
        addTermsFilterIfPresent(mapper, conditions, "merchantId", "msp_invoice.s_merchant_id.keyword", filters);
        addTermsFilterIfPresent(mapper, conditions, "transactionType", "enrich_txn.s_txn_type.keyword", filters);
        addTermsFilterIfPresent(mapper, conditions, "transactionStatus", "enrich_txn.s_state.keyword", filters);
        addTermsFilterIfPresent(mapper, conditions, "transId", "enrich_txn.s_id.keyword", filters);
        addTermsFilterIfPresent(mapper, conditions, "promoCode", "onepr_pr.s_id.keyword", filters);
        addTermsFilterIfPresent(mapper, conditions, "promoName", "onepr_pr.s_name.keyword", filters);
        addTermsFilterIfPresent(mapper, conditions, "cardNumber", "msp_payment.s_e_card_number.keyword", filters);
        addTermsFilterIfPresent(mapper, conditions, "paymentMethod", "msp_payment.s_e_pay_method.keyword", filters);
        addTermsFilterIfPresent(mapper, conditions, "paymentSource", "msp_payment.s_e_gate.keyword", filters);
        addTermsFilterIfPresent(mapper, conditions, "orderSource", "msp_invoice.s_e_order_source.keyword", filters);
        //is promotion
        addTermsFilterIfPresent(mapper, conditions, "Promotion", "msp_payment.s_e_channel.keyword", filters);
        addTermsFilterIfPresent(mapper, conditions, "orderInfo", "msp_invoice.s_info.keyword", filters);
        //Giao dịch upos
        addTermsFilterIfPresent(mapper, conditions, "merchantChannel", "msp_payment.s_e_merchant_channel.keyword", filters);
        addTermsFilterIfPresent(mapper, conditions, "tid", "msp_payment.s_data.tid.keyword", filters);
        addTermsFilterIfPresent(mapper, conditions, "orderReference", "msp_invoice.s_info.keyword", filters);
        addTermsFilterIfPresent(mapper, conditions, "paymentChannel", "msp_payment.s_e_pay_method.keyword", filters);
        addTermsFilterIfPresent(mapper, conditions, "merchantTransRef", "enrich_txn.s_txn_ref.keyword", filters);
        addTermsFilterIfPresent(mapper, conditions, "cardType", "msp_payment.s_ins_brand_id.keyword", filters);
        addTermsFilterIfPresent(mapper, conditions, "approvalCode", "msp_payment.s_data.authorize_id.keyword", filters);
        return filters;
    }

    // Hàm tạo query cho OpenSearch với `after_key` nếu có
    public static String buildQueryWithAfterKey(String baseQuery, JsonNode afterKeyNode) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            ObjectNode base = (ObjectNode) mapper.readTree(baseQuery);

            // Truy cập phần composite trong aggregation
            ObjectNode aggs = (ObjectNode) base.path("aggs");
            ObjectNode byComposite = (ObjectNode) aggs.path("byComposite");
            ObjectNode composite = (ObjectNode) byComposite.path("composite");

            // Gán after vào bên trong composite
            composite.set("after", afterKeyNode);

            return mapper.writeValueAsString(base);
        } catch (Exception e) {
            throw new RuntimeException("Failed to build query with after_key", e);
        }
    }

    private static ArrayNode buildMustNot(ObjectMapper mapper) {
        ArrayNode mustNot = mapper.createArrayNode();
        addMustNotTerm(mapper, mustNot, "b_has_payment", true);
        addMustNotTerm(mapper, mustNot, "enrich_txn.s_base_txn_type.keyword", "invoice");

        List<String> txnTypes = Arrays.asList("Refund Request", "Request Refund", "");
        addMustNotTerm(mapper, mustNot, "enrich_txn.s_txn_type.keyword", txnTypes);

        return mustNot;
    }

    private static ArrayNode buildMustNotByType(ObjectMapper mapper, String type) {
        ArrayNode mustNot = mapper.createArrayNode();
        addMustNotTerm(mapper, mustNot, "b_has_payment", true);
        addMustNotTerm(mapper, mustNot, "enrich_txn.s_base_txn_type.keyword", "invoice");
        if(!type.equals(IConstants.TYPE_REQUEST_REFUND)){
            List<String> txnTypes = Arrays.asList("Refund Request", "Request Refund", "");
            addMustNotTerm(mapper, mustNot, "enrich_txn.s_txn_type.keyword", txnTypes);
        }
        return mustNot;
    }


    // === Hàm helper để thêm điều kiện terms vào filter ===
    private static void addTermsFilterIfPresent(ObjectMapper mapper, Map<String, Object> conditions,
            String key, String field, ArrayNode filterArr) {
        Object valueObj = conditions.get(key);
        if (valueObj != null) {
            List<String> values;

            if (valueObj instanceof String) {
                String raw = ((String) valueObj).trim();
                if (!raw.isEmpty()) {
                    values = Arrays.asList(raw.split(",\\s*"));
                } else {
                    values = null;
                }
            } else if (valueObj instanceof List) {
                values = (List<String>) valueObj;
            } else {
                values = null;
            }

            if (values != null && !values.isEmpty()) {
                ObjectNode termsNode = mapper.createObjectNode();
                ArrayNode termsArray = mapper.valueToTree(values);
                termsNode.set(field, termsArray);

                ObjectNode termsObj = mapper.createObjectNode();
                termsObj.set("terms", termsNode);

                filterArr.add(termsObj);
            }
        }
    }

    private static ArrayNode buildSortArr(ObjectMapper mapper, String sortOrder) {
        ArrayNode sortArr = mapper.createArrayNode();

        ObjectNode sortByTxnDateDesc = mapper.createObjectNode();
        ObjectNode orderObj = mapper.createObjectNode();
        orderObj.put("order", sortOrder);
        sortByTxnDateDesc.set("enrich_txn.d_create", orderObj);

        sortArr.add(sortByTxnDateDesc);
        return sortArr;
    }

    private static void addMustNotTerm(ObjectMapper mapper, ArrayNode mustNotArr, String field, Object value) {
        ObjectNode boolNode = mapper.createObjectNode();
        ArrayNode filterArr = mapper.createArrayNode();

        ObjectNode termObj = mapper.createObjectNode();

        if (value instanceof List) {
            // Nếu value là một List, sử dụng "terms"
            ArrayNode termsArray = mapper.createArrayNode();
            List<String> listValue = (List<String>) value;

            // Chuyển List<String> thành ArrayNode
            for (String item : listValue) {
                termsArray.add(item);
            }

            ObjectNode terms = mapper.createObjectNode();
            terms.set(field, termsArray); // gán ArrayNode vào "terms"
            termObj.set("terms", terms);
        } else {
            // Nếu value là một giá trị đơn, sử dụng "term"
            ObjectNode term = mapper.createObjectNode();
            term.put(field, mapper.convertValue(value, JsonNode.class)); // dùng để handle kiểu khác ngoài String
            termObj.set("term", term);
        }

        filterArr.add(termObj);

        boolNode.set("filter", filterArr);
        ObjectNode mustNotItem = mapper.createObjectNode();
        mustNotItem.set("bool", boolNode);
        mustNotArr.add(mustNotItem);
    }

}
