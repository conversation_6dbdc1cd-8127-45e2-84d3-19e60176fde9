package vn.onepay.transaction.exception;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.reactive.resource.NoResourceFoundException;

import reactor.core.publisher.Mono;
import vn.onepay.transaction.dto.APIExceptionDTO;

@ControllerAdvice
@Order(Ordered.HIGHEST_PRECEDENCE)
public class RESTExceptionHandler {
        private static final Logger logger = LoggerFactory.getLogger(RESTExceptionHandler.class);

        // private void logException(Exception ex) {
        // logger.info(ex.getMessage());
        // }

        @ExceptionHandler({ NoItemExistsException.class })
        public Mono<ResponseEntity<Object>> handleNonExistingItemException(NoItemExistsException ex) {
                APIExceptionDTO dto = APIExceptionDTO.builder()
                                .status(HttpStatus.NOT_FOUND.value())
                                .message(ex.getMessage())
                                .build();
                return Mono.just(
                                ResponseEntity.status(HttpStatus.NOT_FOUND.value())
                                                .contentType(MediaType.APPLICATION_JSON).body(dto));
        }

        @ExceptionHandler({ DateRangeInvalidException.class })
        public Mono<ResponseEntity<APIExceptionDTO>> handleInvalidDateException(DateRangeInvalidException ex) {
                APIExceptionDTO dto = APIExceptionDTO.builder()
                                .status(HttpStatus.BAD_REQUEST.value())
                                .message(ex.getMessage())
                                .build();

                return Mono.just(ResponseEntity
                                .status(HttpStatus.BAD_REQUEST)
                                .contentType(MediaType.APPLICATION_JSON)
                                .body(dto));
        }

        @ExceptionHandler({ ForbiddenException.class })
        public Mono<ResponseEntity<APIExceptionDTO>> handleForbiddenException(ForbiddenException ex) {
                APIExceptionDTO dto = APIExceptionDTO.builder()
                                .status(HttpStatus.FORBIDDEN.value())
                                .message(ex.getMessage())
                                .build();

                return Mono.just(ResponseEntity
                                .status(HttpStatus.FORBIDDEN)
                                .contentType(MediaType.APPLICATION_JSON)
                                .body(dto));
        }
}
