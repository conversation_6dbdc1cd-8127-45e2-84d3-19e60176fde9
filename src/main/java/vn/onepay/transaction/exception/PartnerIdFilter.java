package vn.onepay.transaction.exception;

import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;

import java.nio.charset.StandardCharsets;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.r2dbc.core.DatabaseClient;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Component
public class PartnerIdFilter implements WebFilter {
    private static final Logger logger = LoggerFactory.getLogger(PartnerIdFilter.class);
    public static final String PARTNER_ID_HEADER = "X-Partner-Id";
    private static final List<String> EXCLUDED_PATHS = List.of(
            "/partnerIds","/user","/role","/ecoApps");

    private final DatabaseClient db2DatabaseClient;

    public PartnerIdFilter(@Qualifier("db2DatabaseClient") DatabaseClient db2DatabaseClient) {
        this.db2DatabaseClient = db2DatabaseClient;
    }

    @Override
public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
    String path = exchange.getRequest().getPath().value();

    if (EXCLUDED_PATHS.stream().anyMatch(path::contains)) {
        return chain.filter(exchange);
    }

    List<String> partnerIdHeaders = exchange.getRequest().getHeaders().get(PARTNER_ID_HEADER);
    if (partnerIdHeaders == null || partnerIdHeaders.isEmpty()) {
        logger.warn("Missing partnerId header");
        return writeError(exchange, "Missing partnerId header");
    }

    String partnerId = partnerIdHeaders.get(0);

    Long partnerIdLong;
    try {
        partnerIdLong = Long.parseLong(partnerId);
    } catch (NumberFormatException e) {
        logger.warn("Invalid partnerId format (not a number): {}", partnerId);
        return writeError(exchange, "Invalid partnerId format: must be a number");
    }

    String sql = "SELECT 1 FROM raw_partners WHERE partner_id = $1 LIMIT 1";

    return db2DatabaseClient.sql(sql)
            .bind(0, partnerIdLong)
            .map(row -> row.get(0, Integer.class))
            .one()
            .flatMap(exists -> chain.filter(exchange))
            .switchIfEmpty(Mono.just("fallback").flatMap(f -> {
                return writeError(exchange, "PartnerId " + partnerId + " does not exist");
            }));
}


    private Mono<Void> writeError(ServerWebExchange exchange, String message) {
        exchange.getResponse().setStatusCode(HttpStatus.BAD_REQUEST);
        exchange.getResponse().getHeaders().setContentType(MediaType.APPLICATION_JSON);
        String json = "{\"error\": \"" + message.replace("\"", "\\\"") + "\"}";

        byte[] bytes = json.getBytes(StandardCharsets.UTF_8);
        DataBuffer buffer = exchange.getResponse().bufferFactory().wrap(bytes);
        logger.error(message);
        return exchange.getResponse().writeWith(Mono.just(buffer));
    }

}
