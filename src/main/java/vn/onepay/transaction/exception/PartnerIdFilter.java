package vn.onepay.transaction.exception;

import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;

import java.nio.charset.StandardCharsets;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.r2dbc.core.DatabaseClient;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.util.context.Context;

@Component
public class PartnerIdFilter implements WebFilter {
    private static final Logger logger = LoggerFactory.getLogger(PartnerIdFilter.class);
    public static final String PARTNER_ID_HEADER = "X-Partner-Id";
    public static final String USER_ID_HEADER = "X-User-Id";
    private static final List<String> EXCLUDED_PATHS = List.of(
            "/partnerIds", "/user", "/role", "/ecoApps", "/insert-daily");

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        String path = exchange.getRequest().getPath().value();
        logger.info("Checking headers: x-partner-id and x-user-id...");

        // Bỏ qua các path không cần validate
        if (EXCLUDED_PATHS.stream().anyMatch(path::contains)) {
            return chain.filter(exchange);
        }

        // Lấy và kiểm tra x-partner-id
        List<String> partnerIdHeaders = exchange.getRequest().getHeaders().get(PARTNER_ID_HEADER);
        if (partnerIdHeaders == null || partnerIdHeaders.isEmpty()) {
            logger.warn("Missing x-partner-id header");
            return writeError(exchange, 400, "Missing x-partner-id header");
        }

        String partnerId = partnerIdHeaders.get(0);
        Long partnerIdLong;
        try {
            partnerIdLong = Long.parseLong(partnerId);
        } catch (NumberFormatException e) {
            logger.warn("Invalid x-partner-id format (not a number): {}", partnerId);
            return writeError(exchange, 400, "Invalid x-partner-id format: must be a number");
        }

        // Lấy và kiểm tra x-user-id
        List<String> userIdHeaders = exchange.getRequest().getHeaders().get(USER_ID_HEADER);
        if (userIdHeaders == null || userIdHeaders.isEmpty()) {
            logger.warn("Missing x-user-id header");
            return writeError(exchange, 400, "Missing x-user-id header");
        }

        String userId = userIdHeaders.get(0);

        // Gắn cả 2 biến vào context
        return chain.filter(exchange)
                .contextWrite(ctx -> ctx
                        .put("partnerId", partnerIdLong)
                        .put("userId", userId));
    }

    private Mono<Void> writeError(ServerWebExchange exchange, int status, String message) {
        if (!exchange.getResponse().isCommitted()) {
            exchange.getResponse().setStatusCode(HttpStatus.valueOf(status));
            exchange.getResponse().getHeaders().setContentType(MediaType.APPLICATION_JSON);

            String json = String.format("{\"status\": %d, \"message\": \"%s\"}", status, message.replace("\"", "\\\""));
            byte[] bytes = json.getBytes(StandardCharsets.UTF_8);
            DataBuffer buffer = exchange.getResponse().bufferFactory().wrap(bytes);
            return exchange.getResponse().writeWith(Mono.just(buffer));
        }
        return Mono.empty();
    }

}
