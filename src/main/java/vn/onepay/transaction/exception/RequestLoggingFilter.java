package vn.onepay.transaction.exception;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

@Component("requestLoggingFilterInException")
public class RequestLoggingFilter implements WebFilter {

    private static final Logger logger = LoggerFactory.getLogger(RequestLoggingFilter.class);

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        // Lấy URI và phương thức của request
        String requestUri = exchange.getRequest().getURI().toString();
        HttpMethod method = exchange.getRequest().getMethod();

        // Log thông tin request
        logger.info("Incoming request: {} {}", method != null ? method.name() : "UNKNOWN", requestUri);

        // Log toàn bộ headers
        exchange.getRequest().getHeaders().forEach((name, values) -> {
            logger.info("Header: {} = {}", name, String.join(", ", values));
        });

        // Xử lý chain và log kết quả
        return chain.filter(exchange)
                .doOnSuccess(aVoid -> {
                    if (exchange.getResponse().getStatusCode() != null) {
                        logger.info("Response: {} {} - Status: {}",
                                method != null ? method.name() : "UNKNOWN",
                                requestUri,
                                exchange.getResponse().getStatusCode());
                    } else {
                        logger.warn("Response for {} {} not available",
                                method != null ? method.name() : "UNKNOWN",
                                requestUri);
                    }
                })
                .doOnError(error -> {
                    logger.error("Request error: {} {} - Exception: {}",
                            method != null ? method.name() : "UNKNOWN",
                            requestUri,
                            error.getMessage(), error);
                });
    }
}
