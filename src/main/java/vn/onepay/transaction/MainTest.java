package vn.onepay.transaction;

import java.math.BigDecimal;
import java.security.MessageDigest;

import org.springframework.boot.SpringApplication;

import vn.onepay.transaction.util.AmountUtil;
import vn.onepay.transaction.util.CheckPermissionUtil;
import vn.onepay.transaction.util.DateValidatorUtil;
import java.security.MessageDigest;

public class MainTest {
    public static void main(String[] args) {
        String amount2 =  AmountUtil.formatAmount(BigDecimal.valueOf(1000000), "VND");
        // Boolean allow = CheckPermissionUtil.allowRefundPartialForVietqr("{\"token_name\":\"1\",\"token_email\":\"0\",\"hide_qr_on_mobile\":\"0\",\"qr_version\":\"2\",\"n_avs_applepay\":0,\"vpc_response_version\":\"default\",\"referral_partner_id\":0,\"referral_type\":\"\",\"color_config\":\"#1D559F\",\"color_action\":\"\",\"atm_version\":\"1\",\"international_card_title\":\"\",\"ext_return_fields\":[]}");
		BigDecimal amount = DateValidatorUtil.mapJsonForAmount("{\"id\":\"111801401\",\"op\":\"replace\",\"path\":\"/refund\",\"value\":{\"merchant_id\":\"TESTPCI\",\"amount\":21000,\"currency\":\"VND\",\"note\":\"\",\"hisHash\":\"[]\",\"stringHash\":\"0h1Hrcahz2C-J3coH-2_ID_DKMnJ1kasULRjZlHtFWo\"},\"skipCallSynchronize\":false}");
        Boolean valid = CheckPermissionUtil.isInRange("2025-07-17T17:13:06", 7, 30);
		SpringApplication.run(TransactionAppApplication.class, args);		
	}
}
