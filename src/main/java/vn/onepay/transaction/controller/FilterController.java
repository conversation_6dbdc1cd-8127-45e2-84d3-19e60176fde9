package vn.onepay.transaction.controller;

import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;
import vn.onepay.transaction.dto.KeyValueDTO;
import vn.onepay.transaction.dto.RawPartnerDTO;
import vn.onepay.transaction.service.MerchantService;
import vn.onepay.transaction.service.PartnerService;
import vn.onepay.transaction.dto.ApiResponse;

@RestController
@RequestMapping
@RequiredArgsConstructor
public class FilterController {

        private final PartnerService partnerService;
        private final MerchantService merchantService;

        @GetMapping("/filter/transaction-status")
        public Mono<ResponseEntity<ApiResponse<List<KeyValueDTO>>>> getStatuses() {
                List<String> statuses = List.of(
                                "Successful", "Failed", "Processing",
                                "Fraud", "Waiting for authentication",
                                "Pending", "Incomplete");

                List<KeyValueDTO> result = statuses.stream()
                                .map(s -> new KeyValueDTO(s, s))
                                .toList();

                ApiResponse<List<KeyValueDTO>> response = new ApiResponse<>("SUCCESS", result.size(), result);
                return Mono.just(ResponseEntity.ok(response));
        }

        @GetMapping("/filter/transaction-types")
        public Mono<ResponseEntity<ApiResponse<List<KeyValueDTO>>>> getTransactionTypes() {
                List<KeyValueDTO> types = List.of(
                                new KeyValueDTO("Authorize", "Cấp phép (Authorize)"),
                                new KeyValueDTO("Capture", "Quyết toán (Capture)"),
                                new KeyValueDTO("Purchase", "Thanh toán (Purchase)"),
                                new KeyValueDTO("Refund", "Hoàn tiền (Refund)"),
                                new KeyValueDTO("Refund Capture", "Hoàn tiền quyết toán (Refund Capture)"),
                                new KeyValueDTO("Refund Dispute", "Hoàn tiền khiếu nại (Refund Dispute)"),
                                new KeyValueDTO("Void Authorize", "Hủy cấp phép (Void Authorize)"),
                                new KeyValueDTO("Void Capture", "Hủy quyết toán (Void Capture)"),
                                new KeyValueDTO("Void Purchase", "Hủy thanh toán (Void Purchase)"),
                                new KeyValueDTO("Void Refund", "Hủy hoàn tiền (Void Refund)"),
                                new KeyValueDTO("Void Refund Capture",
                                                "Hủy hoàn tiền quyết toán (Void Refund Capture)"));
                ApiResponse<List<KeyValueDTO>> response = new ApiResponse<>("SUCCESS", types.size(), types);
                return Mono.just(ResponseEntity.ok(response));
        }

        @GetMapping("/filter/payment-methods")
        public Mono<ResponseEntity<ApiResponse<List<KeyValueDTO>>>> getPaymentMethods() {
                List<KeyValueDTO> methods = List.of(
                                new KeyValueDTO("International", "Thẻ quốc tế (International Card)"),
                                new KeyValueDTO("Domestic", "Thẻ nội địa (Domestic Card)"),
                                new KeyValueDTO("Mobile App", "Ứng dụng di động (Mobile App)"),
                                new KeyValueDTO("BNPL", "Mua ngay trả sau (Buy Now, Pay Later)"),
                                new KeyValueDTO("Installment", "Trả góp (Installment)"),
                                new KeyValueDTO("VietQR", "VietQR"),
                                new KeyValueDTO("Apple Pay", "Apple Pay"),
                                new KeyValueDTO("Google Pay", "Google Pay"),
                                new KeyValueDTO("Samsung Pay", "Samsung Pay"),
                                new KeyValueDTO("PayPal", "PayPal"));
                ApiResponse<List<KeyValueDTO>> response = new ApiResponse<>("SUCCESS", methods.size(), methods);
                return Mono.just(ResponseEntity.ok(response));
        }

        @GetMapping("/filter/payment-sources")
        public Mono<ResponseEntity<ApiResponse<List<KeyValueDTO>>>> getPaymentSources() {
                List<KeyValueDTO> sources = List.of(
                                new KeyValueDTO("International", "Quốc tế (International)"),
                                new KeyValueDTO("Domestic", "Nội địa (Domestic)"),
                                new KeyValueDTO("Mobile App", "Ứng dụng di động (Mobile App)"),
                                new KeyValueDTO("BNPL", "BNPL"),
                                new KeyValueDTO("VietQR", "VietQR"));
                ApiResponse<List<KeyValueDTO>> response = new ApiResponse<>("SUCCESS", sources.size(), sources);
                return Mono.just(ResponseEntity.ok(response));
        }

        @GetMapping("/filter/merchantIds")
        public Mono<ResponseEntity<ApiResponse<List<KeyValueDTO>>>> getMerchantFilter() {

                return merchantService.getMerchantIdsByPartnerId()
                                .map(merchantIds -> {
                                        List<KeyValueDTO> result = merchantIds.stream()
                                                        .map(id -> new KeyValueDTO(id, id))
                                                        .toList();
                                        return new ApiResponse<>("SUCCESS", result.size(), result);
                                })
                                .map(ResponseEntity::ok);
        }

        @GetMapping("/filter/order-sources")
        public Mono<ResponseEntity<ApiResponse<List<KeyValueDTO>>>> getOrderSources() {
                List<KeyValueDTO> sources = List.of(
                                new KeyValueDTO("Website/ App", "Website / App"),
                                new KeyValueDTO("Invoice", "Invoice"),
                                new KeyValueDTO("Quicklink", "Quick Link"),
                                new KeyValueDTO("Paymentlink", "Payment Link"),
                                new KeyValueDTO("LDP", "Landing Page"));
                ApiResponse<List<KeyValueDTO>> response = new ApiResponse<>("SUCCESS", sources.size(), sources);
                return Mono.just(ResponseEntity.ok(response));
        }

        @GetMapping("/partnerIds")
        public Mono<ResponseEntity<ApiResponse<List<RawPartnerDTO>>>> getPartnerIds() {
                return partnerService.getRawPartners();
        }

        @GetMapping("/filter/request-refund-status")
        public Mono<ResponseEntity<ApiResponse<List<KeyValueDTO>>>> getRefundRequestStatuses() {
                List<KeyValueDTO> statuses = List.of(
                                new KeyValueDTO("Waiting for Merchant's Approval", "Chờ đơn vị duyệt"),
                                new KeyValueDTO("Merchant Rejected", "Đơn vị từ chối"),
                                new KeyValueDTO("Waiting for OnePay's Approval", "Chờ OnePay duyệt"),
                                new KeyValueDTO("OnePay Rejected", "OnePay từ chối"),
                                new KeyValueDTO("Merchant Approved", "Đơn vị phê duyệt"),
                                new KeyValueDTO("OnePay Approved", "OnePay phê duyệt"));

                ApiResponse<List<KeyValueDTO>> response = new ApiResponse<>("SUCCESS", statuses.size(), statuses);
                return Mono.just(ResponseEntity.ok(response));
        }

}
