package vn.onepay.transaction.controller;

import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import reactor.core.publisher.Mono;
import vn.onepay.transaction.constant.DummyData;
import vn.onepay.transaction.constant.DummyData.EcoAppDTO;
import vn.onepay.transaction.constant.DummyData.EcosystemDTO;
import vn.onepay.transaction.constant.DummyData.ModuleDTO;
import vn.onepay.transaction.constant.DummyData.PermissionDTO;
import vn.onepay.transaction.constant.DummyData.RoleDTO;
import vn.onepay.transaction.constant.DummyData.UserDTO;
import vn.onepay.transaction.dto.ApiResponse;
import vn.onepay.transaction.dto.CreateRoleRequest;

@RestController
@RequestMapping("")
public class PermissionController {
    // 1. GET /roles
    @GetMapping("/roles")
    public Mono<ResponseEntity<ApiResponse<List<RoleDTO>>>> getRoles() {
        List<RoleDTO> roles = List.of(
                new RoleDTO("r1", "staff", "Nhân viên", "Vai trò dành cho nhân viên vận hành cơ bản"),
                new RoleDTO("r2", "technician", "Kỹ thuật", "Vai trò dành cho bộ phận kỹ thuật, vận hành hệ thống"),
                new RoleDTO("r3", "finance", "Tài chính/Kế toán", "Vai trò truy cập các báo cáo và dữ liệu tài chính"),
                new RoleDTO("r4", "manager", "Quản lý", "Vai trò dành cho người quản lý cấp cao"));
        return Mono.just(ResponseEntity.ok(new ApiResponse<>("success", 4, roles)));
    }

    // 2. GET /roles/{roleId}
    @GetMapping("/role/{id}")
    public Mono<ResponseEntity<ApiResponse<List<EcosystemDTO>>>>  getRoleDetail(@PathVariable String id) {
        if (!id.equalsIgnoreCase("r1")) {
            return Mono.just(ResponseEntity.notFound().build());
        }

        List<EcosystemDTO> ecosystems = List.of(
                new EcosystemDTO("eco_tx", "transaction", List.of(
                        new ModuleDTO("mod_tx_dash", "dashboard", List.of(
                                new PermissionDTO("perm_tx_dash_revenue", "view_dashboard_revenue"),
                                new PermissionDTO("perm_tx_dash_service", "view_dashboard_service"))),
                        new ModuleDTO("mod_tx_list", "list", List.of(
                                new PermissionDTO("perm_tx_list_view", "view_transaction_list"),
                                new PermissionDTO("perm_tx_list_filter", "filter_transaction"))),
                        new ModuleDTO("mod_tx_report", "report", List.of(
                                new PermissionDTO("perm_tx_report_view", "view_transaction_report"))))),
                new EcosystemDTO("eco_pc", "paycollect", List.of(
                        new ModuleDTO("mod_pc_dash", "dashboard", List.of(
                                new PermissionDTO("perm_pc_dash_revenue", "view_dashboard_revenue"),
                                new PermissionDTO("perm_pc_dash_collection", "view_dashboard_collection"))),
                        new ModuleDTO("mod_pc_list", "list", List.of(
                                new PermissionDTO("perm_pc_list_view", "view_paycollect_list"),
                                new PermissionDTO("perm_pc_list_filter", "filter_paycollect"))),
                        new ModuleDTO("mod_pc_report", "report", List.of(
                                new PermissionDTO("perm_pc_report_view", "view_paycollect_report"))))));
        
        return Mono.just(ResponseEntity.ok(new ApiResponse<>("success", 2, ecosystems)));
    }

    // 3. POST /roles/save
    @PostMapping(value = "/role", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public Mono<ApiResponse<String>> createRole(@RequestBody CreateRoleRequest request) {
        String message = "Role " + request.code + " created successfully!";
        return Mono.just(new ApiResponse<>("success", 1, message));
    }

    // 4. GET /ecosystem/apps
    @GetMapping("/ecoApps")
    public Mono<ApiResponse<List<EcoAppDTO>>> getEcoApps() {
        return Mono.just(new ApiResponse<>("success", 2, Arrays.asList(
                DummyData.transactionApp(),
                DummyData.payCollectApp())));
    }

    // 5. GET /users
    @GetMapping(value = "/users", produces = MediaType.APPLICATION_JSON_VALUE)
    public Mono<ApiResponse<List<UserDTO>>> getUsers() {
        return Mono.just(new ApiResponse<>("success", DummyData.userList().size(), DummyData.userList()));
    }

    // 6. GET /users/{email}
    @GetMapping("/user/{email}")
    public Mono<ResponseEntity<ApiResponse<UserDTO>>> getUserByEmail(@PathVariable String email) {
        return Mono.justOrEmpty(DummyData.userList().stream()
                .filter(user -> user.email().equalsIgnoreCase(email))
                .findFirst())
                .map(user -> ResponseEntity.ok(new ApiResponse<>("success", 1, user)))
                .defaultIfEmpty(ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new ApiResponse<>("success", 0, null)));
    }

    // 7. POST /permissions
    @PostMapping("/user")
    public Mono<ResponseEntity<ApiResponse<UserDTO>>> createUser(@RequestBody UserDTO userRequest) {
        // Validate cơ bản
        if (userRequest.userName() == null || userRequest.userName().isBlank()
                || userRequest.email() == null || userRequest.email().isBlank()) {
            return Mono.just(ResponseEntity
                    .badRequest()
                    .body(null));
        }

        // Nếu có functions → roles = null. Nếu không → giữ roles, functions = null
        UserDTO sanitizedUser = new UserDTO(
                userRequest.userName(),
                userRequest.fullName(),
                userRequest.email(),
                userRequest.phone(),
                userRequest.role(),
                userRequest.merchantIds(),
                userRequest.isActive(),
                userRequest.createdAt(),
                userRequest.lastLogin(),
                userRequest.functions() != null && !userRequest.functions().isEmpty() ? userRequest.functions() : null,
                (userRequest.functions() != null && !userRequest.functions().isEmpty()) ? null : userRequest.roles()
        );

        return Mono.just(ResponseEntity.ok(new ApiResponse<>("success", 0, sanitizedUser)));
    }
}
