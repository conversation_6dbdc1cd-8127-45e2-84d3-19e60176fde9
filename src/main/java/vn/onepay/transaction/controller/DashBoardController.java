package vn.onepay.transaction.controller;

import java.util.List;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;
import vn.onepay.transaction.constant.IntervalType;
import vn.onepay.transaction.dto.ApiResponse;
import vn.onepay.transaction.dto.DashboardSummaryDTO;
import vn.onepay.transaction.dto.RevenuePercentDTO;
import vn.onepay.transaction.dto.RevenueTrendDTO;
import vn.onepay.transaction.service.DashboardService;

@RestController
@RequestMapping("/dashboard")
@RequiredArgsConstructor
public class DashBoardController {
        private final DashboardService dashboardService;

        @GetMapping("/summary")
        public Mono<DashboardSummaryDTO> getSummary(
                        @RequestParam(defaultValue = "VND") String currency,
                        @RequestParam(required = true) String fromDate,
                        @RequestParam(required = true) String toDate,
                        @RequestParam(required = false) String merchantId,
                        @RequestParam(required = false) Boolean promotion,
                        @RequestParam(required = false) String paymentMethod,
                        @RequestParam(required = false) String orderSource) {

                if (merchantId != null && merchantId.trim().isEmpty()) {
                        merchantId = null;
                }
                return dashboardService.getSummary(fromDate, toDate, currency, merchantId, promotion,
                                paymentMethod, orderSource);

        }

        @GetMapping(value = "/revenue-trend")
        public Mono<ResponseEntity<ApiResponse<List<RevenueTrendDTO>>>> getRevenueTrend(
                        @RequestParam(defaultValue = "VND") String currency,
                        @RequestParam(required = false, defaultValue = "MONTHLY") String interval,
                        @RequestParam(required = false) String fromDate,
                        @RequestParam(required = false) String toDate,
                        @RequestParam(required = false) String merchantId,
                        @RequestParam(required = false) Boolean promotion,
                        @RequestParam(required = false) String paymentMethod,
                        @RequestParam(required = false) String orderSource) {
                IntervalType intervalType = IntervalType.fromString(interval);
                if (merchantId != null && merchantId.trim().isEmpty()) {
                        merchantId = null;
                }
                return dashboardService
                                .getRevenueTrend(fromDate, toDate, currency, merchantId, intervalType,
                                                promotion, paymentMethod, orderSource)
                                .collectList()
                                .map(list -> ResponseEntity.ok(new ApiResponse<>("SUCCESS", list.size(), list)));
        }

        @GetMapping(value = "/revenue")
        public Mono<ResponseEntity<ApiResponse<List<RevenuePercentDTO>>>> getRevenue(
                        @RequestParam(defaultValue = "VND") String currency,
                        @RequestParam(required = true) String fromDate,
                        @RequestParam(required = true) String toDate,
                        @RequestParam(required = true) String groupBy,
                        @RequestParam(required = false) String merchantId,
                        @RequestParam(required = false) Boolean promotion,
                        @RequestParam(required = false) String paymentMethod,
                        @RequestParam(required = false) String orderSource) {

                if (merchantId != null && merchantId.trim().isEmpty()) {
                        merchantId = null;
                }

                return dashboardService
                                .getRevenueGroupBy(fromDate, toDate, currency, merchantId, promotion,
                                                paymentMethod, orderSource, groupBy)
                                .collectList()
                                .map(list -> ResponseEntity.ok(new ApiResponse<>("SUCCESS", list.size(), list)));
        }
    
}
