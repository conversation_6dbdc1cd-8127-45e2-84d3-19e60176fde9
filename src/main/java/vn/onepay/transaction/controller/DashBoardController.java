package vn.onepay.transaction.controller;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import vn.onepay.transaction.constant.IntervalType;
import vn.onepay.transaction.dto.ApiResponse;
import vn.onepay.transaction.dto.DashboardSummaryDTO;
import vn.onepay.transaction.dto.RevenueMerchantDTO;
import vn.onepay.transaction.dto.RevenueTrendDTO;
import vn.onepay.transaction.service.DashboardService;

@RestController
@RequestMapping("/dashboard")
@RequiredArgsConstructor
public class DashBoardController {
        private final DashboardService dashboardService;

        @GetMapping("/summary")
        public Mono<DashboardSummaryDTO> getSummary(ServerWebExchange exchange,
                        @RequestParam(defaultValue = "VND") String currency,
                        @RequestParam(required = true) String fromDate,
                        @RequestParam(required = true) String toDate,
                        @RequestParam(required = false) String merchantId) {

                if (merchantId != null && merchantId.trim().isEmpty()) {
                        merchantId = null;
                }
                return dashboardService.getSummary(exchange,fromDate, toDate, currency, merchantId);

        }

        @GetMapping(value = "/revenue-trend")
        public Mono<ResponseEntity<ApiResponse<List<RevenueTrendDTO>>>> getRevenueTrend(ServerWebExchange exchange,
                        @RequestParam(defaultValue = "VND") String currency,
                        @RequestParam(required = false, defaultValue = "MONTHLY") String interval,
                        @RequestParam(required = false) String fromDate,
                        @RequestParam(required = false) String toDate,
                        @RequestParam(required = false) String merchantId) {
                IntervalType intervalType = IntervalType.fromString(interval);
                if (merchantId != null && merchantId.trim().isEmpty()) {
                        merchantId = null;
                }
                return dashboardService.getRevenueTrend(exchange,fromDate, toDate, currency, merchantId, intervalType).collectList()
                .map(list -> ResponseEntity.ok(new ApiResponse<>("SUCCESS", list.size(), list)));
        }

        @GetMapping(value = "/revenue-merchant")
        public Mono<ResponseEntity<ApiResponse<List<RevenueMerchantDTO>>>> getRevenueMerchantID(ServerWebExchange exchange,
                        @RequestParam(defaultValue = "VND") String currency,
                        @RequestParam String fromDate,
                        @RequestParam String toDate,
                        @RequestParam(required = false) String merchantId) {

                if (merchantId != null && merchantId.trim().isEmpty()) {
                        merchantId = null;
                }

                return dashboardService.getRevenueByMerhantId(exchange,fromDate, toDate, currency, merchantId)
                                .collectList()
                                .map(list -> ResponseEntity.ok(new ApiResponse<>("SUCCESS", list.size(), list)));
        }

        @GetMapping(value = "/revenue-payment-method")
        public Mono<ResponseEntity<ApiResponse<List<Map<String, Object>>>>> getPaymentMethod(ServerWebExchange exchange,
                        @RequestParam(defaultValue = "VND") String currency,
                        @RequestParam(required = false) String fromDate,
                        @RequestParam(required = false) String toDate,
                        @RequestParam(required = false) String merchantId) {

                List<Map<String, Object>> data = List.of(
                                Map.of("paymentMethod", "International", "transactionVolume",
                                                new BigDecimal("300000000"), "percentage", 50),
                                Map.of("paymentMethod", "Domestic", "transactionVolume", new BigDecimal("300000000"),
                                                "percentage", 50));

                return Mono.just(ResponseEntity.ok(new ApiResponse<>("SUCCESS", data.size(), data)));
        }

        @GetMapping(value = "/revenue-order-source")
        public Mono<ResponseEntity<ApiResponse<List<Map<String, Object>>>>> getOrderSource(ServerWebExchange exchange,
                        @RequestParam(defaultValue = "VND") String currency,
                        @RequestParam(required = false) String fromDate,
                        @RequestParam(required = false) String toDate,
                        @RequestParam(required = false) String merchantId) {

                List<Map<String, Object>> data = List.of(
                                Map.of("orderSource", "Website / App", "transactionVolume", new BigDecimal("1000000"),
                                                "percentage", 10),
                                Map.of("orderSource", "Invoice", "transactionVolume", new BigDecimal("1500000"),
                                                "percentage", 15),
                                Map.of("orderSource", "Quick Link", "transactionVolume", new BigDecimal("2000000"),
                                                "percentage", 20),
                                Map.of("orderSource", "Payment Link", "transactionVolume", new BigDecimal("2500000"),
                                                "percentage", 25),
                                Map.of("orderSource", "Landing Page", "transactionVolume", new BigDecimal("3000000"),
                                                "percentage", 30));

                return Mono.just(ResponseEntity.ok(new ApiResponse<>("SUCCESS", data.size(), data)));
        }
}
