package vn.onepay.transaction.controller;

import java.util.Map;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;
import vn.onepay.transaction.dto.PagedResponse;
import vn.onepay.transaction.dto.RequestRefundDetail;
import vn.onepay.transaction.dto.RequestRefundResponse;
import vn.onepay.transaction.service.DownloadTaskService;
import vn.onepay.transaction.service.RequestRefundService;
import vn.onepay.transaction.constant.IConstants;
import vn.onepay.transaction.dto.ApiResponse;

@RestController
@RequestMapping("/request-refund")
@RequiredArgsConstructor
public class RequestRefundController {
    private final RequestRefundService refundService;
    private final DownloadTaskService downloadTaskService;

    @GetMapping("/list")
    public Mono<ApiResponse<PagedResponse<RequestRefundResponse>>> getRefundRequests(
            ServerWebExchange exchange,
            @RequestParam(required = true) String fromDate,
            @RequestParam(required = true) String toDate,
            @RequestParam(required = false) String merchantId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String searchKeyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdDate") String sortField,
            @RequestParam(defaultValue = "desc") String sortOrder) {
        return refundService.getRefundRequests(exchange,fromDate, toDate, merchantId, status,searchKeyword, page, size, sortField, sortOrder);
    }

    @GetMapping("/detail/{docId}")
    public Mono<ResponseEntity<RequestRefundDetail>> getRefundRequestDetail(@PathVariable String docId) {
        RequestRefundDetail response = refundService.getRefundRequestDetail(docId);

        return Mono.just(ResponseEntity.ok(response));
    }

    @GetMapping("/export")
    public Mono<Map<String, String>> createExportTask(@RequestParam(required = true) String fromDate,
            @RequestParam(required = true) String toDate,
            @RequestParam(defaultValue = "ALL") String merchantId,
            @RequestParam(defaultValue = "DAILY") String interval,
            @RequestParam(defaultValue = "VND") String currency) {
        Map<String, Object> params = Map.of(
                "fromDate", fromDate,
                "toDate", toDate,
                "merchantId", merchantId,
                "interval", interval,
                "currency", currency);

        return downloadTaskService.createTask(IConstants.TYPE_REQUEST_REFUND, params).map(task -> Map.of(
                "fileName", task.getFileName(),
                "fileHashName", task.getFileHashName(),
                "status","ok"
                ));
    }
}
