package vn.onepay.transaction.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.server.ServerWebExchange;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;
import vn.onepay.transaction.dto.PagedResponse;
import vn.onepay.transaction.dto.RequestRefundDetail;
import vn.onepay.transaction.dto.RequestRefundResponse;
import vn.onepay.transaction.dto.TransactionHistoryDTO;
import vn.onepay.transaction.service.DownloadTaskService;
import vn.onepay.transaction.service.RequestRefundService;
import vn.onepay.transaction.service.TransactionOldService;
import vn.onepay.transaction.service.TransactionService;
import vn.onepay.transaction.constant.IConstants;
import vn.onepay.transaction.dto.ApiResponse;

@RestController
@RequestMapping("/request-refund")
@RequiredArgsConstructor
public class RequestRefundController {
    private final RequestRefundService refundService;
    private final DownloadTaskService downloadTaskService;
    private final TransactionService transactionService;
    private final TransactionOldService transactionOldService;
    @GetMapping("/list")
    public Mono<ApiResponse<PagedResponse<RequestRefundResponse>>> getRefundRequests(
            ServerWebExchange exchange,
            @RequestParam(required = true) String fromDate,
            @RequestParam(required = true) String toDate,
            @RequestParam(required = false) String merchantId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String searchKeyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdDate") String sortField,
            @RequestParam(defaultValue = "desc") String sortOrder) {
        String userId = exchange.getRequest().getHeaders().getFirst("X-User-Id") != null
                ? exchange.getRequest().getHeaders().getFirst("X-User-Id")
                : "-1";
        
        return transactionOldService.checkPermission(IConstants.TRANSACTION_APP_TRANS_MGMT_VIEW)
            .flatMap(hasPermission -> {
                if (!hasPermission) {
                    return Mono.error(new ResponseStatusException(HttpStatus.FORBIDDEN, "Permission denied"));
                }
                
                return transactionOldService.checkUserExistsInPostgres(userId)
                .flatMap(type -> {
                    if ("1".equalsIgnoreCase(type)) {
                        return refundService.getRefundRequests(fromDate, toDate, merchantId, status,searchKeyword, page, size, sortField, sortOrder);
                    } else {
                        return transactionOldService.getRefundRequestsOld(fromDate, toDate, merchantId, status, searchKeyword, page, size, sortField, sortOrder, userId);
                    }
                });
            });
         
    }

    @GetMapping("/detail/{docId}")
    public Mono<ResponseEntity<RequestRefundDetail>> getRefundRequestDetail(@PathVariable String docId) {
        RequestRefundDetail response = refundService.getRefundRequestDetail(docId);

        return Mono.just(ResponseEntity.ok(response));
    }

    @GetMapping("/export")
    public Mono<Map<String, String>> createExportTask(
            ServerWebExchange exchange,
            @RequestParam(required = true) String fromDate,
            @RequestParam(required = true) String toDate,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String searchKeyword,
            @RequestParam(defaultValue = "ALL") String merchantId) {
        Map<String, Object> params = new HashMap<>();
        params.put("fromDate", fromDate);
        params.put("toDate", toDate);
            if (merchantId != null)
                params.put("merchantId", merchantId);
            if (status != null)
                params.put("status", status);
            if (searchKeyword != null)
                params.put("searchKeyword", searchKeyword);

        return transactionOldService.checkPermission(IConstants.TRANSACTION_APP_TRANS_MGMT_DOWNLOAD)
            .flatMap(hasPermission -> {
                if (!hasPermission) {
                    return Mono.error(new ResponseStatusException(HttpStatus.FORBIDDEN, "Permission denied"));
                }

                return downloadTaskService.createTask(exchange,IConstants.TYPE_REQUEST_REFUND, params).map(task -> Map.of(
                        "file_name", task.getFileName(),
                        "file_hash_name", task.getFileHashName(),
                        "status", "ok"));
            });
    }

    @GetMapping("/history/{parentTransactionId}")
    public Mono<ApiResponse<List<TransactionHistoryDTO>>> getTransactionHistory(
            @PathVariable String parentTransactionId) {

        return transactionService.getTransactionHistory(parentTransactionId,IConstants.TYPE_REQUEST_REFUND);
    }
}
