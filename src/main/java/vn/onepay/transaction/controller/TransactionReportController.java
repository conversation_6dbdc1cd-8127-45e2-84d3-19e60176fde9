package vn.onepay.transaction.controller;

import java.util.Map;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;
import vn.onepay.transaction.constant.IConstants;
import vn.onepay.transaction.dto.PagedResponse;
import vn.onepay.transaction.dto.TransactionReportDTO;
import vn.onepay.transaction.dto.TransactionReportResponse;
import vn.onepay.transaction.service.DownloadTaskService;
import vn.onepay.transaction.service.TransactionReportService;

@RestController
@RequestMapping("/report")
@RequiredArgsConstructor
public class TransactionReportController {

    private final TransactionReportService service;
    private final DownloadTaskService downloadTaskService;

    @GetMapping("/list")
    public Mono<TransactionReportResponse> getTransactionReports(ServerWebExchange exchange,
            @RequestParam(required = true) String fromDate,
            @RequestParam(required = true) String toDate,
            @RequestParam(defaultValue = "ALL") String merchantId,
            @RequestParam(defaultValue = "DAILY") String interval,
            @RequestParam(defaultValue = "VND") String currency,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        return service.getTransactionReports(exchange,fromDate, toDate, interval, merchantId, currency, page, size);
    }

    @GetMapping("/export")
    public Mono<Map<String, String>> createExportTask(@RequestParam(required = true) String fromDate,
            @RequestParam(required = true) String toDate,
            @RequestParam(defaultValue = "ALL") String merchantId,
            @RequestParam(defaultValue = "DAILY") String interval,
            @RequestParam(defaultValue = "VND") String currency) {
        Map<String, Object> params = Map.of(
                "fromDate", fromDate,
                "toDate", toDate,
                "merchantId", merchantId,
                "interval", interval,
                "currency", currency);

        return downloadTaskService.createTask(IConstants.TYPE_TRANSACTION_REPORT, params).map(task -> Map.of(
                "fileName", task.getFileName(),
                "fileHashName", task.getFileHashName(),
                "status","ok"
                ));
    }
}
