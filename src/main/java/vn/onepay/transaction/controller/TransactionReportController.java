package vn.onepay.transaction.controller;

import java.util.HashMap;
import java.util.Map;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;
import vn.onepay.transaction.constant.IConstants;
import vn.onepay.transaction.dto.ReportResponse;
import vn.onepay.transaction.dto.TransactionReportDTO;
import vn.onepay.transaction.service.DownloadTaskService;
import vn.onepay.transaction.service.TransactionReportService;

@RestController
@RequestMapping("/report")
@RequiredArgsConstructor
public class TransactionReportController {

    private final TransactionReportService service;
    private final DownloadTaskService downloadTaskService;

    @GetMapping("/list")
    public Mono<ReportResponse<TransactionReportDTO>> getTransactionReports(
            @RequestParam(required = true) String fromDate,
            @RequestParam(required = true) String toDate,
            @RequestParam(required = false) String paymentMethod,
            @RequestParam(required = false) String orderSource,
            @RequestParam(required = false) Boolean promotion,
            @RequestParam(defaultValue = "ALL") String merchantId,
            @RequestParam(defaultValue = "DAILY") String interval,
            @RequestParam(defaultValue = "VND") String currency,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        return service.getTransactionReports(fromDate, toDate, interval, merchantId, currency, page, size, promotion,
        paymentMethod, orderSource);
    }

    @GetMapping("/export")
    public Mono<Map<String, String>> createExportTask(
            ServerWebExchange exchange,
            @RequestParam(required = true) String fromDate,
            @RequestParam(required = true) String toDate,
            @RequestParam(required = false) String paymentMethod,
            @RequestParam(required = false) String orderSource,
            @RequestParam(required = false) Boolean promotion,
            @RequestParam(defaultValue = "ALL") String merchantId,
            @RequestParam(defaultValue = "DAILY") String interval,
            @RequestParam(defaultValue = "VND") String currency) {
        Map<String, Object> params = new HashMap<>();
        params.put("fromDate", fromDate);
        params.put("toDate", toDate);
        params.put("interval", interval);
        params.put("currency", currency);
            if (paymentMethod != null)
                params.put("paymentMethod", paymentMethod);
            if (merchantId != null)
                params.put("merchantId", merchantId);
            if (orderSource != null)
                params.put("orderSource", orderSource);
            if (promotion != null)
                params.put("isPromotion", promotion);
        return downloadTaskService.createTask(exchange,IConstants.TYPE_TRANSACTION_REPORT, params).map(task -> Map.of(
                "file_name", task.getFileName(),
                "file_hash_name", task.getFileHashName(),
                "status","ok"
                ));
    }
}
