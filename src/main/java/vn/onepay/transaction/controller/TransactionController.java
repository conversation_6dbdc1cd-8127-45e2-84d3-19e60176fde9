package vn.onepay.transaction.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.server.ServerWebExchange;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;
import vn.onepay.transaction.constant.IConstants;
import vn.onepay.transaction.dto.ApiResponse;
import vn.onepay.transaction.dto.PagedResponse;
import vn.onepay.transaction.dto.TransactionDTO;
import vn.onepay.transaction.dto.TransactionDetailResponse;
import vn.onepay.transaction.dto.TransactionHistoryDTO;
import vn.onepay.transaction.service.DownloadTaskService;
import vn.onepay.transaction.service.TransactionOldService;
import vn.onepay.transaction.service.TransactionService;
import vn.onepay.transaction.util.DateValidatorUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping
@RequiredArgsConstructor
public class TransactionController {

    private final TransactionService transactionService;
    private final TransactionOldService transactionOldService;
    private final DownloadTaskService downloadTaskService;

    @GetMapping("/transaction/detail/{docId}")
    public Mono<TransactionDetailResponse> getTransactionDetail(@PathVariable String docId) {
        return transactionService.getTransactionDetail( docId);
    }

    @GetMapping("/transaction/list")
    public Mono<ApiResponse<PagedResponse<TransactionDTO>>> getTransactions(
            ServerWebExchange exchange,
            @RequestParam(required = true) String fromDate,
            @RequestParam(required = true) String toDate,
            @RequestParam(required = false) String merchantId,
            @RequestParam(required = false) String transId,
            @RequestParam(required = false) String paymentMethod,
            @RequestParam(required = false) String transactionType,
            @RequestParam(required = false) String transactionStatus,
            @RequestParam(required = false) String searchKeyword,
            @RequestParam(required = false) Boolean promotion,
            @RequestParam(required = false) String orderSource,
            @RequestParam(defaultValue = "ALL") String searchType,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdDate") String sortField,
            @RequestParam(defaultValue = "desc") String sortOrder) {
        DateValidatorUtil.validateDateISORange(fromDate, toDate);
        if (!IConstants.ALLOWED_SIZES.contains(size)) {
            return Mono.just(ApiResponse.error("Invalid 'size'. Allowed values: 20, 50, 100, 200, 500."));
        }
        String userId = exchange.getRequest().getHeaders().getFirst("X-User-Id") != null
                ? exchange.getRequest().getHeaders().getFirst("X-User-Id")
                : "-1";
        return transactionOldService.checkUserExistsInPostgres(userId)
                .flatMap(type -> {
                    if ("1".equalsIgnoreCase(type)) {
                        return transactionService.getTransactions( fromDate, toDate, merchantId, transId,
                                paymentMethod,
                                transactionStatus, transactionType,
                                searchType, searchKeyword, page, size, sortField, sortOrder, promotion, orderSource);
                    } else {
                        return transactionOldService.callSearchGeneralReport(fromDate, toDate, merchantId, transId,
                                paymentMethod, transactionStatus, transactionType, page, size, sortField, sortOrder,
                                userId);
                    }
                });
    }

    @GetMapping("/transaction/history/{transactionId}")
    public Mono<ApiResponse<List<TransactionHistoryDTO>>> getTransactionHistory( @PathVariable String transactionId) {
        return transactionService.getTransactionHistory(transactionId,IConstants.TYPE_TRANSACTION);
    }

    @GetMapping("/transaction/export")
    public Mono<Map<String, String>> createExportTask( 
            ServerWebExchange exchange,
            @RequestParam(required = true) String fromDate,
            @RequestParam(required = true) String toDate,
            @RequestParam(required = false) String merchantId,
            @RequestParam(required = false) String transId,
            @RequestParam(required = false) String paymentMethod,
            @RequestParam(required = false) String transactionType,
            @RequestParam(required = false) String transactionStatus,
            @RequestParam(required = false) String searchKeyword,
            @RequestParam(required = false) Boolean promotion,
            @RequestParam(required = false) String orderSource,
            @RequestParam(defaultValue = "ALL") String searchType) {
        DateValidatorUtil.validateDateISORange(fromDate, toDate);

        Map<String, Object> params = new HashMap<>();
        params.put("fromDate", fromDate);
        params.put("toDate", toDate);
        params.put("searchType", searchType);
        if (merchantId != null)
            params.put("merchantId", merchantId);
        if (transId != null)
            params.put("transId", transId);
        if (paymentMethod != null)
            params.put("paymentMethod", paymentMethod);
        if (transactionType != null)
            params.put("transactionType", transactionType);
        if (transactionStatus != null)
            params.put("transactionStatus", transactionStatus);
        if (searchKeyword != null)
            params.put("searchKeyword", searchKeyword);
        if (orderSource != null)
            params.put("orderSource", orderSource);
        if (promotion != null)
            params.put("isPromotion", promotion);

        return downloadTaskService.createTask(exchange,IConstants.TYPE_TRANSACTION, params).map(task -> Map.of(
                "file_name", task.getFileName(),
                "file_hash_name", task.getFileHashName(),
                "status", "ok"));
    }

    @GetMapping("/transaction/detail")
    public Mono<ResponseEntity<TransactionDetailResponse>> getTransactionDetail(
        ServerWebExchange exchange,
        @RequestParam(required = true) String transactionId,
        @RequestParam(required = true) String transactionType,
        @RequestParam(required = true) String paymentMethod,
        @RequestParam(required = false) String paymentChannel) {
        if ("UPOS".equalsIgnoreCase(paymentMethod) && (paymentChannel == null || paymentChannel.isEmpty())) {
            return Mono.error(new ResponseStatusException(HttpStatus.NOT_FOUND, "PaymentChannel is not null for UPOS"));
        }
        String userId = exchange.getRequest().getHeaders().getFirst("X-User-Id") != null ? exchange.getRequest().getHeaders().getFirst("X-User-Id") : "-1";
        TransactionDetailResponse response = transactionOldService.getTransactionDetail(transactionId, paymentMethod, transactionType, paymentChannel, userId);
        if (response == null || response.getMerchantInformation() == null || response.getMerchantInformation().getMerchantId() == null) {
            return Mono.error(new ResponseStatusException(HttpStatus.NOT_FOUND, "Transaction ID not found"));
        }
        return transactionOldService
            .checkMerchantIdByUserId(userId, response.getMerchantInformation().getMerchantId(), paymentMethod)
            .flatMap(hasPermission -> {
                if (!hasPermission) {
                    return Mono.error(new ResponseStatusException(HttpStatus.FORBIDDEN, "Permission denied"));
                }
                return Mono.just(ResponseEntity.ok(response));
            });
    }

    @PatchMapping("/transaction/refund/{transactionId}")
    public Mono<ResponseEntity<Map<String, String>>> refundTransaction(
            ServerWebExchange exchange,
            @PathVariable String transactionId,
            @RequestBody String jsonData) {
        String userId = exchange.getRequest().getHeaders().getFirst("X-User-Id") != null ? exchange.getRequest().getHeaders().getFirst("X-User-Id") : "-1";
        String realIp = exchange.getRequest().getHeaders().getFirst("X-Real-IP") != null ? exchange.getRequest().getHeaders().getFirst("X-Real-IP") : "127.0.0.1";
        String service = DateValidatorUtil.mapJsonForService(jsonData);
        if (service == null || service.isEmpty()) {
            return Mono.error(new ResponseStatusException(HttpStatus.METHOD_NOT_ALLOWED, "service not found"));
        }
        TransactionDetailResponse response = transactionOldService.getTransactionDetail(transactionId, service, "Purchase", null, userId);
        if (response == null || response.getMerchantInformation() == null || response.getMerchantInformation().getMerchantId() == null) {
            return Mono.error(new ResponseStatusException(HttpStatus.NOT_FOUND, "Transaction ID not found"));
        }
        return transactionOldService
            .checkMerchantIdByUserId(userId, response.getMerchantInformation().getMerchantId(), service)
            .flatMap(hasPermission -> {
                if (!hasPermission) {
                    return Mono.error(new ResponseStatusException(HttpStatus.FORBIDDEN, "Permission denied"));
                }
                return transactionOldService.refundTransaction(transactionId, service, jsonData, userId, realIp)
                    .map(result -> {
                        if ("Fail".equals(result.get("message"))) {
                            return ResponseEntity.status(500).body(result);
                        } else {
                            return ResponseEntity.ok(result);
                        }
                    });
            });
    }

    @PatchMapping("/transaction/capture/{transactionId}")
    public Mono<ResponseEntity<Map<String, String>>> captureTransaction(
            ServerWebExchange exchange,
            @PathVariable String transactionId,
            @RequestBody String jsonData) {
        String userId = exchange.getRequest().getHeaders().getFirst("X-User-Id") != null ? exchange.getRequest().getHeaders().getFirst("X-User-Id") : "-1";
        String realIp = exchange.getRequest().getHeaders().getFirst("X-Real-IP") != null ? exchange.getRequest().getHeaders().getFirst("X-Real-IP") : "127.0.0.1";
        String service = DateValidatorUtil.mapJsonForService(jsonData);
        if (service == null || service.isEmpty()) {
            return Mono.error(new ResponseStatusException(HttpStatus.METHOD_NOT_ALLOWED, "service not found"));
        }
        TransactionDetailResponse response = transactionOldService.getTransactionDetail(transactionId, service, "Purchase", null, userId);
        if (response == null || response.getMerchantInformation() == null || response.getMerchantInformation().getMerchantId() == null) {
            return Mono.error(new ResponseStatusException(HttpStatus.NOT_FOUND, "Transaction ID not found"));
        }

        return transactionOldService
            .checkMerchantIdByUserId(userId, response.getMerchantInformation().getMerchantId(), service)
            .flatMap(hasPermission -> {
                if (!hasPermission) {
                    return Mono.error(new ResponseStatusException(HttpStatus.FORBIDDEN, "Permission denied"));
                }
                return transactionOldService.captureTransaction(transactionId, response.getMerchantInformation().getMerchantId(), service, jsonData, userId, realIp)
                    .map(result -> {
                        if ("Fail".equals(result.get("message"))) {
                            return ResponseEntity.status(500).body(result);
                        } else {
                            return ResponseEntity.ok(result);
                        }
                    });
            });
    }

    @PatchMapping("/transaction/void/{transactionId}")
    public Mono<ResponseEntity<Map<String, String>>> voidTransaction(
            ServerWebExchange exchange,
            @PathVariable String transactionId,
            @RequestBody String jsonData) {
        String userId = exchange.getRequest().getHeaders().getFirst("X-User-Id") != null ? exchange.getRequest().getHeaders().getFirst("X-User-Id") : "-1";
        String realIp = exchange.getRequest().getHeaders().getFirst("X-Real-IP") != null ? exchange.getRequest().getHeaders().getFirst("X-Real-IP") : "127.0.0.1";
        String service = DateValidatorUtil.mapJsonForService(jsonData);
        if (service == null || service.isEmpty()) {
            return Mono.error(new ResponseStatusException(HttpStatus.METHOD_NOT_ALLOWED, "service not found"));
        }
        TransactionDetailResponse response = transactionOldService.getTransactionDetail(transactionId, service, "Purchase", null, userId);
        if (response == null || response.getMerchantInformation() == null || response.getMerchantInformation().getMerchantId() == null) {
            return Mono.error(new ResponseStatusException(HttpStatus.NOT_FOUND, "Transaction ID not found"));
        }
        return transactionOldService
            .checkMerchantIdByUserId(userId, response.getMerchantInformation().getMerchantId(), service)
            .flatMap(hasPermission -> {
                if (!hasPermission) {
                    return Mono.error(new ResponseStatusException(HttpStatus.FORBIDDEN, "Permission denied"));
                }
                return transactionOldService.voidTransaction(transactionId, response.getMerchantInformation().getMerchantId(), service, jsonData, userId, realIp)
                    .map(result -> {
                        if ("Fail".equals(result.get("message"))) {
                            return ResponseEntity.status(500).body(result);
                        } else {
                            return ResponseEntity.ok(result);
                        }
                    });
            });
    }

    @PatchMapping("/transaction/approve-reject/{transactionId}")
    public Mono<ResponseEntity<Map<String, String>>> approveOrRejectTransaction(
            ServerWebExchange exchange,
            @PathVariable String transactionId,
            @RequestBody String jsonData) {
        String userId = exchange.getRequest().getHeaders().getFirst("X-User-Id") != null ? exchange.getRequest().getHeaders().getFirst("X-User-Id") : "-1";
        String realIp = exchange.getRequest().getHeaders().getFirst("X-Real-IP") != null ? exchange.getRequest().getHeaders().getFirst("X-Real-IP") : "127.0.0.1";
        String service = DateValidatorUtil.mapJsonForService(jsonData);
        if (service == null || service.isEmpty()) {
            return Mono.error(new ResponseStatusException(HttpStatus.METHOD_NOT_ALLOWED, "service not found"));
        }
        TransactionDetailResponse response = transactionOldService.getTransactionDetail(transactionId, service, "Purchase", null, userId);
        if (response == null || response.getMerchantInformation() == null || response.getMerchantInformation().getMerchantId() == null) {
            return Mono.error(new ResponseStatusException(HttpStatus.NOT_FOUND, "Transaction ID not found"));
        }
        return transactionOldService
            .checkMerchantIdByUserId(userId, response.getMerchantInformation().getMerchantId(), service)
            .flatMap(hasPermission -> {
                if (!hasPermission) {
                    return Mono.error(new ResponseStatusException(HttpStatus.FORBIDDEN, "Permission denied"));
                }
                return transactionOldService.approveOrRejectTransaction(transactionId, service, jsonData, userId, realIp)
                    .map(result -> {
                        if ("Fail".equals(result.get("message"))) {
                            log.info("approveOrRejectTransaction: transactionId={}, jsonData={}, result={}", transactionId, jsonData, result);
                            return ResponseEntity.status(500).body(result);
                        } else {
                            return ResponseEntity.ok(result);
                        }
                    });
            });
    }

    @GetMapping("/transaction/{originalTransactionId}/history")
    public Mono<ApiResponse<List<TransactionHistoryDTO>>> getTransactionHistory(
            ServerWebExchange exchange, 
            @PathVariable String originalTransactionId,
            @RequestParam(required = true) String paymentMethod) {
        String userId = exchange.getRequest().getHeaders().getFirst("X-User-Id") != null ? exchange.getRequest().getHeaders().getFirst("X-User-Id") : "-1";
              TransactionDetailResponse response = transactionOldService.getTransactionDetail(originalTransactionId, paymentMethod, "Purchase", null, userId);
        if (response == null || response.getMerchantInformation() == null || response.getMerchantInformation().getMerchantId() == null) {
            return Mono.error(new ResponseStatusException(HttpStatus.NOT_FOUND, "Transaction ID not found"));
        }
        
        return transactionOldService
            .checkMerchantIdByUserId(userId, response.getMerchantInformation().getMerchantId(), paymentMethod)
            .flatMap(hasPermission -> {
                if (!hasPermission) {
                    return Mono.error(new ResponseStatusException(HttpStatus.FORBIDDEN, "Permission denied"));
                }
                List<TransactionHistoryDTO> lstTransactionHistory = transactionOldService.getTransactionHistory(originalTransactionId, paymentMethod, userId);
                if (lstTransactionHistory == null || lstTransactionHistory.isEmpty()) {
                    return Mono.error(new ResponseStatusException(HttpStatus.NOT_FOUND, "originalTransactionId history not found"));
                }
                return Mono.just(ApiResponse.<List<TransactionHistoryDTO>>builder()
                            .status("SUCCESS")
                            .total(lstTransactionHistory.size())
                            .data(lstTransactionHistory)
                            .build());
            });
      
    }

}