package vn.onepay.transaction.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;
import vn.onepay.transaction.constant.IConstants;
import vn.onepay.transaction.dto.ApiResponse;
import vn.onepay.transaction.dto.PagedResponse;
import vn.onepay.transaction.dto.TransactionDTO;
import vn.onepay.transaction.dto.TransactionDetailResponse;
import vn.onepay.transaction.dto.TransactionHistoryDTO;
import vn.onepay.transaction.dto.TransactionPromotionDTO;
import vn.onepay.transaction.dto.TransactionPromotionDetailResponse;
import vn.onepay.transaction.service.DownloadTaskService;
import vn.onepay.transaction.service.TransactionService;
import vn.onepay.transaction.util.DateValidatorUtil;

@RestController
@RequestMapping
@RequiredArgsConstructor
public class TransactionController {

    private final TransactionService transactionService;
    private final DownloadTaskService downloadTaskService;

    @GetMapping("/transaction/detail/{docId}")
    public Mono<ResponseEntity<TransactionDetailResponse>> getTransactionDetail(@PathVariable String docId) {
        TransactionDetailResponse response = transactionService.getTransactionDetail(docId);

        return Mono.just(ResponseEntity.ok(response));
    }

    @GetMapping("/transaction/list")
    public Mono<ApiResponse<PagedResponse<TransactionDTO>>> getTransactions(
            ServerWebExchange exchange,
            @RequestParam(required = true) String fromDate,
            @RequestParam(required = true) String toDate,
            @RequestParam(required = false) String merchantId,
            @RequestParam(required = false) String transId,
            @RequestParam(required = false) String paymentMethod,
            @RequestParam(required = false) String transactionType,
            @RequestParam(required = false) String transactionStatus,
            @RequestParam(required = false) String searchKeyword,
            @RequestParam(required = false) Boolean promotion,
            @RequestParam(defaultValue = "ALL") String searchType,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdDate") String sortField,
            @RequestParam(defaultValue = "desc") String sortOrder) {
        DateValidatorUtil.validateDateISORange(fromDate, toDate);
        return transactionService.getTransactions(exchange,fromDate, toDate, merchantId, transId, paymentMethod,
                transactionStatus, transactionType,
                searchType, searchKeyword, page, size, sortField, sortOrder, promotion);
    }

    @GetMapping("/transaction/history/{transactionId}")
    public Mono<ResponseEntity<ApiResponse<List<TransactionHistoryDTO>>>> getTransactionHistory(
            @PathVariable String transactionId) {

        List<TransactionHistoryDTO> historyDTOs = transactionService.getTransactionHistory(transactionId);
        ApiResponse<List<TransactionHistoryDTO>> response = new ApiResponse<>(
                "SUCCESS",
                historyDTOs.size(),
                historyDTOs);

        return Mono.just(ResponseEntity.ok(response));
    }

    @GetMapping("/transaction/export")
    public Mono<Map<String, String>> createExportTask(@RequestParam(required = true) String fromDate,
            @RequestParam(required = true) String toDate,
            @RequestParam(required = false) String merchantId,
            @RequestParam(required = false) String transId,
            @RequestParam(required = false) String paymentMethod,
            @RequestParam(required = false) String transactionType,
            @RequestParam(required = false) String transactionStatus,
            @RequestParam(required = false) String searchKeyword,
            @RequestParam(required = false) Boolean isPromotion,
            @RequestParam(defaultValue = "ALL") String searchType) {
        DateValidatorUtil.validateDateISORange(fromDate, toDate);

        Map<String, Object> params = new HashMap<>();
        params.put("fromDate", fromDate);
        params.put("toDate", toDate);
        params.put("searchType", searchType);
        if (merchantId != null)
            params.put("merchantId", merchantId);
        if (transId != null)
            params.put("transId", transId);
        if (paymentMethod != null)
            params.put("paymentMethod", paymentMethod);
        if (transactionType != null)
            params.put("transactionType", transactionType);
        if (transactionStatus != null)
            params.put("transactionStatus", transactionStatus);
        if (searchKeyword != null)
            params.put("searchKeyword", searchKeyword);
        if (isPromotion != null)
            params.put("isPromotion", isPromotion);

        return downloadTaskService.createTask(IConstants.TYPE_TRANSACTION, params).map(task -> Map.of(
                "fileName", task.getFileName(),
                "fileHashName", task.getFileHashName(),
                "status", "ok"));
    }

    @GetMapping("/promotion/list")
    public Mono<ApiResponse<PagedResponse<TransactionPromotionDTO>>> getTransactionsPromotion(
        ServerWebExchange exchange,
            @RequestParam(required = true) String fromDate,
            @RequestParam(required = true) String toDate,
            @RequestParam(required = false) String merchantId,
            @RequestParam(required = false) String transId,
            @RequestParam(required = false) String merTransactionId,
            @RequestParam(required = false) String cardNumber,
            @RequestParam(required = false) String authCode,
            @RequestParam(required = false) String promoCode,
            @RequestParam(required = false) String promoName,
            @RequestParam(required = false) String paymentMethod,
            @RequestParam(required = false) String transactionType,
            @RequestParam(required = false) String transactionStatus,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdDate") String sortField,
            @RequestParam(defaultValue = "desc") String sortOrder) {
        DateValidatorUtil.validateDateISORange(fromDate, toDate);
        return transactionService.getTransactionsPromotion(exchange,fromDate, toDate, merchantId,
                transId, merTransactionId, cardNumber, authCode, promoCode,
                promoName, paymentMethod, transactionType, transactionStatus, page,
                size, sortField, sortOrder);
    }

    @GetMapping("/promotion/detail/{docId}")
    public Mono<ResponseEntity<TransactionPromotionDetailResponse>> getTransactionPromotionDetail(
            @PathVariable String docId) {
        TransactionPromotionDetailResponse response = transactionService.getTransactionPromotionDetail(docId);

        return Mono.just(ResponseEntity.ok(response));
    }
}