package vn.onepay.transaction.controller;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;
import vn.onepay.transaction.service.PromotionService;
import vn.onepay.transaction.service.TransactionReportService;
import vn.onepay.transaction.service.UposService;

@RestController
@RequestMapping
@RequiredArgsConstructor
public class RetryJobController {
    private final PromotionService promotionService;
    private final TransactionReportService transactionReportService;
    private final UposService uposService;

    @GetMapping("/report/insert-daily")
    public Mono<ResponseEntity<String>> insertDailyReport(
            @RequestParam(required = true) String fromDate,
            @RequestParam(required = true) String toDate,
            @RequestParam(required = true) String type) {

        switch (type.toLowerCase()) {
            case "promotion":
                return promotionService.insertDailyPromotionReport(fromDate, toDate)
                        .then(Mono.just(ResponseEntity.ok("Promotion report inserted successfully.")))
                        .onErrorResume(e -> Mono.just(ResponseEntity
                                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                                .body("Error while inserting promotion report: " + e.getMessage())));
            case "transaction":
                return transactionReportService.insertDailyTransactionReport(fromDate, toDate)
                        .then(Mono.just(ResponseEntity.ok("Transaction report inserted successfully.")))
                        .onErrorResume(e -> Mono.just(ResponseEntity
                                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                                .body("Error while inserting transaction report: " + e.getMessage())));
            case "upos":
                return uposService.insertDailyUposReport(fromDate, toDate)
                        .then(Mono.just(ResponseEntity.ok("Upos report inserted successfully.")))
                        .onErrorResume(e -> Mono.just(ResponseEntity
                                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                                .body("Error while inserting Upos report: " + e.getMessage())));
            default:
                return Mono.just(ResponseEntity
                        .badRequest()
                        .body("Invalid report type. Must be 'promotion' or 'transaction' or 'upos'."));
        }
    }
}
