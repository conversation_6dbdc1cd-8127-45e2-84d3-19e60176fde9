package vn.onepay.transaction.controller;

import java.util.HashMap;
import java.util.Map;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;
import vn.onepay.transaction.constant.IConstants;
import vn.onepay.transaction.dto.ApiResponse;
import vn.onepay.transaction.dto.PagedResponse;
import vn.onepay.transaction.dto.PromotionReportResponse;
import vn.onepay.transaction.dto.ReportResponse;
import vn.onepay.transaction.dto.TransactionPromotionDTO;
import vn.onepay.transaction.dto.TransactionPromotionDetailResponse;
import vn.onepay.transaction.service.DownloadTaskService;
import vn.onepay.transaction.service.PromotionService;
import vn.onepay.transaction.service.TransactionService;
import vn.onepay.transaction.util.DateValidatorUtil;

@RestController
@RequestMapping
@RequiredArgsConstructor
public class PromotionController {
    private final TransactionService transactionService;
    private final PromotionService promotionService;
    private final DownloadTaskService downloadTaskService;

    @GetMapping("/promotion/list")
    public Mono<ApiResponse<PagedResponse<TransactionPromotionDTO>>> getTransactionsPromotion(
            @RequestParam(required = true) String fromDate,
            @RequestParam(required = true) String toDate,
            @RequestParam(required = false) String merchantId,
            @RequestParam(required = false) String transId,
            @RequestParam(required = false) String merchantTransRef,
            @RequestParam(required = false) String cardNumber,
            @RequestParam(required = false) String authCode,
            @RequestParam(required = false) String promoCode,
            @RequestParam(required = false) String promoName,
            @RequestParam(required = false) String paymentMethod,
            @RequestParam(required = false) String transactionType,
            @RequestParam(required = false) String transactionStatus,
            @RequestParam(required = false) String orderInfo,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdDate") String sortField,
            @RequestParam(defaultValue = "desc") String sortOrder) {
        DateValidatorUtil.validateDateISORange(fromDate, toDate);
        if (!IConstants.ALLOWED_SIZES.contains(size)) {
            return Mono.just(ApiResponse.error("Invalid 'size'. Allowed values: 20, 50, 100, 200, 500."));
        }
        return transactionService.getTransactionsPromotion(fromDate, toDate, merchantId,
                transId, merchantTransRef, cardNumber, authCode, promoCode,
                promoName, paymentMethod, transactionType, transactionStatus, page,
                size, sortField, sortOrder,orderInfo);
    }

    @GetMapping("/promotion/detail/{docId}")
    public Mono<TransactionPromotionDetailResponse> getTransactionPromotionDetail(@PathVariable String docId) {
        return transactionService.getTransactionPromotionDetail(docId);
    }

    @GetMapping("/promotion/report/list")
    public Mono<ApiResponse<ReportResponse<PromotionReportResponse>>> getPromotionReport(
            @RequestParam(required = true) String fromDate,
            @RequestParam(required = true) String toDate,
            @RequestParam(required = false) String promotionCode,
            @RequestParam(required = false) String paymentMethod,
            @RequestParam(defaultValue = "ALL") String merchantId,
            @RequestParam(defaultValue = "DAILY") String interval,
            @RequestParam(defaultValue = "VND") String currency,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        if (!IConstants.ALLOWED_SIZES.contains(size)) {
            return Mono.just(ApiResponse.error("Invalid 'size'. Allowed values: 20, 50, 100, 200, 500."));
        }
        return promotionService.getPromotionReport(fromDate, toDate, promotionCode, paymentMethod, merchantId,
                interval, currency, page, size);
    }

    @GetMapping("/promotion/export")
    public Mono<Map<String, String>> createExportTask(
            ServerWebExchange exchange,
            @RequestParam(required = true) String fromDate,
            @RequestParam(required = true) String toDate,
            @RequestParam(required = false) String merchantId,
            @RequestParam(required = false) String transId,
            @RequestParam(required = false) String merchantTransRef,
            @RequestParam(required = false) String cardNumber,
            @RequestParam(required = false) String authCode,
            @RequestParam(required = false) String promoCode,
            @RequestParam(required = false) String promoName,
            @RequestParam(required = false) String paymentMethod,
            @RequestParam(required = false) String transactionType,
            @RequestParam(required = false) String transactionStatus) {
        DateValidatorUtil.validateDateISORange(fromDate, toDate);
        Map<String, Object> params = new HashMap<>();
        params.put("fromDate", fromDate);
        params.put("toDate", toDate);

        if (paymentMethod != null)
            params.put("paymentMethod", paymentMethod);
        if (merchantId != null)
            params.put("merchantId", merchantId);
        if (transId != null)
            params.put("transId", transId);
        if (merchantTransRef != null)
            params.put("merTransactionId", merchantTransRef);
        if (cardNumber != null)
            params.put("cardNumber", cardNumber);
        if (authCode != null)
            params.put("authCode", authCode);
        if (promoCode != null)
            params.put("promoCode", promoCode);
        if (promoName != null)
            params.put("promoName", promoName);
        if (transactionType != null)
            params.put("transactionType", transactionType);
        if (transactionStatus != null)
            params.put("transactionStatus", transactionStatus);
        return downloadTaskService.createTask(exchange,IConstants.TYPE_PROMOTION, params).map(task -> Map.of(
                "file_name", task.getFileName(),
                "file_hash_name", task.getFileHashName(),
                "status", "ok"));
    }

    @GetMapping("/promotion/report/export")
    public Mono<Map<String, String>> createExportReportTask(
            ServerWebExchange exchange,
            @RequestParam(required = true) String fromDate,
            @RequestParam(required = true) String toDate,
            @RequestParam(required = false) String promotionCode,
            @RequestParam(required = false) String paymentMethod,
            @RequestParam(defaultValue = "ALL") String merchantId,
            @RequestParam(defaultValue = "DAILY") String interval,
            @RequestParam(defaultValue = "VND") String currency) {
        Map<String, Object> params = new HashMap<>();
        params.put("fromDate", fromDate);
        params.put("toDate", toDate);
        params.put("interval", interval);
        params.put("currency", currency);
        if (paymentMethod != null)
            params.put("paymentMethod", paymentMethod);
        if (merchantId != null)
            params.put("merchantId", merchantId);
        if (promotionCode != null)
            params.put("promotionCode", promotionCode);
        return downloadTaskService.createTask(exchange,IConstants.TYPE_PROMOTION_REPORT, params).map(task -> Map.of(
                "file_name", task.getFileName(),
                "file_hash_name", task.getFileHashName(),
                "status", "ok"));
    }
}
