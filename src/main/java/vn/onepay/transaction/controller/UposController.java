package vn.onepay.transaction.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;
import vn.onepay.transaction.constant.IConstants;
import vn.onepay.transaction.dto.ApiResponse;
import vn.onepay.transaction.dto.PagedResponse;
import vn.onepay.transaction.dto.ReportResponse;
import vn.onepay.transaction.dto.UposDetailResponse;
import vn.onepay.transaction.dto.UposListResponse;
import vn.onepay.transaction.dto.UposReportResponse;
import vn.onepay.transaction.dto.TransactionHistoryDTO;
import vn.onepay.transaction.service.DownloadTaskService;
import vn.onepay.transaction.service.TransactionOldService;
import vn.onepay.transaction.service.TransactionService;
import vn.onepay.transaction.service.UposService;
import vn.onepay.transaction.util.DateValidatorUtil;

@RestController
@RequiredArgsConstructor
public class UposController {
    private final UposService uposService;
    private final TransactionService transactionService;
    private final DownloadTaskService downloadTaskService;
    private final TransactionOldService transactionOldService;

    @GetMapping("/upos/list")
    public Mono<ApiResponse<PagedResponse<UposListResponse>>> getTransactionsUpos(
            ServerWebExchange exchange,

            @RequestParam(required = true) String fromDate,
            @RequestParam(required = true) String toDate,
            @RequestParam(required = false) String merchantId,
            @RequestParam(required = false) String tid,
            @RequestParam(required = false) String transactionId,
            @RequestParam(required = false) String orderRef,
            @RequestParam(required = false) String merchantTransRef,
            @RequestParam(required = false) String cardNumber,
            @RequestParam(required = false) String approvalCode,
            @RequestParam(required = false) String paymentChannel,
            @RequestParam(required = false) String cardType,
            @RequestParam(required = false) String transactionType,
            @RequestParam(required = false) String transactionState,
            @RequestParam(required = false) String installmentStatus,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        DateValidatorUtil.validateDateISORange(fromDate, toDate);
        if (!IConstants.ALLOWED_SIZES.contains(size)) {
            return Mono.just(ApiResponse.error("Invalid 'size'. Allowed values: 20, 50, 100, 200, 500."));
        }
        String userId = exchange.getRequest().getHeaders().getFirst("X-User-Id") != null
                ? exchange.getRequest().getHeaders().getFirst("X-User-Id")
                : "-1";
        return transactionOldService.checkUserExistsInPostgres(userId)
                .flatMap(type -> {
                    if ("1".equalsIgnoreCase(type)) {
                        return uposService.getUposTransactions(fromDate, toDate, merchantId, tid, transactionId, orderRef,
                                    merchantTransRef, cardNumber, approvalCode, paymentChannel, cardType, transactionType, transactionState,
                                    installmentStatus, page, size);
                    } else {
                        return transactionOldService.getUposTransactions(fromDate, toDate, merchantId, tid, transactionId, orderRef, merchantTransRef,
                                    cardNumber, approvalCode, paymentChannel, cardType, transactionType, transactionState, installmentStatus,
                                    page, size, userId);
                    }
                });
    }

    @GetMapping("/upos/detail/{docId}")
    public Mono<UposDetailResponse> getTransactionDetail(@PathVariable String docId) {
        return uposService.getUposDetail(docId);
    }

    @GetMapping("/upos/history/{transactionId}")
    public Mono<ApiResponse<List<TransactionHistoryDTO>>> getTransactionHistory(@PathVariable String transactionId) {
        return transactionService.getTransactionHistory(transactionId, IConstants.TYPE_UPOS);
    }

    @GetMapping("/upos/report/list")
    public Mono<ApiResponse<ReportResponse<UposReportResponse>>> getUposReport(
            @RequestParam(required = true) String fromDate,
            @RequestParam(required = true) String toDate,
            @RequestParam(required = false) String tid,
            @RequestParam(required = false) String paymentChannel,
            @RequestParam(defaultValue = "ALL") String merchantId,
            @RequestParam(defaultValue = "DAILY") String interval,
            @RequestParam(defaultValue = "VND") String currency,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        if (!IConstants.ALLOWED_SIZES.contains(size)) {
            return Mono.just(ApiResponse.error("Invalid 'size'. Allowed values: 20, 50, 100, 200, 500."));
        }
        return uposService.getUposReport(fromDate, toDate, merchantId,
                interval, currency, tid, page, size,paymentChannel);
    }

    @GetMapping("/upos/export")
    public Mono<Map<String, String>> createExportTask(
            ServerWebExchange exchange,
            @RequestParam(required = true) String fromDate,
            @RequestParam(required = true) String toDate,
            @RequestParam(required = false) String merchantId,
            @RequestParam(required = false) String tid,
            @RequestParam(required = false) String transactionId,
            @RequestParam(required = false) String orderRef,
            @RequestParam(required = false) String merchantTransRef,
            @RequestParam(required = false) String cardNumber,
            @RequestParam(required = false) String approvalCode,
            @RequestParam(required = false) String paymentChannel,
            @RequestParam(required = false) String cardType,
            @RequestParam(required = false) String transactionType,
            @RequestParam(required = false) String transactionState,
            @RequestParam(required = false) String installmentStatus) {
        DateValidatorUtil.validateDateISORange(fromDate, toDate);
        Map<String, Object> params = new HashMap<>();
        params.put("fromDate", fromDate);
        params.put("toDate", toDate);

        if (tid != null)
            params.put("tid", tid);
        if (merchantId != null)
            params.put("merchantId", merchantId);
        if (transactionId != null)
            params.put("transactionId", transactionId);
        if (merchantTransRef != null)
            params.put("merTransactionId", merchantTransRef);
        if (cardNumber != null)
            params.put("cardNumber", cardNumber);
        if (orderRef != null)
            params.put("orderRef", orderRef);
        if (approvalCode != null)
            params.put("approvalCode", approvalCode);
        if (paymentChannel != null)
            params.put("paymentChannel", paymentChannel);
        if (transactionType != null)
            params.put("transactionType", transactionType);
        if (cardType != null)
            params.put("cardType", cardType);
        if (transactionState != null)
            params.put("transactionState", transactionState);
        if (installmentStatus != null)
            params.put("installmentStatus", installmentStatus);
        return downloadTaskService.createTask(exchange,IConstants.TYPE_UPOS, params).map(task -> Map.of(
                "file_name", task.getFileName(),
                "file_hash_name", task.getFileHashName(),
                "status", "ok"));
    }

    @GetMapping("/upos/report/export")
    public Mono<Map<String, String>> createExportReportTask(
            ServerWebExchange exchange,
            @RequestParam(required = true) String fromDate,
            @RequestParam(required = true) String toDate,
            @RequestParam(required = false) String tid,
            @RequestParam(required = false) String paymentChannel,
            @RequestParam(defaultValue = "ALL") String merchantId,
            @RequestParam(defaultValue = "DAILY") String interval,
            @RequestParam(defaultValue = "VND") String currency) {
        Map<String, Object> params = new HashMap<>();
        params.put("fromDate", fromDate);
        params.put("toDate", toDate);
        params.put("interval", interval);
        params.put("currency", currency);
        if (paymentChannel != null)
            params.put("paymentChannel", paymentChannel);
        if (merchantId != null)
            params.put("merchantId", merchantId);
        if (tid != null)
            params.put("tid", tid);
        return downloadTaskService.createTask(exchange,IConstants.TYPE_UPOS_REPORT, params).map(task -> Map.of(
                "file_name", task.getFileName(),
                "file_hash_name", task.getFileHashName(),
                "status", "ok"));
    }
}
