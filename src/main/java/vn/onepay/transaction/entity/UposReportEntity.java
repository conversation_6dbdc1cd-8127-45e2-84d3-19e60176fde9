package vn.onepay.transaction.entity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UposReportEntity {
    private LocalDate dTransactionDay;
    private String merchantId;
    private String tid;
    private String paymentChannel;
    private String transactionType;
    private String cardType;
    private BigDecimal amount;
    private String currency;
    private LocalDateTime createdDate;
    private LocalDateTime updatedDate;
    private Long totalTrans;
}
