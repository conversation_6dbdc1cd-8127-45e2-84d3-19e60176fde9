package vn.onepay.transaction.entity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PromotionReportEntity {
    private LocalDate dTransactionDay;
    private String merchantId;
    private String transactionType;
    private String payGate;
    private String cardType;
    private String promotionCode;
    private String paymentMethod;
    private String currency;
    private BigDecimal amountInvoice;
    private BigDecimal amountPayment;
    private LocalDateTime createdDate;
    private LocalDateTime updatedDate;
    private Long totalTrans;
}