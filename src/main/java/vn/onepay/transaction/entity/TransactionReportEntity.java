package vn.onepay.transaction.entity;

import lombok.Data;
import org.springframework.data.relational.core.mapping.Table;
import org.springframework.data.relational.core.mapping.Column;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Table("transaction_report")
public class TransactionReportEntity {

    @Column("d_transaction_day")
    private LocalDate dTransactionDay;

    @Column("merchant_id")
    private String merchantId;

    @Column("transaction_type")
    private String transactionType;

    @Column("currency")
    private String currency;

    @Column("amount")
    private BigDecimal amount;

    @Column("created_date")
    private LocalDateTime createdDate;

    @Column("updated_date")
    private LocalDateTime updatedDate;

    @Column("total_trans")
    private Long totalTrans;
}