package vn.onepay.transaction.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import org.springframework.data.relational.core.mapping.Table;
import org.springframework.data.relational.core.mapping.Column;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionReportEntity {
    private LocalDate dTransactionDay;
    private String merchantId;
    private String transactionType;
    private String paymentMethod;
    private String orderSource;
    private Boolean isPromotion;
    private String currency;
    private BigDecimal amount;
    private LocalDateTime createdDate;
    private LocalDateTime updatedDate;
    private Long totalTrans;
}