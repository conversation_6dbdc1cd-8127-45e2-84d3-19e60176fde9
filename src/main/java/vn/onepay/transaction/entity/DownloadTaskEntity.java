package vn.onepay.transaction.entity;

import java.time.LocalDateTime;
import java.util.UUID;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table("download_task")
public class DownloadTaskEntity {

    @Id
    private UUID id;

    @Column("task_type")
    private String taskType;

    @Column("status")
    private String status;

    @Column("request_params")
    private String requestParams; // Lưu JSON dưới dạng String

    @Column("file_name")
    private String fileName;

    @Column("file_hash_name")
    private String fileHashName;

    @Column("file_url")
    private String fileUrl;

    @Column("error_message")
    private String errorMessage;

    @Column("created_at")
    private LocalDateTime createdAt;

    @Column("updated_at")
    private LocalDateTime updatedAt;

    @Column("user_id")
    private String userId;
}
