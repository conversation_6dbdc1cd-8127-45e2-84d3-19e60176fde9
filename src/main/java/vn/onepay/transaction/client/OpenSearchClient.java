package vn.onepay.transaction.client;

import java.io.IOException;
import java.util.Scanner;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.opensearch.client.RequestOptions;
import org.opensearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.apache.http.HttpHost;
import org.opensearch.client.Request;
import org.opensearch.client.Response;
import org.opensearch.client.ResponseException;
import org.opensearch.client.RestClient;

@Service
public class OpenSearchClient {

    private static final Logger logger = Logger.getLogger(OpenSearchClient.class.getName());

    private final RestHighLevelClient client;

    public OpenSearchClient(
            @Value("${opensearch.host}") String host,
            @Value("${opensearch.port}") int port,
            @Value("${opensearch.protocol}") String protocol) {
        this.client = new RestHighLevelClient(
                RestClient.builder(new HttpHost(host, port, protocol)));
    }

    public void close() throws IOException {
        client.close();
    }

    public String executeQuery(String method, String endpoint, String query) throws Exception {
        Request request = new Request(method, endpoint);
        RequestOptions.Builder options = RequestOptions.DEFAULT.toBuilder();
        options.addHeader("Content-Type", "application/json");
        request.setOptions(options.build());
        request.setJsonEntity(query);
        logger.log(Level.INFO, "OPENSEARCH EXECUTE QUERY:" + query);
        try {
            Response response = client.getLowLevelClient().performRequest(request);
            return extractResponse(response);
        } catch (ResponseException rex) {
            // Ngay cả khi lỗi (ví dụ 404), vẫn lấy body về được
            String errorBody = extractResponse(rex.getResponse());
            logger.log(Level.WARNING, "OPENSEARCH EXECUTE QUERY - ResponseException (status: "
                    + rex.getResponse().getStatusLine() + "): " + errorBody);
            return errorBody;
        } catch (Exception ex) {
            logger.log(Level.SEVERE, "OPENSEARCH EXECUTE QUERY - Exception", ex);
            return null;
        }
    }

    private String extractResponse(Response response) {
        try (Scanner scanner = new Scanner(response.getEntity().getContent()).useDelimiter("\\A")) {          
            return scanner.hasNext() ? scanner.next() : "{}";
        } catch (Exception e) {
            logger.log(Level.WARNING, "Failed to extract response body: ", e);
            return "{}";
        }
    }

}
