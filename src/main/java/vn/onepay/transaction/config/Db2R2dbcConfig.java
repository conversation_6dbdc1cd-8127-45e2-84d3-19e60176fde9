package vn.onepay.transaction.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.r2dbc.core.DatabaseClient;

import io.r2dbc.spi.ConnectionFactories;
import io.r2dbc.spi.ConnectionFactory;
import io.r2dbc.spi.ConnectionFactoryOptions;

@Configuration
public class Db2R2dbcConfig {
    @Bean("db2ConnectionFactory")
    public ConnectionFactory db2ConnectionFactory(
            @Value("${db2.r2dbc.url}") String url,
            @Value("${db2.r2dbc.username}") String username,
            @Value("${db2.r2dbc.password}") String password) {
        return ConnectionFactories.get(
                ConnectionFactoryOptions.parse(url)
                        .mutate()
                        .option(ConnectionFactoryOptions.USER, username)
                        .option(ConnectionFactoryOptions.PASSWORD, password)
                        .build());
    }

    @Bean("db2DatabaseClient")
    public DatabaseClient db2DatabaseClient(@Qualifier("db2ConnectionFactory") ConnectionFactory connectionFactory) {
        return DatabaseClient.create(connectionFactory);
    }
}
