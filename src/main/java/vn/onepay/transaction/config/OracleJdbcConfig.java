package vn.onepay.transaction.config;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

@Configuration
public class OracleJdbcConfig {

    @Bean(name = "oracleOneReportHikariConfig")
    @ConfigurationProperties(prefix = "spring.datasource.oracle.onereport.hikari")
    public HikariConfig oracleOneReportHikariConfig() {
        return new HikariConfig();
    }

    @Bean(name = "oracleDataSource")
    @Primary
    public DataSource oracleDataSource(
            @Value("${spring.datasource.oracle.onereport.url}") String url,
            @Value("${spring.datasource.oracle.onereport.username}") String username,
            @Value("${spring.datasource.oracle.onereport.password}") String password,
            @Value("${spring.datasource.oracle.driver-class-name}") String driverClassName,
            @Qualifier("oracleOneReportHikariConfig") HikariConfig hikariConfig
    ) {
        hikariConfig.setJdbcUrl(url);
        hikariConfig.setUsername(username);
        hikariConfig.setPassword(password);
        hikariConfig.setDriverClassName(driverClassName);
        return new HikariDataSource(hikariConfig);
    }

    @Bean(name = "oracleJdbcTemplate")
    @Primary
    public JdbcTemplate oracleJdbcTemplate(@Qualifier("oracleDataSource") DataSource ds) {
        return new JdbcTemplate(ds);
    }

    @Bean(name = "oracleMerchantPortalHikariConfig")
    @ConfigurationProperties(prefix = "spring.datasource.oracle.merchantportal.hikari")
    public HikariConfig oracleMerchantPortalHikariConfig() {
        return new HikariConfig();
    }

    @Bean(name = "oracleMerchantPortalDataSource")
    public DataSource oracleMerchantPortalDataSource(
            @Value("${spring.datasource.oracle.merchantportal.url}") String url,
            @Value("${spring.datasource.oracle.merchantportal.username}") String username,
            @Value("${spring.datasource.oracle.merchantportal.password}") String password,
            @Value("${spring.datasource.oracle.driver-class-name}") String driverClassName,
            @Qualifier("oracleMerchantPortalHikariConfig") HikariConfig hikariConfig
    ) {
        hikariConfig.setJdbcUrl(url);
        hikariConfig.setUsername(username);
        hikariConfig.setPassword(password);
        hikariConfig.setDriverClassName(driverClassName);
        hikariConfig.addDataSourceProperty("oracle.jdbc.timezoneAsRegion", "false");
        return new HikariDataSource(hikariConfig);
    }

    @Bean(name = "oracleMerchantPortalJdbcTemplate")
    public JdbcTemplate oracleMerchantPortalJdbcTemplate(@Qualifier("oracleMerchantPortalDataSource") DataSource ds) {
        return new JdbcTemplate(ds);
    }
}
