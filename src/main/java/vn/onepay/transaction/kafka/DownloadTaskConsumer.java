package vn.onepay.transaction.kafka;

import java.time.LocalDateTime;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.r2dbc.core.DatabaseClient;
import org.springframework.stereotype.Component;

import io.r2dbc.spi.Row;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import vn.onepay.transaction.constant.IConstants;
import vn.onepay.transaction.dto.FileDownloadDto;
import vn.onepay.transaction.entity.DownloadTaskEntity;
import vn.onepay.transaction.repository.FileDownloadRepository;
import vn.onepay.transaction.service.PromotionService;
import vn.onepay.transaction.service.RequestRefundService;
import vn.onepay.transaction.service.TransactionReportService;
import vn.onepay.transaction.service.TransactionService;
import vn.onepay.transaction.service.UposService;
import vn.onepay.transaction.util.NotificationSender;

@ConditionalOnProperty(name = "kafka.download-task.enabled", havingValue = "true", matchIfMissing = false)
@Component
public class DownloadTaskConsumer {

    private final Logger logger = LoggerFactory.getLogger(DownloadTaskConsumer.class);
    @Autowired
    private DatabaseClient postgresClient;
    @Autowired
    private TransactionReportService reportService;
    @Autowired
    private TransactionService transactionService;
    @Autowired
    private PromotionService promotionService;
    @Autowired
    private RequestRefundService refundService;
    @Autowired
    private FileDownloadRepository fileDownloadRepository;
    @Autowired
    private NotificationSender notificationSender;
    @Autowired
    private UposService uposService;
    @KafkaListener(topics = IConstants.DOWNLOAD_TASK_TOPIC, groupId = "download-task-group", concurrency = "2")
    public void handleDownloadTask(UUID taskId) {
        logger.info("Received download task id from Kafka: {}", taskId);

        getPendingTask(taskId)
                .flatMap(task -> processTask(task)
                        .flatMap(fileDto -> updateSuccess(taskId, task, fileDto))
                        .onErrorResume(ex -> handleFailure(taskId, task, ex)))
                .subscribe();
    }

    private Mono<FileDownloadDto> processTask(DownloadTaskEntity task) {
        // func tạo excel
        return createExcel(task)
                .flatMap(fileDto -> {
                    fileDto.setStatus("success");
                    return updateOracleSuccess(fileDto).thenReturn(fileDto);
                });
    }

    private Mono<FileDownloadDto> createExcel(DownloadTaskEntity task) {
        return Mono.fromCallable(() -> {
            if (IConstants.TYPE_TRANSACTION_REPORT.equals(task.getTaskType())) {
                return reportService.createExcel(task);
            } else if (IConstants.TYPE_TRANSACTION.equals(task.getTaskType())) {
                return transactionService.createExcel(task);
            } else if (IConstants.TYPE_PROMOTION.equals(task.getTaskType())) {
                return promotionService.createExcel(task);
            } else if (IConstants.TYPE_PROMOTION_REPORT.equals(task.getTaskType())) {
                return promotionService.createReportExcel(task);
            } else if (IConstants.TYPE_REQUEST_REFUND.equals(task.getTaskType())) {
                return refundService.createExcel(task);
            } else if (IConstants.TYPE_UPOS.equals(task.getTaskType())) {
                return uposService.createExcel(task);
            } else if (IConstants.TYPE_UPOS_REPORT.equals(task.getTaskType())) {
                return uposService.createReportExcel(task);
            } else {
                throw new IllegalArgumentException("Unsupported task type: " + task.getTaskType());
            }
        }).subscribeOn(Schedulers.boundedElastic()); // chạy blocking trên thread riêng
    }

    private Mono<DownloadTaskEntity> getPendingTask(UUID taskId) {
        // func select task dowload by id
        return postgresClient.sql("SELECT * FROM download_task WHERE id = :id AND status = 'PENDING'")
                .bind("id", taskId)
                .map((row, meta) -> mapToEntity(row))
                .one()
                .switchIfEmpty(Mono.fromRunnable(() -> logger.warn("No pending task found for id {}", taskId))
                        .then(Mono.empty()));
    }

    private DownloadTaskEntity mapToEntity(Row row) {
        return DownloadTaskEntity.builder()
                .id(row.get("id", UUID.class))
                .taskType(row.get("task_type", String.class))
                .fileName(row.get("file_name", String.class))
                .fileHashName(row.get("file_hash_name", String.class))
                .requestParams(row.get("request_params", String.class))
                .createdAt(row.get("created_at", LocalDateTime.class))
                .userId(row.get("user_id", String.class))
                .build();
    }

    private Mono<Void> handleFailure(UUID taskId, DownloadTaskEntity task, Throwable ex) { // Fail thì update cả 2 bảng
        logger.error("Error processing task {}: {}", taskId, ex.getMessage(), ex);

        return Mono.fromRunnable(() -> {
            FileDownloadDto dto = new FileDownloadDto();
            dto.setFile_hash_name(task.getFileHashName());
            dto.setStatus("failed");
            fileDownloadRepository.updateStatus(dto);
        })
                .subscribeOn(Schedulers.boundedElastic())
                .then(postgresClient.sql("""
                            UPDATE download_task
                            SET status = 'FAILED', updated_at = :updatedAt
                            WHERE id = :id
                        """)
                        .bind("updatedAt", LocalDateTime.now())
                        .bind("id", taskId)
                        .then());
    }

    private Mono<Void> updateSuccess(UUID taskId, DownloadTaskEntity task, FileDownloadDto fileDto) {
        return postgresClient.sql("""
                    UPDATE download_task
                    SET status = 'DONE', file_url = :filePath, updated_at = :updatedAt
                    WHERE id = :id
                """)
                .bind("filePath", fileDto.getFile_path())
                .bind("updatedAt", LocalDateTime.now())
                .bind("id", taskId)
                .fetch()
                .rowsUpdated()
                .then(notificationSender.send(
                        fileDto.getUser(),
                        fileDto.getFile_hash_name(),
                        fileDto.getFile_name(),
                        true,
                        fileDto.getFile_size()));
    }

    private Mono<Void> updateOracleSuccess(FileDownloadDto fileDto) {
        return Mono.fromRunnable(() -> fileDownloadRepository.updateStatus(fileDto))
                .subscribeOn(Schedulers.boundedElastic()).then();
    }

}
