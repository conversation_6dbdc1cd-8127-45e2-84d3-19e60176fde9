package vn.onepay.transaction.kafka;

import java.time.LocalDateTime;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.r2dbc.core.DatabaseClient;
import org.springframework.stereotype.Component;

import io.r2dbc.spi.Row;
import reactor.core.publisher.Mono;
import vn.onepay.transaction.constant.IConstants;
import vn.onepay.transaction.entity.DownloadTaskEntity;
import vn.onepay.transaction.service.TransactionReportService;
import vn.onepay.transaction.service.TransactionService;
@Component
public class DownloadTaskConsumer {

    private final Logger logger = LoggerFactory.getLogger(DownloadTaskConsumer.class);

    @Autowired
    private DatabaseClient databaseClient;

    @Autowired
    private TransactionReportService reportService;

    @Autowired
    private TransactionService transactionService;

    @KafkaListener(topics = IConstants.DOWNLOAD_TASK_TOPIC, groupId = "download-task-group")
    public void handleDownloadTask(UUID taskId) {
        logger.info("Received download task id from Kafka: {}", taskId);

        databaseClient.sql("SELECT * FROM download_task WHERE id = :id AND status = 'PENDING'")
                .bind("id", taskId)
                .map((row, meta) -> mapToEntity(row))
                .one()
                .switchIfEmpty(Mono.fromRunnable(() -> logger.warn("No pending task found for id {}", taskId)))
                .flatMap(task -> Mono.fromCallable(() -> createExcel(task))
                        .flatMap(filePath -> databaseClient.sql(
                                "UPDATE download_task SET status = 'DONE', file_url = :filePath, updated_at = :updatedAt WHERE id = :id")
                                .bind("filePath", filePath)
                                .bind("updatedAt", LocalDateTime.now())
                                .bind("id", taskId)
                                .then())
                        .onErrorResume(ex -> {
                            logger.error("Error processing task: {}", ex.getMessage(), ex);
                            return databaseClient.sql(
                                    "UPDATE download_task SET status = 'FAILED', updated_at = :updatedAt WHERE id = :id")
                                    .bind("updatedAt", LocalDateTime.now())
                                    .bind("id", taskId)
                                    .then();
                        }))
                .subscribe();
    }

    private DownloadTaskEntity mapToEntity(Row row) {
        return DownloadTaskEntity.builder()
                .id(row.get("id", UUID.class))
                .taskType(row.get("task_type", String.class))
                .fileName(row.get("file_name", String.class))
                .fileHashName(row.get("file_hash_name", String.class))
                .requestParams(row.get("request_params", String.class))
                .createdAt(row.get("created_at", LocalDateTime.class))
                .build();
    }

    private String createExcel(DownloadTaskEntity task) {
        String filePath = "";
        if (IConstants.TYPE_TRANSACTION_REPORT.equals(task.getTaskType())) {
            filePath = reportService.createExcel(task);
        }else if(IConstants.TYPE_TRANSACTION.equals(task.getTaskType())) {
            filePath = transactionService.createExcel(task);
        }
        return filePath;
    }
}
