package vn.onepay.transaction.dto;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RequestRefundResponse {
    private String docId;
    private String merchantId;
    private String requestId;
    private String parentTransactionId;
    private BigDecimal amount;
    private String currency;
    private String createdDate;
    private String createdBy;
    private String requestStatus;
}
