package vn.onepay.transaction.dto;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PromotionReportResponse {
    private String merchantId;
    private String date;
    private String cardType;
    private String currency;
    private Long numPurchaseTrans;
    private Long numRefundVoidTrans;
    private BigDecimal totalAmount;
    private BigDecimal totalOriginalPurchase;
    private BigDecimal totalRefund;
}
