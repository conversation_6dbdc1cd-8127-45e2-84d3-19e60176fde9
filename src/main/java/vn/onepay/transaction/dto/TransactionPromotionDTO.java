package vn.onepay.transaction.dto;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TransactionPromotionDTO {
    private String docId;
    private String merchantId;
    private String baseId;
    private String transactionId;
    private String orderReference; 
    private BigDecimal orgAmount;
    private BigDecimal paymentAmount;
    private String promotionCode;
    private String promotionName;
    private String currency;
    private String paymentMethod;
    private String cardNumber;
    private String createdDate;
    private String transactionType;
    private String transactionStatus;
    private String responseCode;
    private String responseDescription;
}