package vn.onepay.transaction.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TransactionDetailResponse {
    private OrderInformation orderInformation;
    private MerchantInformation merchantInformation;
    private TransactionInformation transactionInformation;
    private PaymentMethod paymentMethod;
    private PromotionInformation promotionInformation;
    private RiskManagement riskManagement;
    private FeeInformation feeInformation;
    private AdvanceInformation advanceInformation;
}