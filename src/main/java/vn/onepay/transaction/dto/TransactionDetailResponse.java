package vn.onepay.transaction.dto;

import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TransactionDetailResponse {
    private OrderInformation orderInformation;
    private MerchantInformation merchantInformation;
    private TransactionInformation transactionInformation;
    private PaymentMethod paymentMethod;
    private InstallmentDetail installmentDetail;
    private PromotionInformation promotionInformation;
    private RiskManagement riskManagement;
    private FeeInformation feeInformation;
    private AdvanceInformation advanceInformation;
    private Map<String, ActionMessage> actions;
}