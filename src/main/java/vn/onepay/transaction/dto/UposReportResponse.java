package vn.onepay.transaction.dto;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UposReportResponse {
    private String date;
    private String merchantId;
    private String tid;
    private String paymentChannel;
    private String cardType;
    private String currency;
    private Long purchaseCount;
    private Long refundVoidCount;
    private BigDecimal totalPurchase;
    private BigDecimal totalRefundVoid;

}
