package vn.onepay.transaction.dto;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PromotionInformation {
    private String promotionCode;
    private String promotionName;
    private BigDecimal discountAmount;
    private String discountCurrency;
}
