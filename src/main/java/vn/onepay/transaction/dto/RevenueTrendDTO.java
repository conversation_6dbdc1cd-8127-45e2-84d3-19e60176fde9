package vn.onepay.transaction.dto;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RevenueTrendDTO {
    private String period; // yyyy-MM-dd, yyyy-Www, or yyyy-MM
    private BigDecimal transactionVolume;
    private Long transactionCount;
}
