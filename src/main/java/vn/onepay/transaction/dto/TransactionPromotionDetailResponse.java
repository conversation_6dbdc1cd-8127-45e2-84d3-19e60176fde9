package vn.onepay.transaction.dto;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TransactionPromotionDetailResponse {
    private String merchantId;
    private String transId;
    private String merTransactionId;
    private String createdDate;
    private BigDecimal orgAmount;
    private BigDecimal paymentAmount;
    private String promoCode;
    private String promoName;
    private String responseCode;
    private String transactionStatus;
    private String transactionType;
    private String paymentMethod;
    private String tokenNumber;
    private String deviceId;
    private String tokenExp;
    private String tokenType;
    private String cardType;
    private String cardNumber;
    private String cardExp;
    private String commercialCard;
    private String cscResultCode;
    private String address;
    private String city;
    private String state;
    private String zip;
}
