package vn.onepay.transaction.dto;

import java.sql.Timestamp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileDownloadDto {
    private String file_hash_name;
    private String file_name;
    private String conditions;
    private String user;
    private String status;
    private Timestamp create_date;
    private Timestamp update_date;
    private Timestamp expired_date;
    private String ext;
    private String file_type;
    private long file_size;
    private String file_path;
}
