package vn.onepay.transaction.dto;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RefundInfomation {
    private String refundRequestId;
    private String parentTransactionId;
    private String transactionType;
    private String paymentMethod;
    private String createdDate;
    private String createdBy;
    private BigDecimal refundAmount;
    private String currency;
    private String requestStatus;
    private String responseCode;
}
