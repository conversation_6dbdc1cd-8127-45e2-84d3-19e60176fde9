package vn.onepay.transaction.dto;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TransactionDTO {
    private String docId;
    private String merchantId;
    private String baseId;
    private String transactionId;
    private String orderReference; 
    private BigDecimal transAmount;
    private String currency;
    private String orderCreatedTime;
    private String transCreatedTime;
    private String transCompletedTime;
    private String orderSource;
    private String paymentMethod;
    private String paymentSource;
    private String cardNumber;
    private String transType;
    private String transStatus;
    private String responseCode;
    private String responseDescription;
    private String originalTransactionId;
    private String merchantTransactionRef;
    private String operatorId;
}