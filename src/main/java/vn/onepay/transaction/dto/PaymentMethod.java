package vn.onepay.transaction.dto;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PaymentMethod {

    private String cardNumber; 
    private String cardBrand;
    private String cardType;
    private String issuer;
    private String acquirer;
    private String cardExpiry;
    private String nameOnCard;
    private String commercialCard;
    private String commercialCardIndicator;
    private String cscResultCode;
    private String dialectCscResultCode;
    private String authorizationCode;

    private String appName;
    private String cardName;
    private String qrId;
    private String bankTranRef;

    private String provider;
    private BigDecimal settlementAmount;
    private String model;
    private String period;
    private String firstPaymentAmount;
    private String payLaterAmount;
}
