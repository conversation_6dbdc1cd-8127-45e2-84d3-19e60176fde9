package vn.onepay.transaction.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PaymentMethod {
    private String cardNumber;
    private String cardBrand;
    private String cardType;
    private String issuer;
    private String cardExpiry;
    private String nameOnCard;
    private String commercialCard;
    private String commercialCardIndicator;
    private String cscResultCode;
    private String dialectCscResultCode;
    private String authorizationCode;
}
