package vn.onepay.transaction.dto;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TransactionReportDTO {
    private String merchantId;
    private String date;
    private Long numPurchaseTrans;
    private Long numAuthorizeTrans;
    private Long numCaptureTrans;
    private Long numRefundTrans;
    private BigDecimal totalRevenue;
    private BigDecimal totalRefund;
}
