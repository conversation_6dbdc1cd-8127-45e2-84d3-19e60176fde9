package vn.onepay.transaction.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UposListResponse {
    private String docId;
    private String baseId;
    private String merchantId;
    private String tid;
    private String transactionId;
    private String date;
    private String orderReference;
    private String merchantTransRef;
    private String paymentChannel;
    private String cardNumber;
    private String amount;
    private String currency;
    private String transactionType;
    private String responseCode;
    private String transactionState;
    private String installmentStatus;
    private String sKey;
}
