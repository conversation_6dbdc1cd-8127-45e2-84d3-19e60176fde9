package vn.onepay.transaction.dto;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderInformation {
    private String orderReference;
    private String orderCreatedTime;
    private BigDecimal orderAmount;
    private String orderCurrency;
    private String orderSource;
}
