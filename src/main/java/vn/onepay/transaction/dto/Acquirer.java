package vn.onepay.transaction.dto;

public class Acquirer {
    private int id;
    private String name;
    private String pay_channel;
    private String group_acquirer;
    private int cutoff;
    private String gateway;

    Acquirer() {
    }

    public Acquirer(int id, String name, String pay_channel, String group_acquirer, int cutoff, String gateway) {
        this.id = id;
        this.name = name;
        this.pay_channel = pay_channel;
        this.group_acquirer = group_acquirer;
        this.cutoff = cutoff;
        this.gateway = gateway;
    }
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPay_channel() {
        return pay_channel;
    }

    public void setPay_channel(String pay_channel) {
        this.pay_channel = pay_channel;
    }

    public String getGroup_acquirer() {
        return group_acquirer;
    }

    public void setGroup_acquirer(String group_acquirer) {
        this.group_acquirer = group_acquirer;
    }

    public int getCutoff() {
        return cutoff;
    }

    public void setCutoff(int cutoff) {
        this.cutoff = cutoff;
    }

    public String getGateway() {
        return gateway;
    }

    public void setGateway(String gateway) {
        this.gateway = gateway;
    }
}
