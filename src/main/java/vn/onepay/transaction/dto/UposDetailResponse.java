package vn.onepay.transaction.dto;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
// @JsonInclude(JsonInclude.Include.NON_NULL)
public class UposDetailResponse {
    private String merchantId;
    private String tid;
    private String transactionId;
    private String date;
    private String orderReference;
    private String merchantTransRef;
    private String paymentChannel;
    private String transactionType;
    private BigDecimal purchaseAmount;
    private String responseCode;
    private String transactionState;
    private String currency;
    
    // Chỉ có ở card & installment
    private BigDecimal voidRefundAmount;
    private String approvalCode;
    private String cardNumber;
    private String cardType;
    private String issuer;
    private String cardExpiry;
    private String nameOnCard;
    private String ipAddress;
    private String ipProxy;
    private String ipCountry;
    private String binCountry;
    private String riskAssessment;

    // Chỉ có ở QR
    private BigDecimal refundAmount;
    private String appName;
    private String accountNumber;
    private String accountName;

    // Chỉ có ở installment
    private String bank;
    private String installmentPeriod;
    private BigDecimal installmentFee;
    private BigDecimal monthlyInstallmentAmount;
    private String installmentStatus;
}
