package vn.onepay.transaction.dto;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResponse<T> {
    private String status;
    private long total;
    private T data;
    private String message;

    public ApiResponse(String status, Integer total, T data) {
        this.status = status;
        this.total = total;
        this.data = data;
    }

    public static <T> ApiResponse<T> error(String message) {
        return ApiResponse.<T>builder()
                .status("ERROR")
                .message(message)
                .build();
    }

    public static <T> ApiResponse<T> success(long total, T data) {
        return ApiResponse.<T>builder()
                .status("SUCCESS")
                .total(total)
                .data(data)
                .build();
    }
}
