package vn.onepay.transaction.dto;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
// @JsonInclude(JsonInclude.Include.NON_NULL)
public class TransactionHistoryDTO {
    private String transactionId;
    private String date;
    private String transactionType;
    private BigDecimal amount;
    private String currency;
    private String status;
    private String operatorId;
    private String description;
    private String merchantTransRef;
    private String originalTransactionId;

}
