package vn.onepay.transaction.dto;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DashboardSummaryDTO {
    private String currency;
    private Long transactionCount;
    private Long refundCount;
    private BigDecimal totalRevenue;
    private BigDecimal totalRefund;
}
