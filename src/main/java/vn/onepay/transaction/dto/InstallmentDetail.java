package vn.onepay.transaction.dto;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InstallmentDetail {
    private BigDecimal priceDifference; // Chênh lệch tiền số tiền gốc và số tiền phải trả
    private String period;
    private BigDecimal amountMonthly; //Số tiền trả góp hàng tháng
}
