package vn.onepay.transaction.repository;

import java.time.LocalDate;

import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import vn.onepay.transaction.dto.DashboardSummaryDTO;
import vn.onepay.transaction.entity.TransactionReportEntity;

public interface TransactionReportRepository extends R2dbcRepository<TransactionReportEntity, String> {
    
}