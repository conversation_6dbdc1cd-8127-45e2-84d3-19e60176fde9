package vn.onepay.transaction.repository;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import vn.onepay.transaction.dto.FileDownloadDto;

@Repository
public class FileDownloadRepository {

    private static final Logger logger = LoggerFactory.getLogger(FileDownloadRepository.class);

    @Qualifier("oracleMerchantPortalJdbcTemplate")
    @Autowired
    private JdbcTemplate jdbcTemplate;

    // public void insert(FileDownloadDto fileDownloadDto) {
    //     logger.info("Start insert file download: file_hash_name={}, user={}, file_name={}",
    //         fileDownloadDto.getFile_hash_name(), fileDownloadDto.getUser(), fileDownloadDto.getFile_name());

    //     jdbcTemplate.execute((Connection con) -> {
    //         try (CallableStatement cs = con.prepareCall("{ call PKG_MERCHANTPORTAL_2.INSERT_FILE_DOWNLOAD(?,?,?,?,?,?,?,?) }")) {
    //             cs.setString(1, fileDownloadDto.getFile_hash_name());
    //             cs.setString(2, fileDownloadDto.getUser());
    //             cs.setString(3, fileDownloadDto.getFile_name());
    //             cs.setString(4, fileDownloadDto.getExt());
    //             cs.setString(5, fileDownloadDto.getFile_type());
    //             cs.setString(6, fileDownloadDto.getConditions());

    //             cs.registerOutParameter(7, Types.INTEGER); // result code
    //             cs.registerOutParameter(8, Types.VARCHAR); // message

    //             cs.execute();

    //             int resultCode = cs.getInt(7);
    //             String message = cs.getString(8);

    //             logger.info("Insert Oracle result - code: {}, message: {}", resultCode, message);

    //             return null;
    //         } catch (Exception e) {
    //             logger.error("Error inserting file download record to Oracle", e);
    //             throw e;
    //         }
    //     });

    //     logger.info("Finished insert file download");
    // }
    public void insert(FileDownloadDto fileDownloadDto) {
        logger.info("Start insert file download: file_hash_name={}, user={}, file_name={}",
                fileDownloadDto.getFile_hash_name(), fileDownloadDto.getUser(), fileDownloadDto.getFile_name());
        String sql = "INSERT INTO MERCHANTPORTAL.TB_FILE_DOWNLOAD "
                   + "(S_FILE_HASH_NAME, N_USER_ID, S_FILE_ORG_NAME, S_EXTENSION, S_TYPE, S_CONDITIONS, S_STATUS,D_EXP_DATE) "
                   + "VALUES (?, ?, ?, ?, ?, ?, ?,?)";
    
        int rows = jdbcTemplate.update(sql,
            fileDownloadDto.getFile_hash_name(),
            fileDownloadDto.getUser(),
            fileDownloadDto.getFile_name(),
            fileDownloadDto.getExt(),
            fileDownloadDto.getFile_type(),
            fileDownloadDto.getConditions(),
            fileDownloadDto.getStatus(),
            fileDownloadDto.getExpired_date()
        );
    
        if (rows > 0) {
            logger.info("Insert to TB_FILE_DOWNLOAD success for file_hash_name={}", fileDownloadDto.getFile_hash_name());
        } else {
            logger.warn("Insert to TB_FILE_DOWNLOAD failed for file_hash_name={}", fileDownloadDto.getFile_hash_name());
        }
    }
    public void updateStatus(FileDownloadDto fileDownloadDto) {
        logger.info("Updating file status for file_hash_name={}, status={}, file_size={}",
            fileDownloadDto.getFile_hash_name(), fileDownloadDto.getStatus(), fileDownloadDto.getFile_size());

        String sql = "UPDATE MERCHANTPORTAL.TB_FILE_DOWNLOAD SET S_STATUS = ?, N_FILE_SIZE = ? WHERE S_FILE_HASH_NAME = ?";
        jdbcTemplate.update(sql, fileDownloadDto.getStatus(), fileDownloadDto.getFile_size(), fileDownloadDto.getFile_hash_name());
    }
}
