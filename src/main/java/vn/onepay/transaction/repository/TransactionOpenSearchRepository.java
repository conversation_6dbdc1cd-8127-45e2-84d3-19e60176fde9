package vn.onepay.transaction.repository;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import vn.onepay.transaction.client.OpenSearchClient;

@Repository
public class TransactionOpenSearchRepository {

    @Value("${opensearch.index}")
    private String index;

    @Value("${opensearch.index.searchDocId}")
    private String indexDoc;
    
    private final OpenSearchClient client;

    public TransactionOpenSearchRepository(OpenSearchClient OpenSearchClient) {
        this.client = OpenSearchClient;
    }

    public String getTransactionList(String query) throws Exception {
        return client.executeQuery("GET", index, query);
    }

    public String getTransactionReport(String query) throws Exception {
        return client.executeQuery("GET", index+"?size=0", query);
    }

    public String getTransactionDetailByDocId(String docId) throws Exception {
        return client.executeQuery("GET", indexDoc + docId, null);
    }
}
