package vn.onepay.transaction.repository;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import vn.onepay.transaction.client.OpenSearchClient;

@Repository
public class TransactionOpenSearchRepository {

    @Value("${opensearch.index}")
    private String index;

    @Value("${opensearch.index.searchDocId}")
    private String indexDoc;
    
    private final OpenSearchClient client;

    public TransactionOpenSearchRepository(OpenSearchClient OpenSearchClient) {
        this.client = OpenSearchClient;
    }

    public String getTransactionList(String query) throws Exception {
        return client.executeQuery("GET", index, query);
    }

    public String getTransactionReport(String query) throws Exception {
        return client.executeQuery("GET", index+"?size=0", query);
    }

    public String getTransactionDetailByDocId(String docId) throws Exception {
        return client.executeQuery("GET", indexDoc + docId, null);
    }

    /**
     * Insert new document into OpenSearch
     * @param docId Document ID (optional, if null OpenSearch will auto-generate)
     * @param jsonData JSON data to insert
     * @return Response from OpenSearch
     * @throws Exception
     */
    public String insertTransaction(String docId, String jsonData) throws Exception {
        String endpoint = docId != null ? indexDoc + docId : index.replace("/_search", "/_doc");
        return client.executeQuery("POST", endpoint, jsonData);
    }

    /**
     * Update existing document in OpenSearch
     * @param docId Document ID to update
     * @param jsonData JSON data for update (should be wrapped in "doc" object)
     * @return Response from OpenSearch
     * @throws Exception
     */
    public String updateTransaction(String docId, String jsonData) throws Exception {
        String endpoint = indexDoc + docId + "/_update";
        return client.executeQuery("POST", endpoint, jsonData);
    }

    /**
     * Upsert document (insert if not exists, update if exists)
     * @param docId Document ID
     * @param jsonData JSON data for upsert
     * @return Response from OpenSearch
     * @throws Exception
     */
    public String upsertTransaction(String docId, String jsonData) throws Exception {
        String endpoint = indexDoc + docId;
        return client.executeQuery("PUT", endpoint, jsonData);
    }

    /**
     * Delete document from OpenSearch
     * @param docId Document ID to delete
     * @return Response from OpenSearch
     * @throws Exception
     */
    public String deleteTransaction(String docId) throws Exception {
        String endpoint = indexDoc + docId;
        return client.executeQuery("DELETE", endpoint, null);
    }

    /**
     * Bulk operations (insert/update/delete multiple documents)
     * @param bulkData Bulk operation data in NDJSON format
     * @return Response from OpenSearch
     * @throws Exception
     */
    public String bulkOperations(String bulkData) throws Exception {
        String endpoint = "/_bulk";
        return client.executeQuery("POST", endpoint, bulkData);
    }
}
