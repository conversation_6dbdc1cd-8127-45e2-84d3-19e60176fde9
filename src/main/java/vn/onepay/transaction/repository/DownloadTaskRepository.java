package vn.onepay.transaction.repository;

import java.util.UUID;

import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;

import reactor.core.publisher.Flux;
import vn.onepay.transaction.entity.DownloadTaskEntity;

@Repository
public interface DownloadTaskRepository extends ReactiveCrudRepository<DownloadTaskEntity, UUID> {

    Flux<DownloadTaskEntity> findByStatus(String status);
}
