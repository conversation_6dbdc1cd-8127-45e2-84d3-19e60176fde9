package vn.onepay.transaction.repository;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import vn.onepay.transaction.dto.AcquirerDTO;
import vn.onepay.transaction.dto.Acquirer;

@Repository
public class AcquirerRepository {
    private static final Logger logger = LoggerFactory.getLogger(AcquirerRepository.class);

    @Autowired
    @Qualifier("oracleJdbcTemplate")
    private JdbcTemplate oracleJdbcTemplate;

    public AcquirerDTO getListAcquirer() {
        logger.info("Starting getListAcquirer - calling function ONEDATA.GET_LIST_ACQUIRER");

        try {
            return oracleJdbcTemplate.execute((Connection con) -> {
                CallableStatement cs = null;
                ResultSet rs = null;
                try {
                    cs = con.prepareCall("{? = call ONEDATA.GET_LIST_ACQUIRER()}");
                    cs.registerOutParameter(1, Types.REF_CURSOR);  // Function returns SYS_REFCURSOR
                    cs.execute();

                    rs = (ResultSet) cs.getObject(1);
                    List<Acquirer> list = new ArrayList<>();

                    while (rs.next()) {
                        Acquirer acquirer = new Acquirer(
                                rs.getInt("N_ID"),
                                rs.getString("S_NAME"),
                                rs.getString("S_PAY_CHANNEL"),      // pay_channel in DTO
                                rs.getString("S_GROUP_ACQUIRER"),   // group_acquirer in DTO
                                rs.getInt("N_CUTOFF"),
                                rs.getString("S_GATEWAY")
                        );
                        list.add(acquirer);
                    }

                    AcquirerDTO acquirerDTO = new AcquirerDTO();
                    acquirerDTO.setList(list);

                    return acquirerDTO;

                } catch (SQLException e) {
                    logger.error("SQL error in getListAcquirer: {}", e.getMessage(), e);
                    throw new RuntimeException("Failed to execute function GET_LIST_ACQUIRER", e);
                } finally {
                    // Close resources in reverse order
                    if (rs != null) {
                        try {
                            rs.close();
                            logger.debug("ResultSet closed");
                        } catch (SQLException e) {
                            logger.warn("Error closing ResultSet: {}", e.getMessage());
                        }
                    }
                    if (cs != null) {
                        try {
                            cs.close();
                            logger.debug("CallableStatement closed");
                        } catch (SQLException e) {
                            logger.warn("Error closing CallableStatement: {}", e.getMessage());
                        }
                    }
                }
            });
        } catch (Exception e) {
            logger.error("Unexpected error in getListAcquirer: {}", e.getMessage(), e);

            // Return empty result instead of null
            AcquirerDTO errorResult = new AcquirerDTO();
            errorResult.setList(new ArrayList<>());

            return errorResult;
        }
    }
}
