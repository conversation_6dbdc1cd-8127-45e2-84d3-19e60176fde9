package vn.onepay.transaction.repository;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import vn.onepay.transaction.dto.AcquirerDTO;

@Repository
public class AcquirerRepository {
    private static final Logger logger = LoggerFactory.getLogger(AcquirerRepository.class);

    @Autowired
    @Qualifier("oracleJdbcTemplate")
    private JdbcTemplate oracleJdbcTemplate;

    public AcquirerDTO getListAcquirer() {
        try {
            return oracleJdbcTemplate.execute(null, (Connection con) -> {
                CallableStatement cs = null;
                ResultSet rs = null;
                try {
                    cs = con.prepareCall("{call get_list_acquirer(?, ?)}");
                    cs.registerOutParameter(1, Types.REF_CURSOR);
                    cs.registerOutParameter(2, Types.VARCHAR);

                    cs.execute();

                    rs = (ResultSet) cs.getObject(1);
                    String message = cs.getString(2);

                    List<Acquirer> list = new ArrayList<>();
                    while (rs.next()) {
                        list.add(new Acquirer(
                                rs.getInt("ID"),
                                rs.getString("NAME"),
                                rs.getString("PAY_CHANNEL"),
                                rs.getString("GROUP_ACQUIRER"),
                                rs.getInt("CUTOFF"),
                                rs.getString("GATEWAY")
                        ));
                    }

                    AcquirerDTO acquirerDTO = new AcquirerDTO();
                    acquirerDTO.setList(list);

                    return acquirerDTO;
                } finally {
                    if (rs != null) try { rs.close(); } catch (SQLException ignored) {}
                    if (cs != null) try { cs.close(); } catch (SQLException ignored) {}
                }
            });
        } catch (Exception e) {
            // TODO: handle exception
        }
    }
}
