package vn.onepay.transaction.repository;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import vn.onepay.transaction.dto.AcquirerDTO;
import vn.onepay.transaction.dto.Acquirer;

@Repository
public class AcquirerRepository {
    private static final Logger logger = LoggerFactory.getLogger(AcquirerRepository.class);

    @Autowired
    @Qualifier("oracleJdbcTemplate")
    private JdbcTemplate oracleJdbcTemplate;

    public AcquirerDTO getListAcquirer() {
        logger.info("Starting getListAcquirer - calling stored procedure ONEDATA.GET_LIST_ACQUIRER");

        try {
            return oracleJdbcTemplate.execute((Connection con) -> {
                CallableStatement cs = null;
                ResultSet rs = null;
                try {
                    logger.debug("Preparing call to stored procedure: {call ONEDATA.GET_LIST_ACQUIRER()}");
                    cs = con.prepareCall("{call ONEDATA.GET_LIST_ACQUIRER()}");

                    // Register output parameters
                    cs.registerOutParameter(1, Types.REF_CURSOR);  // Cursor for result set
                    cs.registerOutParameter(2, Types.VARCHAR);     // Message/status

                    logger.debug("Executing stored procedure...");
                    cs.execute();

                    // Get results
                    rs = (ResultSet) cs.getObject(1);
                    String message = cs.getString(2);
                    logger.info("Stored procedure executed successfully. Message: {}", message);

                    List<Acquirer> list = new ArrayList<>();
                    int count = 0;

                    while (rs.next()) {
                        Acquirer acquirer = new Acquirer(
                                rs.getInt("ID"),
                                rs.getString("NAME"),
                                rs.getString("PAY_CHANNEL"),      // pay_channel in DTO
                                rs.getString("GROUP_ACQUIRER"),   // group_acquirer in DTO
                                rs.getInt("CUTOFF"),
                                rs.getString("GATEWAY")
                        );
                        list.add(acquirer);
                        count++;

                        if (logger.isDebugEnabled()) {
                            logger.debug("Added acquirer: ID={}, NAME={}, PAY_CHANNEL={}",
                                    acquirer.getId(), acquirer.getName(), acquirer.getPay_channel());
                        }
                    }

                    logger.info("Retrieved {} acquirer records. Message: {}", count, message);

                    AcquirerDTO acquirerDTO = new AcquirerDTO();
                    acquirerDTO.setList(list);

                    return acquirerDTO;

                } catch (SQLException e) {
                    logger.error("SQL error in getListAcquirer: {}", e.getMessage(), e);
                    throw new RuntimeException("Failed to execute stored procedure GET_LIST_ACQUIRER", e);
                } finally {
                    // Close resources in reverse order
                    if (rs != null) {
                        try {
                            rs.close();
                            logger.debug("ResultSet closed");
                        } catch (SQLException e) {
                            logger.warn("Error closing ResultSet: {}", e.getMessage());
                        }
                    }
                    if (cs != null) {
                        try {
                            cs.close();
                            logger.debug("CallableStatement closed");
                        } catch (SQLException e) {
                            logger.warn("Error closing CallableStatement: {}", e.getMessage());
                        }
                    }
                }
            });
        } catch (Exception e) {
            logger.error("Unexpected error in getListAcquirer: {}", e.getMessage(), e);

            // Return empty result instead of null
            AcquirerDTO errorResult = new AcquirerDTO();
            errorResult.setList(new ArrayList<>());
            errorResult.setMessage("Error: " + e.getMessage());
            errorResult.setTotal(0);

            return errorResult;
        }
    }
}
